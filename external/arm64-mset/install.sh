#!/bin/bash

# Get the directory where this script is located
BIN_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "BIN_DIR : $BIN_DIR"
MSET_EXE="$BIN_DIR/mset"

CUSTOM_HOME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --home)
            CUSTOM_HOME="$2"
            shift 2
            ;;
        *)
            shift
            ;;
    esac
done

echo "========================================"
echo "MSET Auto Install Script"
echo "========================================"
echo

echo "Installing default versions:"
echo "- Node.js v22.16.0"
echo "- UV v0.7.11"
echo

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Set MSET_HOME based on command line argument or default to parent directory of bin
if [[ -n "$CUSTOM_HOME" ]]; then
    MSET_HOME="$CUSTOM_HOME"
else
    MSET_HOME="$(dirname "$SCRIPT_DIR")"
fi

echo "Setting MSET_HOME=$MSET_HOME"

# Check if mset executable exists
if [[ ! -f "$MSET_EXE" ]]; then
    echo "Error: mset executable not found in the script directory"
    echo "Please ensure mset is in the same directory as this script: $BIN_DIR"
    read -p "Press any key to continue..."
    exit 1
fi

# Make sure mset is executable
chmod +x "$MSET_EXE"

# Export MSET_HOME for the installation process
export MSET_HOME="$MSET_HOME"

echo "Installing Node.js v22.16.0..."
if "$MSET_EXE" --install node=22.16.0; then
    echo "Node.js v22.16.0 installed successfully"
else
    echo "Warning: Node.js installation failed"
fi
echo


echo "Installing UV v0.7.11..."
if "$MSET_EXE" --install uv=0.7.11; then
    echo "UV v0.7.11 installed successfully"
else
    echo "Warning: UV installation failed"
fi
echo

echo "========================================"
echo "Installation Complete!"
echo "========================================"
echo
echo "Installed versions:"
echo "- Node.js v22.16.0"
echo "- UV v0.7.11"
echo

echo "Setting environment variables..."

# Set MSET_HOME environment variable
echo "Setting MSET_HOME environment variable to $MSET_HOME"

# Determine the shell configuration file
if [[ "$SHELL" == */zsh ]]; then
    SHELL_CONFIG="$HOME/.zshrc"
elif [[ "$SHELL" == */bash ]]; then
    SHELL_CONFIG="$HOME/.bash_profile"
else
    SHELL_CONFIG="$HOME/.profile"
fi

echo "Using shell configuration file: $SHELL_CONFIG"

# Create backup of shell config if it exists
if [[ -f "$SHELL_CONFIG" ]]; then
    cp "$SHELL_CONFIG" "${SHELL_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Remove existing MSET entries to avoid duplicates
if [[ -f "$SHELL_CONFIG" ]]; then
    # Create temporary file without MSET entries
    grep -v -e "export MSET_HOME=" -e "# MSET Environment Variables" -e "# MSET PATH" "$SHELL_CONFIG" > "${SHELL_CONFIG}.tmp"
    mv "${SHELL_CONFIG}.tmp" "$SHELL_CONFIG"
fi

# Add MSET_HOME environment variable
echo "" >> "$SHELL_CONFIG"
echo "# MSET Environment Variables" >> "$SHELL_CONFIG"
echo "export MSET_HOME=\"$MSET_HOME\"" >> "$SHELL_CONFIG"

echo "Adding $BIN_DIR to PATH environment variable..."
echo "export PATH=\"$BIN_DIR:\$PATH\" # MSET PATH" >> "$SHELL_CONFIG"
echo "Successfully added $BIN_DIR to PATH environment variable"

# Set PATH temporarily for current session
export PATH="$BIN_DIR:$PATH"
echo "Current session PATH has been updated temporarily"

echo
echo "Environment variables have been added to $SHELL_CONFIG"
echo "Please restart your terminal or run 'source $SHELL_CONFIG' to use the new tools."
echo