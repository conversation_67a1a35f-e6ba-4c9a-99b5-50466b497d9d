import { ipc<PERSON>ender<PERSON> } from 'electron';

export const installUv = () => {
  return ipcRenderer.invoke('install-uv');
}

export const installNodejs = () => {
  return ipcRenderer.invoke('install-nodejs');
}

export const installMset = () => {
  return ipcRenderer.invoke('install-mset');
}

export const installBun = () => {
  return ipcRenderer.invoke('install-bun');
}

export const getUvStatus = () => {
  return ipcRenderer.invoke('get-uv-status');
}

export const getMsetStatus = () => {
  return ipcRenderer.invoke('get-mset-status');
}

export const getMsetPath = () => {
  return ipcRenderer.invoke('get-mset-path');
}

export const getBunStatus = () => {
  return ipcRenderer.invoke('get-bun-status');
}

export const getUvPath = () => {
  return ipcRenderer.invoke('get-uv-path');
}

export const getBunPath = () => {
  return ipcRenderer.invoke('get-bun-path');
}

export const getNodejsPath = () => {
  return ipcRenderer.invoke('get-nodejs-path');
}

export const getNodejsStatus = () => {
  return ipcRenderer.invoke('get-nodejs-status');
}