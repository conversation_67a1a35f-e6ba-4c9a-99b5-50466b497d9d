import { ipc<PERSON>enderer } from 'electron';

/**
 * 上传附件
 * @returns 附件上传结果
 */
export const uploadAttachment = async () => {
  return await ipcRenderer.invoke('upload-attachment');
};

/**
 * 从剪贴板上传附件
 * @param fileData 文件数据
 * @returns 附件上传结果
 */
export const uploadAttachmentFromClipboard = async (fileData: { buffer: number[], originalName: string, mimeType: string, size: number }) => {
  return await ipcRenderer.invoke('upload-attachment-from-clipboard', fileData);
};

/**
 * 获取附件目录路径
 * @returns 附件目录路径
 */
export const getAttachmentsDirectory = async () => {
  return await ipcRenderer.invoke('get-attachments-directory');
};

/**
 * 删除附件
 * @param fileName 文件名
 * @returns 删除结果
 */
export const deleteAttachment = async (fileName: string) => {
  return await ipcRenderer.invoke('delete-attachment', fileName);
};

/**
 * 读取附件文件内容
 * @param fileName 文件名
 * @returns 文件内容（base64格式）
 */
export const readAttachment = async (fileName: string) => {
  return await ipcRenderer.invoke('read-attachment', fileName);
};

/**
 * 解析文档内容
 * @param fileName 文件名
 * @returns 解析后的文档内容
 */
export const parseDocument = async (fileName: string) => {
  return await ipcRenderer.invoke('parse-document', fileName);
};