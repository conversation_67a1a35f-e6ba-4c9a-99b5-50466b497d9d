import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import { Provider } from '../../node/db/provider';

const providerFnMap = new Map<string, () => void>();

export const getModelProviderList = () => {
  return ipcRenderer.invoke('get-model-provider-list');
}


export const getAllModelList = () => {
  return ipcRenderer.invoke('get-all-model-list');
};

export const enabledModel = (id: number, enabled: boolean) => {
  return ipcRenderer.invoke('enabled-model', {
    id,
    enabled
  });
};

export const setDefaultModel = (id: number) => {
  return ipcRenderer.invoke('set-default-model', {
    id
  });
};
export const getModelListByProvider = (provider: string) => {
  return ipcRenderer.invoke('get-model-list-by-provider', {
    provider
  });
};

export const saveProviderStatus = (provider: string, status: number) => {
  return ipcRenderer.invoke('save-provider-status', {
    provider,
    status
  });
};

export const getEnabledModelList = () => {
  return ipcRenderer.invoke('get-enabled-model-list');
};

export const saveProviderCurrentAPIKey = (provider: string, currentAPIKey: string) => {
  return ipcRenderer.invoke('save-provider-current-api-key', {
    provider,
    currentAPIKey
  });
};

export const verifyModelProvider = (provider: string) => {
  return ipcRenderer.invoke('verify-model-provider', {
    provider
  });
};

export const resetDefaultBaseModelUrl = (provider: string) => {
  return ipcRenderer.invoke('reset-default-base-model-url', {
    provider
  });
};

export const updateProvider = (provider: string, updates: Partial<Provider>) => {
  return ipcRenderer.invoke('update-provider', {
    provider,
    updates
  });
};


export const getDefaultApiKey = () => {
  return ipcRenderer.invoke('get-default-api-key');
};


export const onUpdateProviderApiKey = (key: string, callback: () => void) => {
  const fn = () => {
    callback();
  }
  ipcRenderer.on('update-provider-api-key', fn);
  providerFnMap.set(key, fn);
};

export const offUpdateProviderApiKey = (key: string) => {
  ipcRenderer.removeListener('update-provider-api-key', providerFnMap.get(key) as any);
};