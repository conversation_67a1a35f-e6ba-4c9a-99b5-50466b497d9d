import { ipc<PERSON>enderer, IpcRendererEvent } from 'electron';
import { type User } from '../../node/db/user';
import { type Qrcode } from '../../node/cas';

const logoutFnMap = new Map<string, (event: IpcRendererEvent, data: any) => void>();
export const getUser = async (): Promise<User> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('get-user');
    resolve(data);
  });
};



export const onLogout = async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
  // 如果已经存在该key的监听器，先移除
  const existingFn = logoutFnMap.get(key);
  if (existingFn) {
    ipcRenderer.off('logout', existingFn);
    logoutFnMap.delete(key);
  }

  const fn = (event: IpcRendererEvent, data: any) => {
    // console.log('onMessage', event, data);
    callback(event, data);
  }

  // 保存函数引用到Map中
  logoutFnMap.set(key, fn);
  ipcRenderer.on('logout', fn);
}

export const offLogout = async (key: string) => {
  const fn = logoutFnMap.get(key);
  if (fn) {
    ipcRenderer.off('logout', fn);
    logoutFnMap.delete(key);
  }
}

export const saveUser = async (user: User) => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('save-user', user);
    resolve(data);
  });
};


export const getQrCode = async (): Promise<Qrcode> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('get-qr-code');
    resolve(data);
  });
};

export const getQrCodeStatus = async (): Promise<boolean> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('get-qr-code-status');
    resolve(data);
  });
};


export const checkToken = async (): Promise<boolean> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('check-token');
    resolve(data);
  });
};

export const logout = async (): Promise<boolean> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('logout');
    resolve(data);
  });
};



export const checkLatestVersion = async (): Promise<{
  version: string;
  sys: string;
  url: string;
} | false> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('check-latest-version');
    resolve(data);
  });
};


export const getAppVersion = async (): Promise<string> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('get-app-version');
    resolve(data);
  });
};


export const deleteUser = async (): Promise<boolean> => {
  return new Promise(async (resolve) => {
    const data = await ipcRenderer.invoke('delete-user');
    resolve(data);
  });
};
