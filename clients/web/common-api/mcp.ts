import { ipc<PERSON>enderer } from 'electron';

/**
 * 获取mcp 服务器列表
 */
export const getMcpList = (name?: string) => {
  return ipcRenderer.invoke('get-mcp-list', { name });
}

/**
 * 启动mcp 服务器
 */
export const startMcp = (id: number) => {
  return ipcRenderer.invoke('start-mcp', {
    id
  });
}

/**
 * 停止mcp 服务器
 */
export const stopMcp = (id: number) => {
  return ipcRenderer.invoke('stop-mcp', {
    id
  });
}

/**
 * 更新mcp 服务器
 */
export const saveMcp = ({
  id,
  serverConfig,
  serverDesc,
  serverName,
  serverLogo,
  serverFromObj,
}: {
  id?: string,
  serverConfig?: string,
  serverDesc?: string,
  serverName?: string,
  serverLogo?: string,
  serverFromObj?: string,
}) => {
  return ipcRenderer.invoke('save-mcp', {
    id,
    serverConfig,
    serverDesc,
    serverName,
    serverLogo,
    serverFromObj,
  });
}

/**
 * 删除mcp 服务器
 */
export const deleteMcp = (id: string) => {
  return ipcRenderer.invoke('delete-mcp', {
    id
  });
}


/**
 * 获取工具列表
 */
export const getToolList = () => {
  return ipcRenderer.invoke('get-tool-list');
}


/**
 * 获取mcp工具列表
 */
export const getToolListByMcp = (id: number) => {
  return ipcRenderer.invoke('get-tool-list-by-mcp', { id });
}

/**
 * 获取mcp市场列表
 */
export const getMcpMarketList = (pageNum: number, pageSize: number, name: string) => {
  return ipcRenderer.invoke('get-mcp-market-list', { pageNum, pageSize, name });
}



export const enabledTool = (id: number, toolName: string) => {
  return ipcRenderer.invoke('enabled-tool', { id, toolName });
}


export const disabledTool = (id: number, toolName: string) => {
  return ipcRenderer.invoke('disabled-tool', { id, toolName });
}