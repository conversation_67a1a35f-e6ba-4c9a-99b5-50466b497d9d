import { exec } from 'child_process';
import * as os from 'os';
import * as fs from 'fs';

// 检查命令是否存在的函数
export function checkCommand(command: string): Promise<boolean> {
  const platform = os.platform();
  // Windows 使用 where，其他系统使用 which
  const checkCmd = platform === 'win32' ? `where ${command}` : `which ${command}`;

  return new Promise((resolve) => {
    exec(checkCmd, (error) => {
      if (error) {
        // 命令不存在
        resolve(false);
      } else {
        // 命令存在
        resolve(true);
      }
    });
  });
}


// 检测用户是否含有D盘或者E盘
export function checkDisk(): Promise<{
  D: boolean;
  E: boolean;
}> {
  const platform = os.platform();

  return new Promise((resolve) => {
    if (platform === 'win32') {
      // Windows系统检测D盘和E盘
      const D_DIR = 'D:\\';
      const E_DIR = 'E:\\';
      resolve({
        D: fs.existsSync(D_DIR),
        E: fs.existsSync(E_DIR),
      });
    } else {
      // 非Windows系统没有盘符概念
      resolve({
        D: false,
        E: false,
      });
    }
  });
}