import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StdioClientTransport, getDefaultEnvironment } from '@modelcontextprotocol/sdk/client/stdio.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { Tool } from '@modelcontextprotocol/sdk/types';
import logger from '../utils/logger';
import fs from 'fs';
import os from 'os';
import path from 'path';
import memoize from 'lodash/memoize';
import { isBinaryExists, getBinaryPath, getBinaryName, isMsetExists, getMsetBinary, getMsetHomePath } from '../utils/process';
import { isMac, isWin, isLinux } from '../utils/constant';
import { checkCommand, checkDisk } from './check-commands';
export class MCPClient {
  public mcp: Client;
  private transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport | null = null;
  private cmd: string;
  private args: string[];
  private tools: Tool[] = [];
  private env: Record<string, string>;
  private serverUrl: string;
  private requestHeaders: Record<string, string>;
  private serverType: 'stdio' | 'sse' | 'streamable_http';
  public name: string;
  public mcpName: string;
  constructor({
    name,
    version,
    cmd,
    args,
    env,
    serverUrl,
    requestHeaders,
    serverType,
    mcpName,
  }: {
    name: string;
    version: string;
    cmd: string;
    args: string[];
    env: Record<string, string>;
    serverUrl: string;
    requestHeaders: Record<string, string>;
    serverType: 'stdio' | 'sse' | 'streamable_http';
    mcpName: string;
  }) {
    this.mcp = new Client({
      name,
      version
    });
    this.cmd = cmd;
    this.args = args;
    this.env = env || {};
    this.name = name;
    this.serverUrl = serverUrl;
    this.requestHeaders = requestHeaders;
    this.serverType = serverType;
    this.mcpName = mcpName;
  }

  init = async (): Promise<{
    success: boolean;
    error: string | null;
  }> => {
    return await this.connect();
  };

  getInstallInfo = async (): Promise<{
    dir: string;
    uvPath: string;
    bunPath: string;
  }> => {
    const studioName = process.env.STUDIO_NAME;
    const dir = path.join(os.homedir(), `.${studioName}`, 'bin')
    const uvName = await getBinaryName('uv');
    const bunName = await getBinaryName('bun');
    const uvPath = path.join(dir, uvName);
    const bunPath = path.join(dir, bunName);
    return {
      dir,
      uvPath,
      bunPath,
    }
  };

  getTools = (): Tool[] => {
    // const toolResult = await this.mcp.listTools();
    return this.tools.map(item => {
      return {
        ...item,
      }
    });
  }

  connectStdio = async (): Promise<{
    success: boolean;
    error: string | null;
  }> => {
    try {
      const env = {
        ...(this.env || {}),
      }
      const useBun = false;
      if (this.cmd === 'npx') {
        const hasNpx = false;
        if (!hasNpx) {
          const bun = await isBinaryExists('bun');
          const npx = false;
          const mset = await isMsetExists();
          if (bun && useBun) {
            this.cmd = await getBinaryPath('bun');
          } else if (npx) {

            const npxPath = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'nodejs', 'lib', 'node_modules', 'npm', 'bin', 'npx-cli.js');
            this.cmd = await getBinaryPath('node');
            this.args.unshift(npxPath);
          } else if (mset) {
            this.cmd = await getMsetBinary();
            this.args.unshift('npx');
            if (isMac) {
              fs.chmodSync(this.cmd, '755')
            }
            // fs.chmodSync(this.cmd, '755')
            env['MSET_HOME'] = await getMsetHomePath();
          }
          if (this.args && this.args.length > 0) {
            if (!this.args.includes('x') && useBun) {
              this.args.unshift('x')
            }
          }
          if (isWin) {
            const disk = await checkDisk();
            if (disk.D) {
              env['TEMP'] = 'D:\\.npm_temp';
            }
            if (disk.E && !disk.D) {
              env['TEMP'] = 'E:\\.npm_temp';
            }
          } else {
            if (useBun) {
              env['TMPDIR'] = path.join(process.env.HOME || process.env.USERPROFILE || '', `.${process.env.STUDIO_NAME}`, '.bun_temp') as string;
            } else {
              env['TMPDIR'] = path.join(process.env.HOME || process.env.USERPROFILE || '', `.${process.env.STUDIO_NAME}`, '.npm_temp') as string;
            }
          }
          if (!env['NPM_CONFIG_REGISTRY']) {
            env['NPM_CONFIG_REGISTRY'] = 'https://artifactory.sf-express.com/artifactory/api/npm/npm/';
          }
          if (!env['NPM_CONFIG_CACHE']) {
            env['NPM_CONFIG_CACHE'] = path.join(process.env.HOME || process.env.USERPROFILE || '', `.${process.env.STUDIO_NAME}`, '.npm_cache') as string;
            const disk = await checkDisk();
            if (disk.D) {
              env['NPM_CONFIG_CACHE'] = path.join('D:', '.npm_cache') as string;
            }
            if (disk.E && !disk.D) {
              env['NPM_CONFIG_CACHE'] = path.join('E:', '.npm_cache') as string;
            }
          }
          if (!env['BUN_INSTALL_CACHE_DIR']) {
            const defaultBUNInstallCacheDir = path.join(process.env.HOME || process.env.USERPROFILE || '', `.${process.env.STUDIO_NAME}`, '.bun_cache') as string;
            env['BUN_INSTALL_CACHE_DIR'] = defaultBUNInstallCacheDir;
            const disk = await checkDisk();
            if (disk.D) {
              env['BUN_INSTALL_CACHE_DIR'] = path.join('D:', '.bun_cache') as string;
            }
            if (disk.E && !disk.D) {
              env['BUN_INSTALL_CACHE_DIR'] = path.join('E:', '.bun_cache') as string;
            }
          }
        }
      }

      if (this.cmd === 'uvx' || this.cmd === 'uv') {
        // const hasUvx = await checkCommand(this.cmd);
        let cmd = this.cmd;

        const mset = await getMsetBinary();
        if (isMac) {
          fs.chmodSync(mset, '755')
        }
        if (mset) {
          this.args.unshift(cmd);
          this.cmd = mset;
        }
        if (!env['UV_DEFAULT_INDEX']) {
          env['UV_DEFAULT_INDEX'] = 'https://artifactory.sf-express.com/artifactory/api/pypi/pip/simple';
        }
        if (!env['UV_INDEX_URL']) {
          env['UV_INDEX_URL'] = 'https://artifactory.sf-express.com/artifactory/api/pypi/pip/simple';
        }
        if (!env['PIP_INDEX_URL']) {
          env['PIP_INDEX_URL'] = 'https://artifactory.sf-express.com/artifactory/api/pypi/pip/simple';
        }
        const defaultUvCacheDir = 'uv_cache';
        const disk = await checkDisk();
        env['UV_CACHE_DIR'] = path.join(process.env.HOME || process.env.USERPROFILE || '', `.${process.env.STUDIO_NAME}`, defaultUvCacheDir) as string;
        if (disk.D) {
          env['UV_CACHE_DIR'] = path.join('D:', defaultUvCacheDir) as string;
        }
        if (disk.E && !disk.D) {
          env['UV_CACHE_DIR'] = path.join('E:', defaultUvCacheDir) as string;
        }
        // console.log('this.cmd', this.cmd);
      }

      const enhancedPath = await this.getEnhancedPath(process.env.PATH || '');
      logger.info('this.cmd', this.cmd);
      logger.info('this.args', this.args);
      logger.info('this.env', {
        ...getDefaultEnvironment(),
        ...env,
        PATH: enhancedPath,
      });
      logger.info('process.env.PATH', enhancedPath);

      this.transport = new StdioClientTransport({
        command: this.cmd,
        args: this.args,
        env: {
          ...getDefaultEnvironment(),
          PATH: enhancedPath,
          ...env,
          // 如果是uvx 或者python 需要制定INDEX_URL
          // 如果是npx 需要制定registey
          MSET_HOME: await getMsetHomePath(),
        },
        stderr: 'pipe',

      });

      this.transport.stderr?.on('data', (data: Buffer) => {
        logger.info(data.toString());
      });
      // console.log(getDefaultEnvironment());
      await this.mcp.connect(this.transport, {
        // maxTotalTimeout: 3 * 60 * 1000,
        timeout: 3 * 60 * 1000,
      });
      const toolResult = await this.mcp.listTools();
      // logger.info('toolResult', toolResult);
      this.tools = toolResult.tools.map((tool) => {
        return {
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema,
        }
      });

      return {
        success: true,
        error: null,
      };
    } catch (e: any) {
      logger.error('e', e);
      return {
        success: false,
        error: e.toString(),
      };
    }
  }

  connectSSE = async (): Promise<{
    success: boolean;
    error: string | null;
  }> => {
    try {
      this.transport = new SSEClientTransport(new URL(this.serverUrl), {
        requestInit: {
          headers: this.requestHeaders,
        }
      });
      await this.mcp.connect(this.transport, {

      });
      const toolResult = await this.mcp.listTools();
      // logger.info('toolResult', toolResult);
      this.tools = toolResult.tools.map((tool) => {
        return {
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema,
        }
      });
      return {
        success: true,
        error: null,
      };
    } catch (e: any) {
      logger.error('e', e);
      return {
        success: false,
        error: e.toString(),
      };
    }
  }

  connectStreamableHTTP = async (): Promise<{
    success: boolean;
    error: string | null;
  }> => {
    try {
      this.transport = new StreamableHTTPClientTransport(new URL(this.serverUrl), {
        requestInit: {
          headers: this.requestHeaders,
        },
      });
      await this.mcp.connect(this.transport);
      const toolResult = await this.mcp.listTools();
      // logger.info('toolResult', toolResult);
      this.tools = toolResult.tools.map((tool) => {
        return {
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema,
        }
      });
      return {
        success: true,
        error: null,
      };
    } catch (e: any) {
      logger.error('e', e);
      return {
        success: false,
        error: e.toString(),
      };
    }
  }

  connect = async (): Promise<{
    success: boolean;
    error: string | null;
  }> => {
    if (this.serverType === 'stdio') {
      return await this.connectStdio();
    }
    if (this.serverType === 'sse') {
      return await this.connectSSE();
    }
    if (this.serverType === 'streamable_http') {
      return await this.connectStreamableHTTP();
    }
    return {
      success: false,
      error: 'server type not supported',
    };
  };

  private getSystemPath = memoize(async (): Promise<string> => {
    return new Promise((resolve, reject) => {
      let command: string
      let shell: string

      if (process.platform === 'win32') {
        shell = 'powershell.exe'
        command = '$env:PATH'
      } else {
        // 尝试获取当前用户的默认 shell
        let userShell = process.env.SHELL
        if (!userShell) {
          if (fs.existsSync('/bin/zsh')) {
            userShell = '/bin/zsh'
          } else if (fs.existsSync('/bin/bash')) {
            userShell = '/bin/bash'
          } else if (fs.existsSync('/bin/fish')) {
            userShell = '/bin/fish'
          } else {
            userShell = '/bin/sh'
          }
        }
        // 这里如何以管理员方式启动
        shell = userShell

        // 根据不同的 shell 构建不同的命令
        if (userShell.includes('zsh')) {
          command =
            'source /etc/zshenv 2>/dev/null || true; source ~/.zshenv 2>/dev/null || true; source /etc/zprofile 2>/dev/null || true; source ~/.zprofile 2>/dev/null || true; source /etc/zshrc 2>/dev/null || true; source ~/.zshrc 2>/dev/null || true; source /etc/zlogin 2>/dev/null || true; source ~/.zlogin 2>/dev/null || true; echo $PATH'
        } else if (userShell.includes('bash')) {
          command =
            'source /etc/profile 2>/dev/null || true; source ~/.bash_profile 2>/dev/null || true; source ~/.bash_login 2>/dev/null || true; source ~/.profile 2>/dev/null || true; source ~/.bashrc 2>/dev/null || true; echo $PATH'
        } else if (userShell.includes('fish')) {
          command =
            'source /etc/fish/config.fish 2>/dev/null || true; source ~/.config/fish/config.fish 2>/dev/null || true; source ~/.config/fish/config.local.fish 2>/dev/null || true; echo $PATH'
        } else {
          // 默认使用 zsh
          shell = '/bin/zsh'
          command =
            'source /etc/zshenv 2>/dev/null || true; source ~/.zshenv 2>/dev/null || true; source /etc/zprofile 2>/dev/null || true; source ~/.zprofile 2>/dev/null || true; source /etc/zshrc 2>/dev/null || true; source ~/.zshrc 2>/dev/null || true; source /etc/zlogin 2>/dev/null || true; source ~/.zlogin 2>/dev/null || true; echo $PATH'
        }
      }

      logger.info(`Using shell: ${shell} with command: ${command}`)
      const child = require('child_process').spawn(shell, ['-c', command], {
        env: { ...process.env },
        cwd: os.homedir()
      })

      let path = ''
      child.stdout.on('data', (data: Buffer) => {
        path += data.toString()
      })

      child.stderr.on('data', (data: Buffer) => {
        logger.error('Error getting PATH:', data.toString())
      })

      child.on('close', (code: number) => {
        if (code === 0) {
          const trimmedPath = path.trim()
          resolve(trimmedPath)
        } else {
          reject(new Error(`Failed to get system PATH, exit code: ${code}`))
        }
      })
    })
  });

  private async getEnhancedPath(originalPath: string): Promise<string> {
    let systemPath = ''
    try {
      systemPath = await this.getSystemPath()
      logger.info('systemPath', systemPath);
    } catch (error) {
      logger.error('[MCP] Failed to get system PATH:', error)
    }
    // 将原始 PATH 按分隔符分割成数组
    const pathSeparator = process.platform === 'win32' ? ';' : ':'
    const existingPaths = [...systemPath.split(pathSeparator), ...originalPath.split(pathSeparator)].filter(Boolean);

    const homeDir = process.env.HOME || process.env.USERPROFILE || ''

    // 定义要添加的新路径
    const newPaths: string[] = [];
    const studioName = process.env.STUDIO_NAME;

    if (isMac) {
      newPaths.push(
        '/bin',
        '/usr/bin',
        '/usr/local/bin',
        '/usr/local/sbin',
        '/opt/homebrew/bin',
        '/opt/homebrew/sbin',
        '/usr/local/opt/node/bin',
        `${homeDir}/.nvm/current/bin`,
        `${homeDir}/.npm-global/bin`,
        `${homeDir}/.yarn/bin`,
        `${homeDir}/.cargo/bin`,
        `${homeDir}/.${studioName}/bin`,
        '/opt/local/bin'
      )
    }

    if (isLinux) {
      newPaths.push(
        '/bin',
        '/usr/bin',
        '/usr/local/bin',
        `${homeDir}/.nvm/current/bin`,
        `${homeDir}/.npm-global/bin`,
        `${homeDir}/.yarn/bin`,
        `${homeDir}/.cargo/bin`,
        `${homeDir}/${studioName}/bin`,
        '/snap/bin'
      )
    }

    if (isWin) {
      newPaths.push(
        `${process.env.APPDATA}\\npm`,
        `${homeDir}\\AppData\\Local\\Yarn\\bin`,
        `${homeDir}\\.cargo\\bin`,
        `${homeDir}\\.${studioName}\\bin`,
        `${homeDir}\\.${studioName}\\nodejs`,
      )
    }

    // 只添加不存在的路径
    newPaths.forEach((path) => {
      if (path && !existingPaths.includes(path)) {
        existingPaths.push(path)
      }
    })

    // 转换回字符串
    return Array.from(new Set(existingPaths)).join(pathSeparator)
  }


  close = async () => {
    await this.mcp.close();
    return true;
  }
  callTool = async (toolName: string, params: Record<string, any>, signal?: AbortSignal) => {
    const tool = this.tools.find((tool) => tool.name === toolName);
    if (!tool) {
      throw new Error(`Tool ${toolName} not found`);
    }

    const result = await this.mcp.callTool({
      name: toolName,
      arguments: params,
    }, undefined, {
      signal,
    });
    return result;
  };
}
