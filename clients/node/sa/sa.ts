import { getLogPath } from "../utils/logger";
import { ipcMain } from "electron";

const SensorsAnalytics = require('sa-sdk-node');

const sensorsUrl = 'https://ubs.sf-express.com/sa?project=datamark';

export const sa = new SensorsAnalytics({
  server_url: sensorsUrl,
});

export const trackEvent = (distinctId: string, event: string, properties: any) => {
  try {
    sa.track(distinctId, 'mcp_client_events', {
      trigger_event: event,
      ...properties,
      app_version: process.env.APP_VERSION,
    });
  } catch (e) {
    return;
  }

}



export const registerSa = () => {
  sa.disableReNameOption();

  sa.initNWConsumer({
    url: sensorsUrl,
    cachePath: getLogPath('info' as any, true),
    timeout: 30 * 1000,
  });

  ipcMain.on('track-event', (_, distinctId, event, properties) => {
    trackEvent(distinctId, event, properties);
  });
}