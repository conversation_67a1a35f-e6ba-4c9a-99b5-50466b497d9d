// const { autoUpdater } = require('electron-updater');
import { autoUpdater, type UpdateCheckResult } from 'electron-updater'
import { app, BrowserWindow, dialog, MessageBoxOptions, shell } from 'electron';
import logger from '../utils/logger';
import { getUserFromDb } from '../db/user';
import axios from '../utils/axios';
import { isMac, isWin } from '../utils/constant';

interface UpdateInfo {
  version: string;
  files?: any[];
  path?: string;
  sha512?: string;
  releaseDate?: string;
}

interface ProgressInfo {
  total: number;
  delta: number;
  transferred: number;
  percent: number;
  bytesPerSecond: number;
}

interface UpdaterEvents {
  'updater-checking-for-update': void;
  'updater-update-available': UpdateInfo;
  'updater-update-not-available': UpdateInfo;
  'updater-error': string;
  'updater-download-progress': ProgressInfo;
  'updater-update-downloaded': UpdateInfo;
  'updater-manual-check-no-update': void;
  'updater-manual-check-error': string;
}

export class AutoUpdaterManager {
  // private mainWindow: BrowserWindow | null = null;
  private isUpdateDownloaded: boolean = false;
  private isChecking: boolean = false;

  private isDownloading: boolean = false;

  constructor() {
    this.init();
  }

  private init = (): void => {
    // 配置自动更新器
    autoUpdater.autoDownload = false; // 自动下载更新
    autoUpdater.autoInstallOnAppQuit = true; // 应用退出时自动安装

    // 设置更新日
    autoUpdater.logger = logger;
    if (autoUpdater.logger && 'transports' in autoUpdater.logger) {
      (autoUpdater.logger as any).transports.file.level = 'info';
    }

    // 配置更新服务器 URL（根据环境变量配置）
    if (process.env.UPDATE_SERVER_URL) {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url: process.env.UPDATE_SERVER_URL,
      });
    }

    this.setupEventListeners();
  }

  private setupEventListeners = (): void => {
    // 检查更新时触发
    autoUpdater.on('checking-for-update', () => {
      logger.info('检查更新中...');
      this.sendToRenderer('updater-checking-for-update');
    });

    // 发现可用更新
    autoUpdater.on('update-available', (info: UpdateInfo) => {
      logger.info('发现可用更新:', info);
      this.sendToRenderer('updater-update-available', info);
    });

    // 没有可用更新
    autoUpdater.on('update-not-available', (info: UpdateInfo) => {
      logger.info('当前已是最新版本:', info);
      this.sendToRenderer('updater-update-not-available', info);
    });

    // 更新出错
    autoUpdater.on('error', (err: Error) => {
      logger.error('更新错误:', err);
      this.sendToRenderer('updater-error', err.message);
    });

    // 下载进度
    autoUpdater.on('download-progress', (progressObj: ProgressInfo) => {
      this.isDownloading = true;
      const message = `下载速度: ${Math.round(progressObj.bytesPerSecond / 1024)} KB/s - 已下载 ${progressObj.percent.toFixed(2)}% (${progressObj.transferred}/${progressObj.total})`;
      logger.info(message);
      this.sendToRenderer('updater-download-progress', progressObj);
    });

    autoUpdater.on('update-cancelled', () => {
      logger.info('更新取消');
      this.isDownloading = false;
    });

    // 更新下载完成
    autoUpdater.on('update-downloaded', (info: UpdateInfo) => {
      logger.info('更新下载完成:', info);
      this.isDownloading = false;
      this.isUpdateDownloaded = true;
      this.sendToRenderer('updater-update-downloaded', info);

      // 可以选择立即安装或延迟安装
      // this.showUpdateDialog(info);
      // this.showUpdateDialog(info);
    });
  }

  public getMainWindow = (): BrowserWindow => {
    return BrowserWindow.getAllWindows()[0];
  }

  private sendToRenderer = <K extends keyof UpdaterEvents>(
    event: K,
    data?: UpdaterEvents[K]
  ): void => {
    if (this.getMainWindow() && this.getMainWindow().webContents) {
      logger.info('sendToRenderer', event, data);
      this.getMainWindow().webContents.send(event, data);
    }
  }

  // 检查更新
  public checkForUpdates = async (): Promise<UpdateCheckResult | null> => {
    if (this.isChecking) {
      logger.info('正在检查更新中，跳过本次检查');
      return null;
    }

    try {
      this.isChecking = true;
      const result = await autoUpdater.checkForUpdates();

      // logger.info('更新检查结果:', result);
      return result;
    } catch (error) {
      logger.error('检查更新失败:', error);
      throw error;
    } finally {
      this.isChecking = false;
    }
  }

  checkLatestVersion = async () => {
    let name = 'alink-setup'
    let sys = ''
    if (isWin) {
      sys = 'win-x64'
    }
    if (isMac) {
      // 判断mac 的芯片是arm64 还是 x64
      const arch = process.arch;
      if (arch === 'arm64') {
        sys = 'mac-arm64'
      } else {
        sys = 'mac-x64'
      }
    }
    const user = getUserFromDb();
    const version = await axios.get(`${process.env.BASE_SERVER_MARKET_URL}/pkgversion/latestVersion`, {
      params: {
        name,
        sys
      },
      headers: {
        'Content-Type': 'application/json',
        'token': user.session
      },
      proxy: false
    });
    if (version.data.errorCode === '09020102') {
      this.getMainWindow().webContents.send('logout');
      return false;
    }
    if (version.data.data.version !== process.env.APP_VERSION) {
      return version.data.data;
    }
    return false;
  }

  // 手动检查更新
  public checkForUpdatesManually = async (): Promise<UpdateCheckResult | null> => {
    try {
      const result = await this.checkForUpdates();

      // 如果没有可用更新，显示提示
      if (!result || !result.updateInfo) {
        this.sendToRenderer('updater-manual-check-no-update');
        return null;
      }
      if (result.isUpdateAvailable) {

        // await axios.get(`${process.env.BASE_SERVER_MARKET_URL}/api/v1/updater/check-update?version=${result.updateInfo.version}`);
        this.showWillUpdateDialog(result.updateInfo);
      }


      return {
        isUpdateAvailable: result.isUpdateAvailable,
        updateInfo: {
          version: result.updateInfo?.version,
          releaseDate: result.updateInfo?.releaseDate,
          files: result.updateInfo?.files,
          path: result.updateInfo?.path,
          sha512: result.updateInfo?.sha512,
        },
        versionInfo: {
          version: result.versionInfo.version,
          releaseDate: result.versionInfo.releaseDate,
          files: result.versionInfo.files,
          path: result.versionInfo.path,
          sha512: result.versionInfo.sha512,
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.sendToRenderer('updater-manual-check-error', errorMessage);
      throw error;
    }
  }


  // 显示即将更新
  showWillUpdateDialog = async (info: UpdateInfo): Promise<void> => {
    if (!this.getMainWindow()) return;
    const latest = await this.checkLatestVersion();
    let options: MessageBoxOptions
    let isForce = false;
    if (latest && latest.version && latest.version !== process.env.APP_VERSION && latest.is_force === 1) {
      options = {
        type: 'info',
        title: '强制更新',
        message: `检测到新版本，请立即更新，${latest.version}需要强制更新，否则应用将无法使用`,
        detail: `版本 ${info.version} ，请点击"下载更新"`,
        buttons: ['下载更新', '退出应用'],
        defaultId: 0,
        cancelId: 1
      }
      isForce = true;

      dialog.showMessageBox(this.getMainWindow(), options).then((result) => {
        if (isForce) {
          if (result.response === 0) {
            const suffix = isWin ? '.exe' : '.dmg';
            const dmgFile = (info.files || []).find((file) => file.url.endsWith(suffix));
            if (dmgFile) {
              const url = `${process.env.UPDATE_SERVER_URL}/${dmgFile.url}`;
              logger.info('下载更新', url);
              shell.openExternal(url);
              app.quit();
            }
          } else {
            app.quit();
          }
        }
      });
    } else {
      options = {
        type: 'info',
        title: '应用更新',
        message: '检测到新版本，是否下载更新',
        detail: `版本 ${info.version} 已准备就绪。点击"下载更新"后台开始下载`,
        buttons: ['下载更新', '取消'],
        defaultId: 0,
        cancelId: 1
      };
      if (isMac) {
        dialog.showMessageBox(this.getMainWindow(), options).then((result) => {
          if (result.response === 0) {
            // 下载更新
            if (isWin) {
              this.downloadUpdate();
            } else {
              if (info.files) {
                const dmgFile = info.files.find((file) => file.url.endsWith('.dmg'));
                if (dmgFile) {
                  const url = `${process.env.UPDATE_SERVER_URL}/${dmgFile.url}`;
                  shell.openExternal(url);
                }
              }
            }
          }
        });
      }
    }
  }


  downloadUpdate = (): void => {
    if (this.isDownloading) {
      logger.info('正在下载更新，跳过本次下载');
      return;
    }
    if (isMac) {
      autoUpdater.checkForUpdates().then((result) => {
        if (result && result.isUpdateAvailable) {
          const dmgFile = result.updateInfo?.files?.find((file) => file.url.endsWith('.dmg'));
          if (dmgFile) {
            const url = `${process.env.UPDATE_SERVER_URL}/${dmgFile.url}`;
            shell.openExternal(url);
          }
        }
      });
    } else {
      if (this.isUpdateDownloaded) {
        this.quitAndInstall();
      } else {
        autoUpdater.checkForUpdates().then((result) => {
          if (result && result.isUpdateAvailable) {
            autoUpdater.downloadUpdate(result.cancellationToken);
          }
        });
      }
    }
  }

  // 显示更新对话框
  showUpdateDialog = (info: UpdateInfo): void => {
    if (!this.getMainWindow()) return;

    const options: MessageBoxOptions = {
      type: 'info',
      title: '应用更新',
      message: '新版本已下载完成',
      detail: `版本 ${info.version} 已准备就绪。点击"立即重启"安装更新，或选择"稍后安装"在下次启动时安装。`,
      buttons: ['立即重启', '稍后安装'],
      defaultId: 0,
      cancelId: 1
    };

    dialog.showMessageBox(this.getMainWindow(), options).then((result) => {
      if (result.response === 0) {
        // 立即重启并安装更新
        this.quitAndInstall();
      }
      // 选择稍后安装，不做任何操作，下次启动时自动安装
    });
  }

  // 退出并安装更新
  public quitAndInstall = (): void => {
    if (isMac) {
      autoUpdater.checkForUpdates().then((result) => {
        if (result && result.isUpdateAvailable) {
          const dmgFile = result.updateInfo?.files?.find((file) => file.url.endsWith('.dmg'));
          if (dmgFile) {
            const url = `${process.env.UPDATE_SERVER_URL}/${dmgFile.url}`;
            shell.openExternal(url);
          }
        }
      });
    } else {
      if (this.isUpdateDownloaded) {
        autoUpdater.quitAndInstall();
      } else {
        logger.warn('尝试安装更新，但更新尚未下载完成');
      }
    }

  }

  // 静默更新（后台下载，不显示对话框）
  public silentUpdate = async (): Promise<any> => {
    try {
      autoUpdater.autoDownload = false;
      autoUpdater.autoInstallOnAppQuit = true;

      const result = await autoUpdater.checkForUpdates();
      logger.info('静默更新检查完成:', result);
      if (result && result.isUpdateAvailable) {
        autoUpdater.downloadUpdate(result.cancellationToken);
      }
      return result;
    } catch (error) {
      logger.error('静默更新失败:', error);
      throw error;
    }
  }

  // 启动时检查更新
  public checkForUpdatesOnStartup = async (): Promise<void> => {
    // 延迟检查，避免影响应用启动
    setTimeout(async () => {
      try {
        await this.silentUpdate();
      } catch (error) {
        logger.error('启动时更新检查失败:', error);
      }
    }, 5000); // 5秒后检查更新
  }

  // 定期检查更新
  public startPeriodicCheck = (intervalHours: number = 4): void => {
    const intervalMs = intervalHours * 60 * 60 * 1000;

    setInterval(async () => {
      try {
        await this.silentUpdate();
      } catch (error) {
        logger.error('定期更新检查失败:', error);
      }
    }, intervalMs);

    logger.info(`已设置定期更新检查，间隔 ${intervalHours} 小时`);
  }
}

// 删除重复的导出声明
