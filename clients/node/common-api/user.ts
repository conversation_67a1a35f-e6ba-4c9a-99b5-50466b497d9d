import { ipcMain, IpcMainInvokeEvent, BrowserWindow } from 'electron';
import { CasClient } from '../cas';
import { getUserFromDb, saveUserToDb, deleteUserFromDb } from '../db/user';
import { isWin, isMac } from '../utils/constant';
import axios from '../utils/axios';
import { Qrcode } from '../cas';
import { ElectronAgent } from '../chat/ElectronAgent';
import logger from '../utils/logger';


const getMainWindow = () => {
  return BrowserWindow.getAllWindows()[0]
}

export const addUserListener = () => {
  const casClient = new CasClient();


  const getUser = async (event: IpcMainInvokeEvent, data: any) => {
    const user = getUserFromDb();
    return user;
  }
  ipcMain.handle('get-user', getUser);

  const saveUser = async (event: IpcMainInvokeEvent, data: any) => {
    const user = data;
    const currentUser = saveUserToDb(user.userId, user.userName, user.session);
    return currentUser;
  }
  // ipcMain.handle('save-user', saveUser);

  const getQrCode = async (event: IpcMainInvokeEvent, data: any): Promise<Qrcode | false> => {
    const qrCode = await casClient.getQrcode();
    return qrCode;
  };

  const getQrCodeStatus = async (event: IpcMainInvokeEvent, data: any) => {
    const qrCodeStatus = await casClient.getQrcodeStatus();
    return qrCodeStatus;
  }

  const checkToken = async (event: IpcMainInvokeEvent, data: any) => {
    const user = getUserFromDb();
    if (user.session) {
      const tokenResult = await casClient.checkToken(user.session);
      return tokenResult;
    }
    return false;
  }

  const checkLatestVersion = async (event: IpcMainInvokeEvent, data: any) => {
    let name = 'alink-setup'
    let sys = ''
    if (isWin) {
      sys = 'win-x64'
    }
    if (isMac) {
      // 判断mac 的芯片是arm64 还是 x64
      const arch = process.arch;
      if (arch === 'arm64') {
        sys = 'mac-arm64'
      } else {
        sys = 'mac-x64'
      }
    }
    const user = getUserFromDb();
    const version = await axios.get(`${process.env.BASE_SERVER_MARKET_URL}/pkgversion/latestVersion`, {
      params: {
        name,
        sys
      },
      headers: {
        'Content-Type': 'application/json',
        'token': user.session
      },
      proxy: false
    });
    if (version.data.errorCode === '09020102') {
      getMainWindow().webContents.send('logout');
      return;
    }
    logger.info('version', version.data.data.version, process.env.APP_VERSION);
    if (version.data.data.version !== process.env.APP_VERSION) {
      return version.data.data;
    }
    return false;
  }

  const getAppVersion = async (event: IpcMainInvokeEvent, data: any) => {
    return process.env.APP_VERSION;
  }

  const logout = async (event: IpcMainInvokeEvent, data: any) => {
    const user = getUserFromDb();
    if (user.session) {
      try {
        const logoutResult = await casClient.logout(user.session);
        ElectronAgent.getInstance().close();
        ElectronAgent.destroyInstance();
        deleteUserFromDb();
        return logoutResult;
      } catch (e) {
        console.error(e);
        return false;
      }
    }
    return false;
  }

  const deleteUser = async (event: IpcMainInvokeEvent, data: any) => {
    deleteUserFromDb();
    return true;
  }
  ipcMain.handle('get-qr-code', getQrCode);
  ipcMain.handle('get-qr-code-status', getQrCodeStatus);
  ipcMain.handle('check-token', checkToken);
  ipcMain.handle('save-user', saveUser);
  ipcMain.handle('delete-user', deleteUser);
  ipcMain.handle('logout', logout);
  ipcMain.handle('check-latest-version', checkLatestVersion);
  ipcMain.handle('get-app-version', getAppVersion);
  return () => {
    ipcMain.removeHandler('get-user');
    ipcMain.removeHandler('get-qr-code');
    ipcMain.removeHandler('get-qr-code-status');
    ipcMain.removeHandler('check-token');
    ipcMain.removeHandler('save-user');
    ipcMain.removeHandler('logout');
    ipcMain.removeHandler('check-latest-version');
    ipcMain.removeHandler('get-app-version');
    ipcMain.removeHandler('delete-user');
  }
};
