import { ipc<PERSON><PERSON>, BrowserWindow } from 'electron';
import axios from '../utils/axios';
import OpenAI from 'openai';
import logger from '../utils/logger';

import {
  getProviderList,
  saveProvider,
  getModelListByProvider,
  getModelList,
  saveModel,
  getProvider,
  saveProviderStatus,
  Provider,
  Model,
  getEnabledModelList,
  updateModel,
  setModelDefault,
  saveProviderCurrentAPIKey,
  updateProvider,
  getDefaultModel,
  deleteModel,
  getEnabledModelListByProvider
} from '../db/provider';
import { AIPLAT_API_BASE_URL, AIPLAT_MODEL_URL, AIPLAT_BASE_API_KEY, AIPLAT_API_SIT_BASE_URL, AIPLAT_MODEL_SIT_URL, AIPLAT_MODEL_SIT_GET_DEFAULT_API_KEY, AIPLAT_MODEL_GET_DEFAULT_API_KEY } from '../constant';
import { getUserFromDb } from '../db/user';


const getMainWindow = () => {
  return BrowserWindow.getAllWindows()[0];
}

const _getModelListByProvider = async (provider: string) => {
  const isSit = process.env.APP_ENV === 'SIT';
  // let MODEL_URL = isSit ? AIPLAT_MODEL_SIT_URL : AIPLAT_MODEL_URL;
  let MODEL_URL = AIPLAT_MODEL_URL;
  const currentProvider = getProvider(provider);
  if (!currentProvider) {
    return [];
  }
  let modelUrl = ''
  const isAIPLAT = currentProvider.provider === 'AIPLAT' && (currentProvider.apiBaseUrl === AIPLAT_API_BASE_URL || currentProvider.apiBaseUrl === AIPLAT_API_SIT_BASE_URL);
  if (isAIPLAT) {
    modelUrl = MODEL_URL;
  } else {
    if (currentProvider.apiBaseUrl.endsWith('/')) {
      modelUrl = currentProvider.apiBaseUrl + 'models';
    } else {
      modelUrl = currentProvider.apiBaseUrl + '/models';
    }
  }
  const apiKey = currentProvider.currentAPIKey;
  const modelList = getModelListByProvider(provider);

  if (modelList && modelList.length === 0) {
    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
    if (isAIPLAT) {
      headers['x-sf-userid'] = getUserFromDb().userId;
    }
    try {
      const res = await axios.get(modelUrl, {
        headers,
        proxy: false,
      });
      if (isAIPLAT && modelUrl === MODEL_URL) {
        // logger.info('res', res.data.data);
        const modelList = res.data.data;

        modelList.forEach((model: any) => {
          const getParameterRuleDefault = (name: string) => {
            return model?.parameter_rules?.find((rule: any) => rule.name === name)?.default;
          }
          saveModel(
            provider,
            model.name,
            {
              logo: processIconUrl(model.icon_url),
              enabled: 1,
              temperature: getParameterRuleDefault('temperature'),
              top_p: getParameterRuleDefault('top_p'),
              max_tokens: getParameterRuleDefault('max_tokens'),
              repetition_penalty: getParameterRuleDefault('frequency_penalty'),
              seed: getParameterRuleDefault('seed'),
            }
          )
        });
      } else {
        res.data.data.forEach((model: any) => {
          saveModel(
            provider,
            model.id
          )
        });
      }
      return getModelListByProvider(provider);
    } catch (error) {
      logger.error('error', error);
      return [];
    }
  } else {
    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    }
    if (isAIPLAT) {
      headers['x-sf-userid'] = getUserFromDb().userId;
    }
    try {
      const res = await axios.get(modelUrl, {
        headers,
        proxy: false,
      });


      // deleteAllModelByProvider(provider);
      const _modelList = getModelListByProvider(provider);
      if (isAIPLAT && modelUrl === MODEL_URL) {
        // logger.info('res', res);
        // 不在res.data.data.data中的模型删除
        // console.log('res.data.data', res.data.data);
        // logger.info('res.data.data', res.data.data);
        _modelList.forEach((item: any) => {
          // console.log('item', item);
          if (!res.data.data.find((model: any) => model.name === item.model)) {
            deleteModel(provider, item.model);
          }
        });
        // 在res.data.data.data中的模型保存
        res.data.data.forEach((model: any) => {
          // console.log('model', model.parameter_rules);
          const getParameterRuleDefault = (name: string) => {
            return model?.parameter_rules?.find((rule: any) => rule.name === name)?.default;
          }
          saveModel(
            provider,
            model.name,
            {
              logo: processIconUrl(model.icon_url),
              temperature: getParameterRuleDefault('temperature'),
              top_p: getParameterRuleDefault('top_p'),
              max_tokens: getParameterRuleDefault('max_tokens'),
              repetition_penalty: getParameterRuleDefault('frequency_penalty'),
              seed: getParameterRuleDefault('seed'),
              modelTag: model.model_tags?.join(',')
            }
          )
        });
      } else {
        // logger.info('res', res.data.data);
        // 不在res.data.data中的模型删除
        _modelList.forEach((item: any) => {
          if (!res.data.data.find((model: any) => model.id === item.model)) {
            deleteModel(provider, item.model);
          }
        });
        // 在res.data.data中的模型保存
        res.data.data.forEach((model: any) => {
          saveModel(
            provider,
            model.id
          )
        });
      }
      return getModelListByProvider(provider);
    } catch (error) {
      logger.error('error', error);
      return [];
    }
  }
}

// 帮助函数：处理icon_url替换
const processIconUrl = (url: string) => {
  if (url && url.startsWith('http://inc-aiplat-core-shenzhen-futian1.oss.sfcloud.local:8080')) {
    return url.replace('http://inc-aiplat-core-shenzhen-futian1.oss.sfcloud.local:8080', 'https://ai.sf-express.com/oss-proxy');
  }
  return url;
};



export const addModelProviderListener = () => {
  // 获取模型提供商列表
  ipcMain.handle('get-model-provider-list', async (): Promise<Provider[]> => {
    const providerList = getProviderList();

    // console.log('providerList', providerList);
    if (providerList.length === 0) {
      const res = [
        {
          id: 0,
          provider: 'AIPLAT',
          name: 'AIPLAT',
          apiBaseUrl: AIPLAT_API_BASE_URL,
          modelUrl: AIPLAT_MODEL_URL,
          defaultBaseModelUrl: AIPLAT_API_BASE_URL,
          defaultApiKey: AIPLAT_BASE_API_KEY,
          createdAt: new Date().toISOString(),
          status: 1,
          currentAPIKey: AIPLAT_BASE_API_KEY,
          isSetDefault: 0,
        },
        // {
        //   id: 1,
        //   provider: 'OpenAI',
        //   name: 'OpenAI',
        //   apiBaseUrl: 'https://api.gptsapi.net/v1/',
        //   modelUrl: 'https://api.openai.com/v1/models',
        //   defaultBaseModelUrl: 'https://api.openai.com/v1',
        //   defaultApiKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
        //   currentAPIKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
        //   createdAt: new Date().toISOString(),
        //   status: 1
        // },
        // {
        //   id: 2,
        //   provider: 'Anthropic',
        //   name: 'Anthropic',
        //   apiBaseUrl: 'https://api.gptsapi.net/v1/',
        //   modelUrl: 'https:/api.gptsapi.net/v1/models',
        //   defaultBaseModelUrl: 'https://api.gptsapi.net/v1/',
        //   defaultApiKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
        //   currentAPIKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
        //   createdAt: new Date().toISOString(),
        //   status: 1
        // },
        // {
        //   id: 3,
        //   provider: 'DeepSeek',
        //   name: 'DeepSeek',
        //   apiBaseUrl: 'https://api.deepseek.com/v1/',
        //   modelUrl: 'https://api.deepseek.com/v1/models',
        //   defaultBaseModelUrl: 'https://api.deepseek.com/v1/',
        //   defaultApiKey: '***********************************',
        //   currentAPIKey: '***********************************',
        //   createdAt: new Date().toISOString(),
        //   status: 1
        // }
      ]
      res.forEach(provider => {
        saveProvider(provider.provider, {
          name: provider.name,
          apiBaseUrl: provider.apiBaseUrl,
          defaultApiKey: provider.defaultApiKey,
          currentAPIKey: provider.currentAPIKey,
          status: provider.status,
          defaultBaseModelUrl: provider.defaultBaseModelUrl,
          modelUrl: provider.modelUrl,
          isSetDefault: provider.isSetDefault || 0,
        });
      });
      return res;
    } else {
      //   // {
      //   //   provider: 'AIPLAT',
      //   //   name: 'AIPLAT',
      //   //   apiBaseUrl: 'http://llm-model-hub-apis.sf-express.com/v1/',
      //   //   modelUrl: AIPLAT_MODEL_URL,
      //   //   defaultBaseModelUrl: 'http://llm-model-hub-apis.sf-express.com/v1/',
      //   //   defaultApiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NLZXkiOiJhcGkyX2Q0ZDZkM2MwLTMyYTYtNDc3Ni05ZWY1LWU2MzEwOTFkMjBiOCIsImp0aSI6OTY1OSwicHJvamVjdF9pZCI6MTQ2Niwic3lzdGVtS2V5IjoiOTMyMmUzMDEtZmEyOC00YjE0LWJkMjQtODM0NzM3NTViMzgwIn0.x1HCtCfuiLZ8_lBBX2LFviqCmh2JhQE2TivCXY7Tkw4',
      //   //   currentAPIKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3NLZXkiOiJhcGkyX2Q0ZDZkM2MwLTMyYTYtNDc3Ni05ZWY1LWU2MzEwOTFkMjBiOCIsImp0aSI6OTY1OSwicHJvamVjdF9pZCI6MTQ2Niwic3lzdGVtS2V5IjoiOTMyMmUzMDEtZmEyOC00YjE0LWJkMjQtODM0NzM3NTViMzgwIn0.x1HCtCfuiLZ8_lBBX2LFviqCmh2JhQE2TivCXY7Tkw4',
      //   //   status: 1
      //   // },
      //   {
      //     provider: 'OpenAI',
      //     name: 'OpenAI',
      //     apiBaseUrl: 'https://api.gptsapi.net/v1/',
      //     modelUrl: 'https://api.gptsapi.net/v1/models',
      //     defaultBaseModelUrl: 'https://api.gptsapi.net/v1',
      //     defaultApiKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
      //     createdAt: new Date().toISOString(),
      //     status: 1
      //   },
      //   {
      //     provider: 'Anthropic',
      //     name: 'Anthropic',
      //     apiBaseUrl: 'https://api.gptsapi.net/v1/',
      //     modelUrl: 'https://api.gptsapi.net/v1/models',
      //     defaultBaseModelUrl: 'https://api.gptsapi.net/v1',
      //     defaultApiKey: 'sk-6zB97b0feee4b0bf294ca47c3bbd4be5af1653eab1aop7oJ',
      //     status: 1
      //   },
      //   {
      //     provider: 'DeepSeek',
      //     name: 'DeepSeek',
      //     defaultApiKey: '***********************************',
      //     modelUrl: 'https://api.deepseek.com/v1/models',
      //     defaultBaseModelUrl: 'https://api.deepseek.com/v1',
      //     apiBaseUrl: 'https://api.deepseek.com/v1/',
      //     status: 1
      //   },
      // ];
      // res.forEach(provider => {
      //   saveProvider(provider.provider, {
      //     name: provider.name,
      //     apiBaseUrl: provider.apiBaseUrl,
      //     defaultApiKey: provider.defaultApiKey,
      //     defaultBaseModelUrl: provider.modelUrl,
      //     // currentAPIKey: provider.currentAPIKey,

      //     modelUrl: provider.modelUrl,
      //     status: provider.status,
      //   });
      // });
      // providerList.filter(provider => {
      //   const index = res.findIndex(p => p.provider === provider.provider);
      //   if (index === -1) {
      //     return true
      //   }
      //   return false;
      // }).forEach(provider => {
      //   deleteProvider(provider.provider);
      //   deleteModelByProvider(provider.provider);
      // });
      return getProviderList();
    }
  });
  ipcMain.handle('get-model-list-by-provider', async (event, {
    provider
  }: {
    provider: string
  }): Promise<Model[]> => {
    return await _getModelListByProvider(provider);
  })
  ipcMain.handle('get-all-model-list', async (event): Promise<Model[]> => {
    const providerList = getProviderList();
    const modelList = getModelList();
    // 过滤掉不在providerList中的model
    return modelList.filter((model: any) => providerList.some((provider: any) => provider.provider === model.provider));
  });
  // 保存模型提供商开启或者关闭的状态
  ipcMain.handle('save-provider-status', async (event, {
    provider,
    status
  }: {
    provider: string,
    status: number
  }) => {
    await saveProviderStatus(provider, status);
  });

  ipcMain.handle('save-provider-current-api-key', async (event, {
    provider,
    currentAPIKey
  }: {
    provider: string,
    currentAPIKey: string
  }) => {
    await saveProviderCurrentAPIKey(provider, currentAPIKey);
  });

  ipcMain.handle('get-enabled-model-list', async (event): Promise<Model[]> => {
    return getEnabledModelList();
  });

  ipcMain.handle('enabled-model', async (event, {
    id,
    enabled
  }: {
    id: number,
    enabled: boolean
  }) => {
    const result = updateModel(id, {
      enabled: enabled ? 1 : 0
    });
    if (result) {
      // 如果默认模型被删除，则设置为第一个模型为默认模型
      if (result.enabled === 0) {
        const defaultModel = getDefaultModel(result.provider);
        if (defaultModel && defaultModel.id === result.id) {
          const _modelList = getEnabledModelListByProvider(result.provider);
          if (_modelList.length > 0) {
            setModelDefault(_modelList[0].id);
          }
        }
      }
      return result;
    } else {
      return false;
    }
  });
  ipcMain.handle('set-default-model', async (event, {
    id
  }: {
    id: number
  }) => {
    const result = setModelDefault(id);
    if (result) {
      return result;
    } else {
      return false;
    }
  });

  ipcMain.handle('verify-model-provider', async (event, {
    provider,
  }) => {
    const currentProvider = getProvider(provider);
    const modelList = await _getModelListByProvider(provider);
    if (modelList.length === 0) {
      return false;
    }
    if (currentProvider) {
      const openai = new OpenAI({
        apiKey: currentProvider.currentAPIKey || currentProvider.defaultApiKey,
        baseURL: currentProvider.apiBaseUrl,
      });
      try {
        await openai.chat.completions.create({
          model: modelList[0].model,
          messages: [{ role: 'user', content: '你好' }],
        });
        return true;
      } catch (error) {
        return false;
      }
    }
    return false;
  });

  ipcMain.handle('reset-default-base-model-url', async (event, {
    provider
  }: {
    provider: string
  }) => {
    const currentProvider = getProvider(provider);
    if (currentProvider) {
      await updateProvider(provider, {
        apiBaseUrl: currentProvider.defaultBaseModelUrl
      });
    }
  });
  ipcMain.handle('update-provider', async (event, {
    provider,
    updates
  }: {
    provider: string,
    updates: Partial<Provider>
  }) => {
    return updateProvider(provider, updates);
  });

  ipcMain.handle('get-default-api-key', async (event) => {
    const getDefaultApiKeyUrl = process.env.APP_ENV === 'SIT' ? AIPLAT_MODEL_SIT_GET_DEFAULT_API_KEY : AIPLAT_MODEL_GET_DEFAULT_API_KEY;
    const BASE_URL = process.env.APP_ENV === 'SIT' ? AIPLAT_API_SIT_BASE_URL : AIPLAT_API_BASE_URL;
    const res = await axios.get(getDefaultApiKeyUrl, {
      headers: {
        'x-sf-userid': getUserFromDb().userId,
      }
    });
    if (res.data && res.data.data && typeof res.data.data === 'string') {
      const currProvider = getProvider('AIPLAT');
      if (currProvider && currProvider.id && (currProvider.isSetDefault === 0 || !currProvider.isSetDefault)) {
        let key = res.data.data;
        key = key.startsWith('Bearer ') ? key.slice(7) : key;
        updateProvider('AIPLAT', {
          defaultApiKey: key.trim(),
          currentAPIKey: key.trim(),
          defaultBaseModelUrl: BASE_URL,
          apiBaseUrl: BASE_URL,
          isSetDefault: 1,
        });
        getMainWindow().webContents.send('update-provider-api-key', {
          provider: 'AIPLAT',
          apiKey: key.trim(),
        });
      }
    }

    return getProvider('AIPLAT');
  });
  return () => {
    ipcMain.removeHandler('get-movdel-provider-list');
    ipcMain.removeHandler('get-model-list-by-provider');
    ipcMain.removeHandler('get-all-model-list');
    ipcMain.removeHandler('save-provider-status');
    ipcMain.removeHandler('get-enabled-model-list');
    ipcMain.removeHandler('enabled-model');
    ipcMain.removeHandler('set-default-model');
    ipcMain.removeHandler('verify-model-provider');
    ipcMain.removeHandler('save-provider-current-api-key');
    ipcMain.removeHandler('reset-default-base-model-url');
    ipcMain.removeHandler('update-provider');
    ipcMain.removeHandler('get-default-api-key');
  }
}
