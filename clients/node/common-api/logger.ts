import { ipcMain } from 'electron';
import logger from '../utils/logger';

// 添加日志API监听器
export const addLoggerListener = () => {
  // 处理渲染进程发来的日志
  ipcMain.on('console-log', (_, level: string, ...args: any[]) => {
    switch (level) {
      case 'log':
        logger.log(...args);
        break;
      case 'info':
        logger.info(...args);
        break;
      case 'warn':
        logger.warn(...args);
        break;
      case 'error':
        logger.error(...args);
        break;
      case 'debug':
        logger.debug(...args);
        break;
      default:
        logger.log(...args);
    }
  });

  // 返回清理函数
  return () => {
    ipcMain.removeAllListeners('console-log');
  };
};
