import { ipc<PERSON>ain, IpcMainInvokeEvent, dialog, app, BrowserWindow } from 'electron';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { execFile } from 'child_process';
import { promisify } from 'util';
import logger from '../utils/logger';
import os from 'os';
import { contentProcessor } from '../attachment/ContentProcessor';
import { SUPPORT_DOC_EXTENSIONS, SUPPORT_EXCEL_EXTENSIONS, SUPPORT_EXTENSIONS, SUPPORT_PDF_EXTENSIONS, SUPPORT_PPT_EXTENSIONS } from '../attachment/constant';
import { getParserDir, getParserExeName } from '../utils/parserScripts';

const execFileAsync = promisify(execFile);

const attachmentsDir = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'attachments');

/**
 * 根据 MIME 类型获取文件扩展名
 */
/**
 * 检查文件类型是否被允许
 */
const isAllowedFileType = (filePath: string): boolean => {
  const fileName = path.basename(filePath).toLowerCase();
  const allowedExtensions = [
    ...SUPPORT_EXTENSIONS,
  ]

  return allowedExtensions.some(ext => fileName.endsWith(ext));
};

const getExtensionFromMimeType = (mimeType: string): string => {
  const mimeToExtension: { [key: string]: string } = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/bmp': '.bmp',
    'image/webp': '.webp',
    'text/plain': '.txt',
    'text/markdown': '.md',
    'application/pdf': '.pdf',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
    'application/vnd.ms-excel': '.xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx'
  };

  return mimeToExtension[mimeType] || '.bin';
};

/**
 * 获取解析器类型
 */
const getParserType = (fileExtension: string): 'doc' | 'excel' | 'ppt' | 'pdf' | null => {
  // const docExtensions = ['.doc', '.docx'];
  // const excelExtensions = ['.xls', '.xlsx', '.xlsm', '.xlsb'];

  if (SUPPORT_DOC_EXTENSIONS.includes(fileExtension)) {
    return 'doc';
  } else if (SUPPORT_EXCEL_EXTENSIONS.includes(fileExtension)) {
    return 'excel';
  } else if (SUPPORT_PPT_EXTENSIONS.includes(fileExtension)) {
    return 'ppt';
  } else if (SUPPORT_PDF_EXTENSIONS.includes(fileExtension)) {
    return 'pdf';
  }

  return null;
};

/**
 * 获取解析器可执行文件路径
 */
const getParserPath = (parserType: 'doc' | 'excel' | 'ppt' | 'pdf'): string => {
  const baseDir = getParserDir();
  return path.join(baseDir, getParserExeName(parserType));
};

/**
 * 调用外部解析器解析文档
 */
const parseDocumentFile = async (filePath: string, parserType: 'doc' | 'excel' | 'ppt' | 'pdf'): Promise<string> => {
  const parserPath = getParserPath(parserType);

  // 检查解析器是否存在
  if (!fs.existsSync(parserPath)) {
    throw new Error(`解析器不存在: ${parserPath}`);
  }

  try {
    const args = [filePath];

    // 根据解析器类型添加特定参数
    if (parserType === 'doc') {
      args.push('-f', 'text'); // 输出纯文本格式
    } else if (parserType === 'excel') {
      args.push('-f', 'csv'); // 输出CSV格式
    }

    logger.info('调用解析器', { parserPath, filePath, args });

    const { stdout, stderr } = await execFileAsync(parserPath, args, {
      timeout: 30000, // 30秒超时
      maxBuffer: 10 * 1024 * 1024, // 10MB缓冲区
      encoding: 'utf8'
    });

    if (stderr) {
      logger.warn('解析器stderr输出', { stderr });
    }

    if (!stdout.trim()) {
      throw new Error('解析器返回空内容');
    }

    return stdout.trim();

  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new Error(`解析器可执行文件不存在或无权限: ${parserPath}`);
    } else if (error.signal === 'SIGTERM') {
      throw new Error('解析器执行超时');
    } else if (error.code === 'EMSGSIZE') {
      throw new Error('文档内容过大，超出处理限制');
    } else {
      throw new Error(`解析器执行失败: ${error.message}`);
    }
  }
};

/**
 * 文件附件处理API
 */
export const addAttachmentListener = () => {
  // 上传文件
  ipcMain.handle('upload-attachment', async (event: IpcMainInvokeEvent, data: any) => {
    let parentWindow: BrowserWindow | null = null;

    try {
      // 获取父窗口
      parentWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

      if (!parentWindow) {
        logger.error('无法找到父窗口');
        return { success: false, message: '窗口状态异常，请重试' };
      }

      // 保存原始窗口状态
      const originalAlwaysOnTop = parentWindow.isAlwaysOnTop();

      // 设置窗口为置顶状态，确保对话框不会被其他窗口遮挡
      if (!originalAlwaysOnTop) {
        parentWindow.setAlwaysOnTop(true, 'modal-panel');
      }

      const _supportExtensions = SUPPORT_EXTENSIONS.map(ext => {
        return ext.replace(/\./, '');
      });

      try {
        const result = await dialog.showOpenDialog(parentWindow, {
          // modal: true,              // 设置为模态对话框
          // alwaysOnTop: true,        // 对话框保持在最顶层

          properties: ['openFile'],
          filters: [
            { name: 'All Supported Files', extensions: _supportExtensions }
          ],
          title: '上传附件'
        });

        if (result.canceled || result.filePaths.length === 0) {
          return { success: false, message: '用户取消选择文件', canceled: true };
        }

        if (result.filePaths.length > 5) {
          return { success: false, message: '最多只能同时选择5个文件' };
        }

        const filePath = result.filePaths[0];


        const fileSize = fs.statSync(filePath).size;

        logger.info('上传附件', { filePath, fileSize });
        if (fileSize > 10 * 1024 * 1024) {
          logger.warn('文件大小超出限制', { filePath, fileSize });
          return { success: false, message: '文件大小超出限制' };
        }

        // 验证文件类型
        if (!isAllowedFileType(filePath)) {
          const fileName = path.basename(filePath);
          logger.warn('不支持的文件类型', { fileName });
          return {
            success: false,
            message: `不支持的文件类型: "${fileName}"\n支持的类型: 图片、文档(PDF/Office)、代码/文本文件`
          };
        }

        const fileExtension = path.extname(filePath);
        const fileName = path.basename(filePath, fileExtension);
        const uuid = uuidv4();
        const newFileName = `${uuid}${fileExtension}`;

        // 创建attachments目录
        // const attachmentsDir = path.join(app.getPath('userData'), 'attachments');
        if (!fs.existsSync(attachmentsDir)) {
          fs.mkdirSync(attachmentsDir, { recursive: true });
        }

        const targetPath = path.join(attachmentsDir, newFileName);

        // 复制文件
        await fs.promises.copyFile(filePath, targetPath);

        // 获取文件信息
        const stats = await fs.promises.stat(targetPath);

        const attachmentInfo = {
          id: uuid,
          originalName: fileName + fileExtension,
          fileName: newFileName,
          path: targetPath,
          size: stats.size,
          extension: fileExtension,
          uploadTime: new Date().toISOString()
        };

        logger.info('文件上传成功', {
          originalName: fileName + fileExtension,
          newName: newFileName,
          path: targetPath,
          size: stats.size
        });

        // 异步处理文件内容，不阻塞响应
        setImmediate(() => {
          contentProcessor.processAttachmentAsync(attachmentInfo).catch(error => {
            logger.error('异步处理附件内容失败', { uuid, error: error.message });
          });
        });

        return {
          success: true,
          data: attachmentInfo
        };

      } finally {
        // 恢复窗口状态
        if (parentWindow && !originalAlwaysOnTop) {
          parentWindow.setAlwaysOnTop(false);
        }

        // 确保父窗口重新获得焦点
        if (parentWindow) {
          parentWindow.focus();
        }
      }

    } catch (error: any) {
      logger.error('文件上传失败', error);

      // 即使出错也要恢复窗口状态
      if (parentWindow) {
        try {
          parentWindow.setAlwaysOnTop(false);
          parentWindow.focus();
        } catch (windowError) {
          logger.error('恢复窗口状态失败', windowError);
        }
      }

      return { success: false, message: '文件上传失败: ' + error.message };
    }
  });

  // 获取附件目录路径
  ipcMain.handle('get-attachments-directory', async (event: IpcMainInvokeEvent) => {
    try {
      // const attachmentsDir = path.join(app.getPath('userData'), 'attachments');
      return attachmentsDir;
    } catch (error) {
      logger.error('获取附件目录失败', error);
      return '';
    }
  });

  // 删除附件
  ipcMain.handle('delete-attachment', async (event: IpcMainInvokeEvent, fileName: string) => {
    try {
      // const attachmentsDir = path.join(app.getPath('userData'), 'attachments');
      const filePath = path.join(attachmentsDir, fileName);

      if (fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath);
        logger.info('附件删除成功', fileName);
        return { success: true };
      } else {
        return { success: false, message: '文件不存在' };
      }
    } catch (error: any) {
      logger.error('附件删除失败', error);
      return { success: false, message: '附件删除失败: ' + error.message };
    }
  });

  // 读取附件文件内容（用于预览）
  ipcMain.handle('read-attachment', async (event: IpcMainInvokeEvent, fileName: string) => {
    try {
      // const attachmentsDir = path.join(app.getPath('userData'), 'attachments');
      const filePath = path.join(attachmentsDir, fileName);

      if (!fs.existsSync(filePath)) {
        return { success: false, message: '文件不存在' };
      }

      // 读取文件为base64格式
      const fileBuffer = await fs.promises.readFile(filePath);
      const base64Data = fileBuffer.toString('base64');

      // 获取文件的MIME类型
      const extension = path.extname(fileName).toLowerCase();
      let mimeType = 'application/octet-stream';

      const mimeTypes: { [key: string]: string } = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.webp': 'image/webp',
        // '.pdf': 'application/pdf',
        '.txt': 'text/plain',
        '.md': 'text/markdown'
      };

      if (mimeTypes[extension]) {
        mimeType = mimeTypes[extension];
      }

      logger.info('文件读取成功', { fileName, size: fileBuffer.length });

      return {
        success: true,
        data: {
          fileName,
          mimeType,
          base64Data,
          size: fileBuffer.length
        }
      };
    } catch (error: any) {
      logger.error('文件读取失败', error);
      return { success: false, message: '文件读取失败: ' + error.message };
    }
  });

  // 解析文档内容（用于AI处理）
  ipcMain.handle('parse-document', async (event: IpcMainInvokeEvent, fileName: string) => {
    try {
      const filePath = path.join(attachmentsDir, fileName);

      if (!fs.existsSync(filePath)) {
        return { success: false, message: '文件不存在' };
      }

      const fileExtension = path.extname(fileName).toLowerCase();
      const parserType = getParserType(fileExtension);

      if (!parserType) {
        return {
          success: false,
          message: `不支持的文件类型: ${fileExtension}`,
          supportedTypes: ['.doc', '.docx', '.xls', '.xlsx']
        };
      }

      const parsedContent = await parseDocumentFile(filePath, parserType);

      logger.info('文档解析成功', {
        fileName,
        fileExtension,
        parserType,
        contentLength: parsedContent.length
      });

      return {
        success: true,
        data: {
          fileName,
          fileExtension,
          parserType,
          content: parsedContent,
          contentLength: parsedContent.length,
          parseTime: new Date().toISOString()
        }
      };

    } catch (error: any) {
      logger.error('文档解析失败', error);
      return {
        success: false,
        message: '文档解析失败: ' + error.message,
        error: error.toString()
      };
    }
  });

  // 从剪贴板上传附件
  ipcMain.handle('upload-attachment-from-clipboard', async (event: IpcMainInvokeEvent, fileData: { buffer: number[], originalName: string, mimeType: string, size: number }) => {
    try {
      const { buffer, originalName, mimeType, size } = fileData;

      // 生成唯一文件名
      const uuid = uuidv4();
      const fileExtension = path.extname(originalName) || getExtensionFromMimeType(mimeType);
      const newFileName = `${uuid}${fileExtension}`;
      const tempPath = path.join(attachmentsDir, newFileName);

      // 验证文件类型
      if (!isAllowedFileType(tempPath)) {
        logger.warn('剪贴板文件类型不支持', { originalName, mimeType, fileExtension });
        return {
          success: false,
          message: `不支持的文件类型: "${originalName}"\n支持的类型: 图片、文档(PDF/Office)、代码/文本文件`
        };
      }

      // 创建attachments目录
      if (!fs.existsSync(attachmentsDir)) {
        fs.mkdirSync(attachmentsDir, { recursive: true });
      }

      const targetPath = tempPath;

      // 将buffer数组转换为Buffer并保存文件
      const fileBuffer = Buffer.from(buffer);
      await fs.promises.writeFile(targetPath, fileBuffer);

      const attachmentInfo = {
        id: uuid,
        originalName,
        fileName: newFileName,
        path: targetPath,
        size: fileBuffer.length,
        extension: fileExtension,
        uploadTime: new Date().toISOString()
      };

      logger.info('剪贴板文件上传成功', {
        originalName,
        newName: newFileName,
        path: targetPath,
        size: fileBuffer.length,
        mimeType
      });

      // 异步处理文件内容，不阻塞响应
      setImmediate(() => {
        contentProcessor.processAttachmentAsync(attachmentInfo).catch(error => {
          logger.error('异步处理剪贴板附件内容失败', { uuid, error: error.message });
        });
      });

      return {
        success: true,
        data: attachmentInfo
      };

    } catch (error: any) {
      logger.error('剪贴板文件上传失败', error);
      return { success: false, message: '剪贴板文件上传失败: ' + error.message };
    }
  });

  return () => {
    ipcMain.removeHandler('upload-attachment');
    ipcMain.removeHandler('upload-attachment-from-clipboard');
    ipcMain.removeHandler('get-attachments-directory');
    ipcMain.removeHandler('delete-attachment');
    ipcMain.removeHandler('read-attachment');
    ipcMain.removeHandler('parse-document');
  };
};
