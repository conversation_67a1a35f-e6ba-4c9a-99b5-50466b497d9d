import { addUserListener } from './user';
import { addModelProviderListener } from './modelProvider';
import { addInstallListener } from './install';
import { addMcpListener } from './mcp';
import { addChatListener } from './chat';
import { addLoggerListener } from './logger';
import { addUpdaterListener } from './updater';
import { addAttachmentListener } from './attachment';

let userDisposer: any;
let modelProviderDisposer: any;
let installDisposer: any;
let mcpDisposer: any;
let chatDisposer: any;
let loggerDisposer: any;
let updaterDisposer: any;
let attachmentDisposer: any;

export const registerAPI = () => {
  userDisposer = addUserListener();
  modelProviderDisposer = addModelProviderListener();
  installDisposer = addInstallListener();
  mcpDisposer = addMcpListener();
  chatDisposer = addChatListener();
  loggerDisposer = addLoggerListener();
  updaterDisposer = addUpdaterListener();
  attachmentDisposer = addAttachmentListener();
};

export const unregisterAPI = () => {
  userDisposer();
  modelProviderDisposer();
  installDisposer();
  mcpDisposer();
  chatDisposer();
  loggerDisposer();
  updaterDisposer();
  attachmentDisposer();
};
