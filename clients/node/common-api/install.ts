import { ipcMain, IpcMainInvokeEvent } from 'electron';
import path from 'path';
import os from 'os';
import { isBinaryExists, getBinaryPath, isMsetExists, getMsetHomePath } from '../utils/process';
import { runInstallScript, runMsetInstallScript } from '../utils/installScripts';
import logger from '../utils/logger';
/**
 * 增加安装 uv 和 bun 保证使用的环境是可控制的
 * @returns
 */
export const addInstallListener = () => {
  ipcMain.handle('install-uv', async (event: IpcMainInvokeEvent, data: any) => {
    try {
      const res = await runInstallScript('install_uv.js');
      return true;
    } catch (error) {
      return false;
    }
  });
  ipcMain.handle('install-bun', async (event: IpcMainInvokeEvent, data: any) => {
    try {
      const res = await runInstallScript('install_bun.js');
      return true;
    } catch (error) {
      return false;
    }
  });
  ipcMain.handle('install-nodejs', async (event: IpcMainInvokeEvent, data: any) => {
    try {
      const res = await runInstallScript('install_nodejs.js');
      return true;
    } catch (error) {
      return false;
    }
  });

  ipcMain.handle('install-mset', async (event: IpcMainInvokeEvent, data: any) => {
    try {
      const res = await runMsetInstallScript();
      return true;
    } catch (error) {
      return false;
    }
  });


  ipcMain.handle('get-uv-status', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await isBinaryExists('uv');
    logger.info('res', res);
    return res;
  });


  ipcMain.handle('get-bun-status', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await isBinaryExists('bun');
    logger.info('res', res);
    return res;
  });


  ipcMain.handle('get-nodejs-status', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await isBinaryExists('node');
    logger.info('res', res);
    return res;
  });
  ipcMain.handle('get-mset-status', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await isMsetExists();
    logger.info('res', res);
    return res;
  });

  ipcMain.handle('get-uv-path', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await getBinaryPath('uv');
    return res;
  });
  ipcMain.handle('get-bun-path', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await getBinaryPath('bun');
    return res;
  });
  ipcMain.handle('get-nodejs-path', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await getBinaryPath('node');
    return res;
  });
  ipcMain.handle('get-mset-path', async (event: IpcMainInvokeEvent, data: any) => {
    const res = await getMsetHomePath();
    return res;
  });
  return () => {
    ipcMain.removeHandler('install-uv');
    ipcMain.removeHandler('install-bun');
    ipcMain.removeHandler('install-nodejs');
    ipcMain.removeHandler('get-uv-status');
    ipcMain.removeHandler('get-bun-status');
    ipcMain.removeHandler('get-uv-path');
    ipcMain.removeHandler('get-bun-path');
    ipcMain.removeHandler('get-nodejs-path');
    ipcMain.removeHandler('get-nodejs-status');
  }
};
