import { ipc<PERSON><PERSON>, BrowserWindow } from "electron";
import { getMcpList, saveMcp, deleteMcp, getMcp, MCP } from "../db/mcp";
import { ElectronAgent } from "../chat/ElectronAgent";
import axios from '../utils/axios';
import logger from "../utils/logger";
import { getUserFromDb } from "../db/user";


const getMainWindow = () => {
  return BrowserWindow.getAllWindows()[0]
}
export function addMcpListener() {

  ipcMain.handle('get-mcp-list', async (event, { name }: {
    name?: string;
  }) => {
    return getMcpList(name);
  });

  ipcMain.handle('get-tool-list', async () => {
    return ElectronAgent.getInstance().getToolList();
  });

  ipcMain.handle('get-tool-list-by-mcp', async (event, { id }: {
    id: number;
  }) => {
    const mcpConfig = getMcp(id);
    return ElectronAgent.getInstance().getToolListByMcp(mcpConfig.id.toString());
  });

  ipcMain.handle('save-mcp', async (event, { id, serverConfig, serverDesc, serverName, serverLogo, serverFromObj }: {
    id?: number;
    serverConfig?: string;
    serverDesc?: string;
    serverName?: string;
    serverLogo?: string;
    serverFromObj?: string;
  }) => {
    const result = await saveMcp({
      id,
      serverConfig,
      serverDesc,
      serverName,
      serverLogo,
      serverFromObj,
    });
    return result;
  });

  ipcMain.handle('delete-mcp', async (event, { id }: {
    id: number;
  }) => {
    const mcpConfig = getMcp(id);
    deleteMcp(id);
    const _mcpClient = ElectronAgent.getInstance().getMcpClient(mcpConfig.serverName);
    if (_mcpClient) {
      await ElectronAgent.getInstance().stop(mcpConfig.serverName);
    }
    return true;
  });

  ipcMain.handle('start-mcp', async (event, { id }: {
    id: number;
  }) => {
    const mcpConfig = getMcp(id);
    const _mcpClient = ElectronAgent.getInstance().getMcpClient(mcpConfig.id.toString());
    if (!_mcpClient) {
      // 添加 mcp client
      const result = await ElectronAgent.getInstance().addMcpClient(mcpConfig);
      if (result) {
        // const startResult = await ElectronAgent.getInstance().start(mcpConfig.id.toString());
        if (result.success) {
          await saveMcp({
            id,
            serverStatus: 1
          });
          return {
            success: true,
            error: null,
          };
        } else {
          return {
            success: false,
            error: result.error,
          };
        }
      }
      return {
        success: false,
        error: 'mcp client not found',
      };
    }
    return {
      success: false,
      error: 'mcp client not found',
    };
  });

  ipcMain.handle('stop-mcp', async (event, { id }: {
    id?: number;
  }) => {
    const mcpConfig = getMcp(id as number);
    const _mcpClient = ElectronAgent.getInstance().getMcpClient(mcpConfig.id.toString());
    if (_mcpClient) {
      await ElectronAgent.getInstance().stop(mcpConfig.id.toString());
    }
    const result = await saveMcp({
      id,
      serverStatus: 0
    });
    if (result.success) {
      return result.result
    } else {
      return false;
    }
  });

  ipcMain.handle('get-mcp-market-list', async (event, { pageNum, pageSize, name }: {
    pageNum: number;
    pageSize: number;
    name: string;
  }) => {
    type Response = {
      data: {
        data: {
          id: number;
          name: string;
          desc: string;
          // 1 顺丰云市场 2 sse 3 stdio
          protocol: number;
          url: string;
          config: string;
          icon: string;
        }[]
      }
      code: number;
      errorCode: string;
    };
    const user = getUserFromDb();
    const res = await axios.get<Response>(process.env.BASE_SERVER_MARKET_URL + '/mcpserver/pageList', {
      params: {
        page: pageNum,
        page_size: pageSize,
        name: name
      },

      headers: {
        userId: user.userId,
        'x-sf-userid': user.userId,
        cookie: `token=${user.session}`,
      },
      proxy: false,
    });
    // console.log(res.data.data.data);
    if (res.data.code === 0) {
      const currDataList = res?.data?.data?.data || [];
      // console.log(currDataList);
      const mcpMarketList: MCP[] = currDataList.map((item) => {
        let config: any = {};
        if (item.config) {
          try {
            const itemConfig = JSON.parse(item.config);
            console.log(item.config);
            config.mcpId = item.id;
            config.source = '1';
            const protocolMap = {
              1: 'sse',
              2: 'stdio',
              3: 'streamable_http',
            };
            if (itemConfig.command) {
              config.cmd = itemConfig.command;
              config.args = itemConfig.args;
              config.env = itemConfig.env;
              config.requestHeaders = itemConfig.requestHeaders;
              config.serverUrl = itemConfig.url || item.url;
              config.serverType = protocolMap[item.protocol] || 'stdio';
            } else {
              const key = Object.keys(itemConfig)[0];
              const value = key === 'mcpServers' ? Object.values(itemConfig[key])[0] : itemConfig[key];

              if (value) {
                config.cmd = value.command;
                config.args = value.args;
                config.env = value.env;
                config.requestHeaders = value.requestHeaders;
                config.serverUrl = value.url || item.url;
                config.serverType = protocolMap[item.protocol] || 'stdio';
              }
            }
          } catch (e) {
            logger.error('item.config', item.config);
          }
        }
        return {
          id: item.id,
          serverName: item.name,
          serverDesc: item.desc,
          serverConfig: JSON.stringify(config),
          serverFromObj: JSON.stringify(config),
          serverLogo: item.icon || '',
          serverStatus: 0,
          serverTools: '',
          isBuiltin: 0,
        }
      });
      return mcpMarketList || []
    }
    return []
  });


  ipcMain.handle('enabled-tool', async (event, { id, toolName }: {
    id: number;
    toolName: string;
  }) => {
    const mcpConfig = getMcp(id);

    const serverTools = mcpConfig.serverTools || '';
    const toolList = serverTools.split(',');
    if (toolList.findIndex(item => item === toolName) > -1) {
      return true;
    } else {
      toolList.push(toolName);
      const newServerTools = toolList.join(',');
      await saveMcp({
        id,
        serverTools: newServerTools,
      });
      return true;
    }
  });

  ipcMain.handle('disabled-tool', async (event, { id, toolName }: {
    id: number;
    toolName: string;
  }) => {
    const mcpConfig = getMcp(id);
    const serverTools = mcpConfig.serverTools || '';
    const toolList = serverTools.split(',');
    if (toolList.findIndex(item => item === toolName) === -1) {
      return true;
    } else {
      const newServerTools = toolList.filter(item => item !== toolName).join(',');
      await saveMcp({
        id,
        serverTools: newServerTools,
      });
      return true;
    }
  });

  return () => {
    ipcMain.removeHandler('get-mcp-list');
    ipcMain.removeHandler('save-mcp');
    ipcMain.removeHandler('delete-mcp');
    ipcMain.removeHandler('start-mcp');
    ipcMain.removeHandler('stop-mcp');
    ipcMain.removeHandler('get-mcp-market-list');
    ipcMain.removeHandler('enabled-tool');
    ipcMain.removeHandler('disabled-tool');
  }
}
