import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { AutoUpdaterManager } from '../updater/autoUpdater';

export const addUpdaterListener = () => {
  const updaterManager = new AutoUpdaterManager();

  // 在生产环境中启用自动更新
  if (process.env.NODE_ENV !== 'development') {
    // updaterManager.checkForUpdatesOnStartup();
    //updaterManager.startPeriodicCheck(4); // 每4小时检查一次
  }

  // 自动更新相关的 IPC 处理
  ipcMain.handle('updater-check-for-updates', async () => {
    if (updaterManager) {
      try {
        const res = await updaterManager.checkForUpdatesManually();
        // logger.info('更新检查结果:', res);
        return res;
      } catch (error) {
        throw error;
      }
    }
    throw new Error('更新管理器未初始化');
  });


  ipcMain.handle('updater-download-update', () => {
    if (updaterManager) {
      updaterManager.downloadUpdate();
    }
  });

  ipcMain.handle('updater-quit-and-install', () => {
    if (updaterManager) {
      updaterManager.quitAndInstall();
    }
  });

  return () => {

  }

};
