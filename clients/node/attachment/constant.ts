export const SUPPORT_TEXT_EXTENSIONS = [
  // 基础文本
  '.txt', '.md', '.markdown', '.html', '.htm', '.xml', '.css',

  // 编程语言
  '.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte',
  '.py', '.pyw', '.pyc', '.pyo', '.pyd',
  '.java', '.class', '.jar',
  '.c', '.h', '.cpp', '.hpp', '.cc', '.cxx',
  '.cs', '.vb', '.fs', '.fsx',
  '.php', '.phtml',
  '.rb', '.rbw',
  '.go', '.mod', '.sum',
  '.rs', '.rlib',
  '.swift',
  '.kt', '.kts',
  '.scala', '.sc',
  '.clj', '.cljs', '.cljc',
  '.lua',
  '.perl', '.pl', '.pm',
  '.r', '.rmd',
  '.m', '.mm',
  '.dart',
  '.elm',
  '.ex', '.exs',
  '.jl',
  '.nim',
  '.zig',

  // 配置文件
  '.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.config',
  '.env', '.gitignore', '.gitattributes',
  '.dockerignore', '.dockerfile',
  '.editorconfig', '.eslintrc', '.prettierrc',
  '.babelrc', '.npmrc', '.yarnrc',

  // 脚本文件
  '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',

  // 标记语言
  '.rst', '.tex', '.latex', '.org', '.adoc',

  // 数据文件
  '.csv', '.tsv', '.log', '.sql',

  // Web相关
  '.scss', '.sass', '.less', '.stylus',
  '.coffee', '.litcoffee',
  '.pug', '.jade', '.ejs', '.hbs', '.handlebars',

  // 移动开发
  '.gradle', '.pro', '.plist',

];

export const SUPPORT_DOC_EXTENSIONS = [
  '.doc', '.docx',
]

export const SUPPORT_EXCEL_EXTENSIONS = [
  '.xls', '.xlsx', '.xlsm', '.xlsb'
]

export const SUPPORT_IMAGE_EXTENSIONS = [
  '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
]

export const SUPPORT_PDF_EXTENSIONS = [
  '.pdf',
]

export const SUPPORT_PPT_EXTENSIONS = [
  '.ppt', '.pptx',
]

export const SUPPORT_EXTENSIONS = [
  ...SUPPORT_TEXT_EXTENSIONS,
  ...SUPPORT_DOC_EXTENSIONS,
  ...SUPPORT_EXCEL_EXTENSIONS,
  ...SUPPORT_IMAGE_EXTENSIONS,
  ...SUPPORT_PDF_EXTENSIONS,
  ...SUPPORT_PPT_EXTENSIONS,
];
