import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';
import { AttachmentInfo } from '../db/messages';
import { AttachmentContent, createAttachmentContent, updateAttachmentContent, updateProcessingStatus } from '../db/attachmentContents';
import { execFile } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import { SUPPORT_DOC_EXTENSIONS, SUPPORT_EXCEL_EXTENSIONS, SUPPORT_IMAGE_EXTENSIONS, SUPPORT_PDF_EXTENSIONS, SUPPORT_PPT_EXTENSIONS } from './constant';
import { getParserExeName, getParserDir } from '../utils/parserScripts';

const execFileAsync = promisify(execFile);
const attachmentsDir = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'attachments');

export class ContentProcessor {

  /**
   * 确定内容类型
   */
  private determineContentType(extension: string): 'image' | 'document' | 'text' | 'code' {
    const codeExts = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte', '.py', '.java', '.c', '.cpp', '.h', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.lua', '.dart', '.json', '.yaml', '.yml', '.xml', '.css', '.scss', '.html'];

    const ext = extension.toLowerCase();
    if (SUPPORT_IMAGE_EXTENSIONS.includes(ext)) return 'image';
    if (SUPPORT_DOC_EXTENSIONS.includes(ext)) return 'document';
    if (SUPPORT_PDF_EXTENSIONS.includes(ext)) return 'document';
    if (SUPPORT_PPT_EXTENSIONS.includes(ext)) return 'document';
    if (SUPPORT_EXCEL_EXTENSIONS.includes(ext)) return 'document';
    if (codeExts.includes(ext)) return 'code';
    return 'text';
  }

  /**
   * 异步处理附件内容
   */
  async processAttachmentAsync(attachmentInfo: AttachmentInfo): Promise<void> {
    const contentType = this.determineContentType(attachmentInfo.extension);

    try {
      // 1. 创建处理记录
      const attachmentContent: AttachmentContent = {
        file_uuid: attachmentInfo.id,
        file_name: attachmentInfo.originalName,
        file_extension: attachmentInfo.extension,
        file_size: attachmentInfo.size,
        content_type: contentType,
        processing_status: 'pending'
      };

      createAttachmentContent(attachmentContent);
      logger.info('创建附件内容处理记录', { fileUuid: attachmentInfo.id, contentType });

      // 2. 开始处理
      updateProcessingStatus(attachmentInfo.id, 'processing');

      // 3. 根据内容类型处理
      const processedContent = await this.processContentByType(attachmentInfo, contentType);

      // 4. 更新处理结果
      updateAttachmentContent(attachmentInfo.id, {
        ...processedContent,
        processing_status: 'completed'
      });

      logger.info('附件内容处理完成', {
        fileUuid: attachmentInfo.id,
        contentType,
        hasRawContent: !!processedContent.raw_content,
        hasBase64Data: !!processedContent.base64_data
      });

    } catch (error) {
      logger.error('附件内容处理失败', { fileUuid: attachmentInfo.id, error: error instanceof Error ? error.message : String(error) });
      updateProcessingStatus(attachmentInfo.id, 'failed', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 根据内容类型处理文件
   */
  private async processContentByType(
    attachmentInfo: AttachmentInfo,
    contentType: 'image' | 'document' | 'text' | 'code'
  ): Promise<Partial<AttachmentContent>> {
    const filePath = path.join(attachmentsDir, attachmentInfo.fileName);

    switch (contentType) {
      case 'image':
        return await this.processImage(filePath);
      case 'document':
        return await this.processDocument(filePath, attachmentInfo.extension);
      case 'text':
      case 'code':
        return await this.processTextFile(filePath);
      default:
        throw new Error(`不支持的内容类型: ${contentType}`);
    }
  }

  /**
   * 处理图片文件
   */
  private async processImage(filePath: string): Promise<Partial<AttachmentContent>> {
    try {
      const fileBuffer = await fs.promises.readFile(filePath);
      const base64Data = fileBuffer.toString('base64');

      return {
        base64_data: base64Data
      };
    } catch (error) {
      throw new Error(`图片处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理文档文件
   */
  private async processDocument(filePath: string, extension: string): Promise<Partial<AttachmentContent>> {
    try {
      const parserType = this.getParserType(extension);

      if (parserType) {
        // 使用外部解析器
        const rawContent = await this.parseDocumentFile(filePath, parserType);
        return {
          raw_content: rawContent,
          parsed_content: JSON.stringify({
            type: 'parsed_document',
            parser: parserType,
            contentLength: rawContent.length
          })
        };
      } else {
        throw new Error(`不支持的文档类型: ${extension}`);
      }
    } catch (error) {
      throw new Error(`文档处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理文本/代码文件
   */
  private async processTextFile(filePath: string): Promise<Partial<AttachmentContent>> {
    try {
      const rawContent = await fs.promises.readFile(filePath, 'utf-8');
      const extension = path.extname(filePath).toLowerCase();

      // 检测编程语言
      const language = this.detectLanguage(extension);

      return {
        raw_content: rawContent,
        parsed_content: JSON.stringify({
          type: 'text_file',
          language: language,
          lineCount: rawContent.split('\n').length,
          charCount: rawContent.length
        })
      };
    } catch (error) {
      throw new Error(`文本文件处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取解析器类型
   */
  private getParserType(fileExtension: string): 'doc' | 'excel' | 'ppt' | 'pdf' | null {
    // const docExtensions = ['.doc', '.docx'];
    // const excelExtensions = ['.xls', '.xlsx', '.xlsm', '.xlsb'];

    if (SUPPORT_DOC_EXTENSIONS.includes(fileExtension)) {
      return 'doc';
    } else if (SUPPORT_EXCEL_EXTENSIONS.includes(fileExtension)) {
      return 'excel';
    } else if (SUPPORT_PPT_EXTENSIONS.includes(fileExtension)) {
      return 'ppt';
    } else if (SUPPORT_PDF_EXTENSIONS.includes(fileExtension)) {
      return 'pdf';
    }

    return null;
  }

  /**
   * 调用外部解析器解析文档
   */
  private async parseDocumentFile(filePath: string, parserType: 'doc' | 'excel' | 'ppt' | 'pdf'): Promise<string> {
    const baseDir = getParserDir()
    const parserPath = path.join(baseDir, getParserExeName(parserType));

    // 检查解析器是否存在
    if (!fs.existsSync(parserPath)) {
      throw new Error(`解析器不存在: ${parserPath}`);
    }

    try {
      const args = [filePath];

      // 根据解析器类型添加特定参数
      if (parserType === 'doc') {
        args.push('-f', 'text'); // 输出纯文本格式
      } else if (parserType === 'excel') {
        args.push('-f', 'csv'); // 输出CSV格式
      } else if (parserType === 'ppt') {
        args.push('-f', 'text'); // 输出纯文本格式
      } else if (parserType === 'pdf') {
        args.push('-f', 'text'); // 输出纯文本格式
      }

      logger.info('调用解析器', { parserPath, filePath, args });

      const { stdout, stderr } = await execFileAsync(parserPath, args, {
        timeout: 30000, // 30秒超时
        maxBuffer: 10 * 1024 * 1024, // 10MB缓冲区
        encoding: 'utf8'
      });

      if (stderr) {
        logger.warn('解析器stderr输出', { stderr });
      }

      if (!stdout.trim()) {
        throw new Error('解析器返回空内容');
      }

      return stdout.trim();

    } catch (error: any) {
      if (error.code === 'ENOENT') {
        throw new Error(`解析器可执行文件不存在或无权限: ${parserPath}`);
      } else if (error.signal === 'SIGTERM') {
        throw new Error('解析器执行超时');
      } else if (error.code === 'EMSGSIZE') {
        throw new Error('文档内容过大，超出处理限制');
      } else {
        throw new Error(`解析器执行失败: ${error.message}`);
      }
    }
  }

  /**
   * 检测编程语言
   */
  private detectLanguage(extension: string): string {
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.vue': 'vue',
      '.svelte': 'svelte',
      '.py': 'python',
      '.pyw': 'python',
      '.java': 'java',
      '.c': 'c',
      '.h': 'c',
      '.cpp': 'cpp',
      '.hpp': 'cpp',
      '.cc': 'cpp',
      '.cxx': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.lua': 'lua',
      '.dart': 'dart',
      '.json': 'json',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.md': 'markdown',
      '.sh': 'bash',
      '.sql': 'sql'
    };

    return languageMap[extension.toLowerCase()] || 'text';
  }
}

// 导出单例实例
export const contentProcessor = new ContentProcessor();
