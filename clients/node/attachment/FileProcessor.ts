import { AttachmentInfo } from '../db/messages';
import path from 'path';
import fs from 'fs';
import { execFile } from 'child_process';
import { promisify } from 'util';
import logger from '../utils/logger';
import os from 'os';
import { SUPPORT_DOC_EXTENSIONS, SUPPORT_EXCEL_EXTENSIONS, SUPPORT_IMAGE_EXTENSIONS, SUPPORT_PDF_EXTENSIONS, SUPPORT_PPT_EXTENSIONS, SUPPORT_TEXT_EXTENSIONS } from './constant';
import { getParserDir, getParserExeName } from '../utils/parserScripts';

const execFileAsync = promisify(execFile);
const attachmentsDir = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'attachments');

const readTextFile = async (fileName: string) => {
  const filePath = path.join(attachmentsDir, fileName);
  if (!fs.existsSync(filePath)) {
    throw new Error('文件不存在');
  }
  const fileBuffer = await fs.promises.readFile(filePath);
  return fileBuffer.toString('utf-8');
};

/**
 * 读取附件文件为base64格式
 */
const readAttachmentFile = async (fileName: string) => {
  const filePath = path.join(attachmentsDir, fileName);

  if (!fs.existsSync(filePath)) {
    throw new Error('文件不存在');
  }

  const fileBuffer = await fs.promises.readFile(filePath);
  const base64Data = fileBuffer.toString('base64');

  // 获取文件的MIME类型
  const extension = path.extname(fileName).toLowerCase();
  let mimeType = 'application/octet-stream';

  const mimeTypes: { [key: string]: string } = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp'
  };

  if (mimeTypes[extension]) {
    mimeType = mimeTypes[extension];
  }

  return {
    fileName,
    mimeType,
    base64Data,
    size: fileBuffer.length
  };
};

/**
 * 获取解析器类型
 */
const getParserType = (fileExtension: string): 'doc' | 'excel' | 'ppt' | 'pdf' | null => {
  // const docExtensions = SUPPORT_DOC_EXTENSIONS;
  // const excelExtensions = ['.xls', '.xlsx', '.xlsm', '.xlsb'];

  if (SUPPORT_DOC_EXTENSIONS.includes(fileExtension)) {
    return 'doc';
  } else if (SUPPORT_EXCEL_EXTENSIONS.includes(fileExtension)) {
    return 'excel';
  } else if (SUPPORT_PPT_EXTENSIONS.includes(fileExtension)) {
    return 'ppt';
  } else if (SUPPORT_PDF_EXTENSIONS.includes(fileExtension)) {
    return 'pdf';
  }

  return null;
};

/**
 * 获取解析器可执行文件路径
 */
const getParserPath = (parserType: 'doc' | 'excel' | 'ppt' | 'pdf'): string => {
  const baseDir = getParserDir()
  return path.join(baseDir, getParserExeName(parserType));
};

/**
 * 调用外部解析器解析文档
 */
const parseDocumentFile = async (filePath: string, parserType: 'doc' | 'excel' | 'ppt' | 'pdf'): Promise<string> => {
  const parserPath = getParserPath(parserType);

  // 检查解析器是否存在
  if (!fs.existsSync(parserPath)) {
    throw new Error(`解析器不存在: ${parserPath}`);
  }

  try {
    const args = [filePath];

    // 根据解析器类型添加特定参数
    if (parserType === 'doc') {
      args.push('-f', 'text'); // 输出纯文本格式
    } else if (parserType === 'excel') {
      args.push('-f', 'csv'); // 输出CSV格式
    } else if (parserType === 'ppt') {
      args.push('-f', 'text'); // 输出纯文本格式
    } else if (parserType === 'pdf') {
      args.push('-f', 'text'); // 输出纯文本格式
    }

    logger.info('调用解析器', { parserPath, filePath, args });

    const { stdout, stderr } = await execFileAsync(parserPath, args, {
      timeout: 30000, // 30秒超时
      maxBuffer: 10 * 1024 * 1024, // 10MB缓冲区
      encoding: 'utf8'
    });

    if (stderr) {
      logger.warn('解析器stderr输出', { stderr });
    }

    if (!stdout.trim()) {
      throw new Error('解析器返回空内容');
    }

    return stdout.trim();

  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new Error(`解析器可执行文件不存在或无权限: ${parserPath}`);
    } else if (error.signal === 'SIGTERM') {
      throw new Error('解析器执行超时');
    } else if (error.code === 'EMSGSIZE') {
      throw new Error('文档内容过大，超出处理限制');
    } else {
      throw new Error(`解析器执行失败: ${error.message}`);
    }
  }
};

/**
 * 解析文档内容
 */
const parseDocumentContent = async (fileName: string) => {
  const filePath = path.join(attachmentsDir, fileName);

  if (!fs.existsSync(filePath)) {
    throw new Error('文件不存在');
  }

  const fileExtension = path.extname(fileName).toLowerCase();
  const parserType = getParserType(fileExtension);

  if (!parserType) {
    throw new Error(`不支持的文件类型: ${fileExtension}`);
  }

  const parsedContent = await parseDocumentFile(filePath, parserType);

  return {
    fileName,
    fileExtension,
    parserType,
    content: parsedContent,
    contentLength: parsedContent.length
  };
};

export interface ProcessedFileContent {
  fileName: string;
  originalName: string;
  type: 'image' | 'document' | 'excel' | 'text' | 'pdf' | 'ppt' | 'unknown';
  content: string;
  base64Content?: string; // 图片的base64内容
  error?: string;
}

export interface FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean;
  process(file: AttachmentInfo): Promise<ProcessedFileContent>;
  isAsync(): boolean;
}

/**
 * 图片文件处理器
 */
export class ImageProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    // const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return SUPPORT_IMAGE_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await readAttachmentFile(file.fileName);
      const base64Content = `data:${result.mimeType};base64,${result.base64Data}`;

      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'image',
        content: '', // 图片不包含文本内容
        base64Content
      };
    } catch (error: any) {
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'image',
        content: '',
        error: `图片处理失败: ${error.message}`
      };
    }
  }

  isAsync(): boolean {
    return false; // 图片处理比较快，同步处理
  }
}

/**
 * 文档文件处理器（Word文档）
 */
export class DocumentProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    return SUPPORT_DOC_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await parseDocumentContent(file.fileName);

      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'document',
        content: result.content
      };
    } catch (error: any) {
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'document',
        content: '',
        error: `文档处理失败: ${error.message}`
      };
    }
  }

  isAsync(): boolean {
    return true; // 文档解析比较慢，异步处理
  }
}

/**
 * Excel文件处理器
 */
export class ExcelProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    return SUPPORT_EXCEL_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await parseDocumentContent(file.fileName);

      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'excel',
        content: result.content
      };
    } catch (error: any) {
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'excel',
        content: '',
        error: `Excel处理失败: ${error.message}`
      };
    }
  }

  isAsync(): boolean {
    return true; // Excel解析比较慢，异步处理
  }
}

export class TextProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    return SUPPORT_TEXT_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await readTextFile(file.fileName);

      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'text',
        content: `[文件: ${file.originalName}]\n${result}`,
      };
    } catch (e) {
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'text',
        content: '',
        error: `文本处理失败: ${e}`
      }
    }

  }

  isAsync(): boolean {
    return false; // 文本处理比较快，同步处理
  }
}

export class PdfProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    return SUPPORT_PDF_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await parseDocumentContent(file.fileName);
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'pdf',
        content: result.content
      };
    } catch (e) {
      logger.error('PDF处理失败', { fileName: file.fileName, error: e });
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'pdf',
        content: '',
        error: `PDF处理失败: ${e}`
      }
    }
  }

  isAsync(): boolean {
    return false; // PDF处理比较快，同步处理
  }
}

export class PptProcessor implements FileProcessingStrategy {
  canProcess(file: AttachmentInfo): boolean {
    return SUPPORT_PPT_EXTENSIONS.includes(file.extension.toLowerCase());
  }

  async process(file: AttachmentInfo): Promise<ProcessedFileContent> {
    try {
      const result = await parseDocumentContent(file.fileName);
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'ppt',
        content: result.content
      };
    } catch (e) {
      logger.error('PPT处理失败', { fileName: file.fileName, error: e });
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'ppt',
        content: '',
        error: `PPT处理失败: ${e}`
      }
    }
  }

  isAsync(): boolean {
    return false; // PPT处理比较快，同步处理
  }
}

/**
 * 文件处理器管理类
 */
export class FileProcessorManager {
  private processors: FileProcessingStrategy[] = [
    new ImageProcessor(),
    new DocumentProcessor(),
    new ExcelProcessor(),
    new TextProcessor(),
    new PdfProcessor(),
    new PptProcessor(),
  ];

  /**
   * 获取文件的处理器
   */
  getProcessor(file: AttachmentInfo): FileProcessingStrategy | null {
    return this.processors.find(processor => processor.canProcess(file)) || null;
  }

  /**
   * 处理单个文件
   */
  async processFile(file: AttachmentInfo): Promise<ProcessedFileContent> {
    const processor = this.getProcessor(file);

    if (!processor) {
      return {
        fileName: file.fileName,
        originalName: file.originalName,
        type: 'unknown',
        content: '',
        error: `不支持的文件类型: ${file.extension}`
      };
    }

    return await processor.process(file);
  }

  /**
   * 批量处理文件
   */
  async processFiles(files: AttachmentInfo[]): Promise<ProcessedFileContent[]> {
    const results: ProcessedFileContent[] = [];

    // 先处理同步文件（如图片）
    const syncFiles = files.filter(file => {
      const processor = this.getProcessor(file);
      return processor && !processor.isAsync();
    });

    // 再处理异步文件（如文档）
    const asyncFiles = files.filter(file => {
      const processor = this.getProcessor(file);
      return processor && processor.isAsync();
    });

    // 同步处理
    for (const file of syncFiles) {
      const result = await this.processFile(file);
      results.push(result);
    }

    // 异步并行处理
    if (asyncFiles.length > 0) {
      const asyncResults = await Promise.allSettled(
        asyncFiles.map(file => this.processFile(file))
      );

      asyncResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          const file = asyncFiles[index];
          results.push({
            fileName: file.fileName,
            originalName: file.originalName,
            type: 'unknown',
            content: '',
            error: `处理失败: ${result.reason}`
          });
        }
      });
    }

    return results;
  }

  /**
   * 将处理结果格式化为发送给AI的文本
   */
  formatProcessedContent(processedFiles: ProcessedFileContent[]): string {
    if (processedFiles.length === 0) return '';

    const sections: string[] = [];

    processedFiles.forEach((file, index) => {
      sections.push(`\n--- 附件 ${index + 1}: ${file.originalName} ---`);

      if (file.error) {
        sections.push(`❌ ${file.error}`);
      } else if (file.type === 'image') {
        sections.push(`🖼️ 图片文件已上传，请分析图片内容。`);
        // 注意：base64内容不包含在文本中，会通过其他方式发送给多模态模型
      } else if (file.content) {
        sections.push(file.content);
      } else {
        sections.push('📄 文件已上传但内容为空');
      }
    });

    return sections.join('\n');
  }

  /**
   * 获取所有图片的base64内容
   */
  getImageContents(processedFiles: ProcessedFileContent[]): string[] {
    return processedFiles
      .filter(file => file.type === 'image' && file.base64Content && !file.error)
      .map(file => file.base64Content!);
  }
}
