import { AttachmentInfo } from '../db/messages';
import { MultiModalContent, TextContent, ImageContent, DocumentContent } from '../chat/ChatClient';
import { FileProcessorManager } from './FileProcessor';
import logger from '../utils/logger';

/**
 * 附件转换器 - 将AttachmentInfo转换为ChatClient可用的MultiModalContent
 */
export class AttachmentConverter {
  private fileProcessor: FileProcessorManager;

  constructor() {
    this.fileProcessor = new FileProcessorManager();
  }

  /**
   * 将附件数组转换为多模态内容数组
   */
  async convertAttachmentsToMultiModal(attachments: AttachmentInfo[]): Promise<MultiModalContent[]> {
    if (!attachments || attachments.length === 0) {
      return [];
    }

    logger.info('开始转换附件为多模态内容', {
      count: attachments.length,
      files: attachments.map(f => ({ name: f.originalName, type: f.extension }))
    });

    const multiModalContents: MultiModalContent[] = [];

    try {
      // 使用FileProcessor处理所有附件
      const processedFiles = await this.fileProcessor.processFiles(attachments);

      for (const processedFile of processedFiles) {
        if (processedFile.error) {
          logger.warn('跳过处理失败的附件', {
            fileName: processedFile.originalName,
            error: processedFile.error
          });
          continue;
        }

        const originalAttachment = attachments.find(a => a.fileName === processedFile.fileName);
        if (!originalAttachment) {
          logger.warn('找不到对应的原始附件信息', { fileName: processedFile.fileName });
          continue;
        }

        const multiModalContent = this.createMultiModalContent(processedFile, originalAttachment);
        if (multiModalContent) {
          multiModalContents.push(multiModalContent);
        }
      }

      logger.info('附件转换完成', {
        原始附件数: attachments.length,
        成功转换数: multiModalContents.length,
        失败数: processedFiles.filter(f => f.error).length
      });

      return multiModalContents;

    } catch (error: any) {
      logger.error('附件转换过程中发生错误', error);
      throw new Error(`附件转换失败: ${error.message}`);
    }
  }

  /**
   * 根据处理结果创建对应的多模态内容
   */
  private createMultiModalContent(
    processedFile: any,
    originalAttachment: AttachmentInfo
  ): MultiModalContent | null {
    const baseMetadata = {
      filename: originalAttachment.originalName,
      size: originalAttachment.size,
      mimeType: this.getMimeType(originalAttachment.extension)
    };

    switch (processedFile.type) {
      case 'image':
        return this.createImageContent(processedFile, baseMetadata);

      case 'document':
      case 'excel':
      case 'ppt':
      case 'pdf':
        return this.createDocumentContent(processedFile, baseMetadata, originalAttachment);

      case 'text':
        return this.createTextContent(processedFile, baseMetadata, originalAttachment);

      default:
        logger.warn('不支持的文件类型', {
          type: processedFile.type,
          filename: originalAttachment.originalName
        });
        return null;
    }
  }


  private createTextContent(processedFile: any, baseMetadata: any, originalAttachment: AttachmentInfo): TextContent {
    return {
      type: 'text',
      content: processedFile.content,
      metadata: {
        ...baseMetadata
      }
    };
  }

  /**
   * 创建图片内容
   */
  private createImageContent(processedFile: any, baseMetadata: any): ImageContent {
    return {
      type: 'image',
      content: processedFile.base64Content || processedFile.content,
      metadata: {
        ...baseMetadata,
        width: undefined, // 可以从图片元数据中获取
        height: undefined
      }
    };
  }

  /**
   * 创建文档内容
   */
  private createDocumentContent(
    processedFile: any,
    baseMetadata: any,
    originalAttachment: AttachmentInfo
  ): DocumentContent {
    const originalFormat = this.getDocumentFormat(originalAttachment.extension);

    return {
      type: 'document',
      content: processedFile.content,
      metadata: {
        ...baseMetadata
      },
      originalFormat,
      filePath: [originalAttachment.path]
    };
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      // 图片
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',

      // 文档
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.txt': 'text/plain',
      '.md': 'text/markdown'
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * 根据文件扩展名获取文档格式
   */
  private getDocumentFormat(extension: string): 'pdf' | 'docx' | 'txt' | 'md' | 'html' {
    const formatMap: { [key: string]: 'pdf' | 'docx' | 'txt' | 'md' | 'html' } = {
      '.pdf': 'pdf',
      '.doc': 'docx',
      '.docx': 'docx',
      '.txt': 'txt',
      '.md': 'md',
      '.html': 'html'
    };

    return formatMap[extension.toLowerCase()] || 'txt';
  }

  /**
   * 创建包含文本和多模态内容的统一prompt
   */
  createUnifiedPrompt(textPrompt: string, multiModalContents: MultiModalContent[]): (string | MultiModalContent)[] {
    const prompt: (string | MultiModalContent)[] = [];

    // 添加文本内容
    if (textPrompt && textPrompt.trim()) {
      prompt.push({
        type: 'text',
        content: textPrompt.trim()
      });
    }

    // 添加多模态内容
    if (multiModalContents && multiModalContents.length > 0) {
      prompt.push(...multiModalContents);
    }

    return prompt;
  }
}
