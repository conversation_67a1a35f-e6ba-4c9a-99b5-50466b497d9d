import fs from 'fs';
import os from 'os';
import path from 'path';
import { getMsetPath } from './installScripts';
import { isWin } from './constant';


export function checkDriveExists(driveLetter: string) {
  const drivePath = `${driveLetter}:\\`;
  return fs.existsSync(drivePath);
}


export async function getBinaryName(name: string): Promise<string> {
  if (process.platform === 'win32') {
    if (name === 'node') {
      return `${name}.cmd`;
    }
    return `${name}.exe`;
  }
  return name;
}

export async function getBinaryPath(name?: string): Promise<string> {
  if (!name) {
    const studioName = process.env.STUDIO_NAME as string;
    return path.join(os.homedir(), `.${studioName}`, 'bin');
  }

  const binaryName = await getBinaryName(name);
  const studioName = process.env.STUDIO_NAME as string;
  const binariesDir = path.join(os.homedir(), `.${studioName}`, 'bin');
  const binariesDirExists = await fs.existsSync(binariesDir);
  return binariesDirExists ? path.join(binariesDir, binaryName) : binaryName;
}

export async function isBinaryExists(name: string): Promise<boolean> {
  const cmd = await getBinaryPath(name);
  return await fs.existsSync(cmd);
}

export async function getMsetHomePath(): Promise<string> {
  return path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'mset');
}

export async function isMsetExists(): Promise<boolean> {
  const msetPath = await getMsetHomePath();
  return fs.existsSync(msetPath);
}

export async function getMsetBinary(): Promise<string> {
  const filename = isWin ? 'mset.exe' : 'mset';
  const msetPath = path.join(getMsetPath(), filename);
  return await fs.existsSync(msetPath) ? msetPath : '';
}