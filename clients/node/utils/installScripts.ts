import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import os from 'os';
import logger from './logger';
import { spawn } from 'child_process';
import { isArm64, isMac, isWin } from './constant';

export function getResourcePath() {
  return path.join(app.getAppPath(), 'resources');
}

export function getMsetPath() {
  const winDir = 'windows-mset';

  const macDir = isArm64 ? 'arm64-mset' : 'intel-mset';

  if (app.isPackaged) {
    if (isWin) {
      return path.join(process.resourcesPath, 'app.asar.unpacked', 'external', winDir);
    } else if (isMac) {
      return path.join(process.resourcesPath, 'app.asar.unpacked', 'external', macDir);
    }
  } else {
    if (isWin) {
      return path.join(app.getAppPath(), 'external', winDir);
    } else if (isMac) {
      return path.join(app.getAppPath(), 'external', macDir);
    }
  }
  return '';
}

export function runInstallScript(scriptPath: string, execPath?: string, args?: string[]): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    const installScriptPath = path.join(getResourcePath(), 'scripts', scriptPath);
    logger.info(`Running script at: ${installScriptPath}`);
    logger.info(`args: ${args}`);
    logger.info(`process.execPath: ${process.execPath}`);

    const currExecPath = execPath || process.execPath;

    const nodeProcess = spawn(currExecPath, [installScriptPath, ...(args || [])], {
      env: { ...process.env, ELECTRON_RUN_AS_NODE: '1' }
    });

    nodeProcess.stdout.on('data', (data) => {
      logger.info(`Script output: ${data}`)
    });

    nodeProcess.stderr.on('data', (data) => {
      logger.error(`Script error: ${data}`)
    });

    nodeProcess.on('close', (code) => {
      if (code === 0) {
        logger.info('Script completed successfully')
        resolve()
      } else {
        logger.error(`Script exited with code ${code}`)
        reject(new Error(`Process exited with code ${code}`))
      }
    });
  })
}


export function runMsetInstallScript(): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    const file = isWin ? 'install.bat' : 'install.sh';
    const installScriptPath = path.join(getMsetPath(), file);
    logger.info(`Running script at: ${installScriptPath}`);

    let userShell = isWin ? 'powershell.exe' : process.env.SHELL;

    if (!userShell) {
      if (fs.existsSync('/bin/zsh')) {
        userShell = '/bin/zsh'
      } else if (fs.existsSync('/bin/bash')) {
        userShell = '/bin/bash'
      } else if (fs.existsSync('/bin/fish')) {
        userShell = '/bin/fish'
      } else {
        userShell = '/bin/sh'
      }
    }

    const installDir = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'mset');
    if (!fs.existsSync(installDir)) {
      fs.mkdirSync(installDir, { recursive: true });
    }

    const nodeProcess = spawn(userShell, [installScriptPath,
      '--home',
      installDir
    ], {
      env: { ...process.env },
      cwd: path.join(getMsetPath()),
    });

    nodeProcess.stdout.on('data', (data) => {
      logger.info(`Script output: ${data}`)
    });

    nodeProcess.stderr.on('data', (data) => {
      logger.error(`Script error: ${data}`)
    });

    nodeProcess.on('close', (code) => {
      if (code === 0) {
        logger.info('Script completed successfully')
        resolve()
      } else {
        logger.error(`Script exited with code ${code}`)
        reject(new Error(`Process exited with code ${code}`))
      }
    });
  })
}
