import { app } from 'electron';
import fs from 'fs';
import path from 'path';
import logger from './logger';
import { spawn } from 'child_process';
import { isArm64, isMac, isWin } from './constant';


export function getParserExeName(type: 'excel' | 'doc' | 'ppt' | 'pdf') {
  return `${type}-parser${isWin ? '.exe' : ''}`;
}

export function getParserDir() {
  const winDir = 'windows-parser';

  const macDir = isArm64 ? 'arm64-parser' : 'intel-parser';

  if (app.isPackaged) {
    if (isWin) {
      return path.join(process.resourcesPath, 'app.asar.unpacked', 'external', winDir);
    } else if (isMac) {
      return path.join(process.resourcesPath, 'app.asar.unpacked', 'external', macDir);
    }
  } else {
    if (isWin) {
      return path.join(app.getAppPath(), 'external', winDir);
    } else if (isMac) {
      return path.join(app.getAppPath(), 'external', macDir);
    }
  }
  return '';
}

export function runParserScript(filePath: string, type: 'excel' | 'doc' | 'ppt' | 'pdf'): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    let scriptPath = '';
    if (type === 'excel') {
      scriptPath = 'excel-parser';
    } else if (type === 'doc') {
      scriptPath = 'doc-parser';
    }
    const parserPath = path.join(getParserDir(), scriptPath);
    logger.info(`Running script at: ${parserPath}`);


    let userShell = isWin ? 'powershell.exe' : process.env.SHELL;

    if (!userShell) {
      if (fs.existsSync('/bin/zsh')) {
        userShell = '/bin/zsh'
      } else if (fs.existsSync('/bin/bash')) {
        userShell = '/bin/bash'
      } else if (fs.existsSync('/bin/fish')) {
        userShell = '/bin/fish'
      } else {
        userShell = '/bin/sh'
      }
    }

    const nodeProcess = spawn(userShell, [parserPath, filePath], {
      env: { ...process.env, ELECTRON_RUN_AS_NODE: '1' },
      cwd: path.join(getParserDir())
    });

    nodeProcess.stdout.on('data', (data) => {
      logger.info(`Script output: ${data}`)
    });

    nodeProcess.stderr.on('data', (data) => {
      logger.error(`Script error: ${data}`)
    });

    nodeProcess.on('close', (code) => {
      if (code === 0) {
        logger.info('Script completed successfully')
        resolve()
      } else {
        logger.error(`Script exited with code ${code}`)
        reject(new Error(`Process exited with code ${code}`))
      }
    });
  })
}


// export function runMsetInstallScript(): Promise<void> {
//   return new Promise<void>((resolve, reject) => {
//     const file = isWin ? 'install.bat' : 'install.sh';
//     const installScriptPath = path.join(getMsetPath(), file);
//     logger.info(`Running script at: ${installScriptPath}`);

//     let userShell = isWin ? 'powershell.exe' : process.env.SHELL;

//     if (!userShell) {
//       if (fs.existsSync('/bin/zsh')) {
//         userShell = '/bin/zsh'
//       } else if (fs.existsSync('/bin/bash')) {
//         userShell = '/bin/bash'
//       } else if (fs.existsSync('/bin/fish')) {
//         userShell = '/bin/fish'
//       } else {
//         userShell = '/bin/sh'
//       }
//     }

//     const installDir = path.join(os.homedir(), `.${process.env.STUDIO_NAME}`, 'mset');
//     if (!fs.existsSync(installDir)) {
//       fs.mkdirSync(installDir, { recursive: true });
//     }

//     const nodeProcess = spawn(userShell, [installScriptPath,
//       '--home',
//       installDir
//     ], {
//       env: { ...process.env },
//       cwd: path.join(getMsetPath()),
//     });

//     nodeProcess.stdout.on('data', (data) => {
//       logger.info(`Script output: ${data}`)
//     });

//     nodeProcess.stderr.on('data', (data) => {
//       logger.error(`Script error: ${data}`)
//     });

//     nodeProcess.on('close', (code) => {
//       if (code === 0) {
//         logger.info('Script completed successfully')
//         resolve()
//       } else {
//         logger.error(`Script exited with code ${code}`)
//         reject(new Error(`Process exited with code ${code}`))
//       }
//     });
//   })
// }
