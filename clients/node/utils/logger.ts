// 使用 electron-log 作为日志工具
import electronLog from 'electron-log';
import path from 'path';
import os from 'os';
import fs from 'fs';

/**
 * 日志类型枚举
 */
enum LogType {
  INFO = 'info',
  ERROR = 'error',
  WARN = 'warn',
  DEBUG = 'debug',
}

/**
 * 日志工具接口
 */
interface Logger {
  info(...args: any[]): void;
  success(...args: any[]): void;
  warn(...args: any[]): void;
  error(...args: any[]): void;
  debug(...args: any[]): void;
  custom(color?: string, isBold?: boolean, ...args: any[]): void;
  log(...args: any[]): void;
  table(data: any[]): void;
  group(title: string, ...args: any[]): void;
}

// 创建不同类型日志的实例
const loggers: Record<LogType, typeof electronLog> = {
  [LogType.INFO]: electronLog.create({ logId: 'info' }),
  [LogType.ERROR]: electronLog.create({ logId: 'error' }),
  [LogType.WARN]: electronLog.create({ logId: 'warn' }),
  [LogType.DEBUG]: electronLog.create({ logId: 'debug' }),
};

// 配置控制台输出级别
Object.values(loggers).forEach(logger => {
  logger.transports.console.level = 'debug';
});

/**
 * 获取日志存储路径
 * @param logType 日志类型
 * @returns 日志文件路径
 */
const getLogPath = (logType: LogType, sensorsLog = false): string => {
  // 每次调用时创建新的日期对象，确保使用当前日期
  const date = new Date();
  const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  const formattedHour = String(date.getHours()).padStart(2, '0');

  // 根据操作系统确定日志保存路径
  let logPath;
  const studioNAME = process.env.STUDIO_NAME as string;

  if (process.platform === 'darwin') {
    // macOS
    logPath = path.join(os.homedir(), 'Library/Logs', studioNAME, logType);
  } else if (process.platform === 'win32') {
    // Windows
    logPath = path.join(os.homedir(), 'AppData/Roaming', studioNAME, 'logs', logType);
  } else {
    // Linux 或其他系统
    logPath = path.join(os.homedir(), '.config', studioNAME, 'logs', logType);
  }

  // 确保日志目录存在
  fs.mkdirSync(logPath, { recursive: true });

  // 返回包含日期和小时的完整日志文件路径，实现按小时切分
  return sensorsLog ? logPath : path.join(logPath, `${formattedDate}-${formattedHour}.log`);
};

// 配置每种日志类型的文件路径
Object.entries(loggers).forEach(([type, logger]) => {
  logger.transports.file.level = 'info';
  logger.transports.file.resolvePathFn = () => getLogPath(type as LogType);
});

// 添加勾子函数来修改日志内容 (仅用于远程传输)
Object.values(loggers).forEach(logger => {
  logger.hooks.push((message, transport) => {
    // 只处理远程传输的日志
    if (transport !== logger.transports.remote) {
      return message;
    }

    // 为远程日志添加额外信息
    return {
      ...message,
      appVersion: process.env.npm_package_version,
      timestamp: new Date().toISOString(),
      platform: process.platform,
      // 可以添加更多自定义字段
    };
  });
});

// 创建一个适应原接口的 logger
const logger: Logger = {
  /**
   * 输出信息日志
   * @param args 日志参数，支持多个参数
   */
  info: (...args: any[]): void => {
    loggers[LogType.INFO].info(...args);
  },

  /**
   * 输出成功日志
   * @param args 日志参数，支持多个参数
   */
  success: (...args: any[]): void => {
    loggers[LogType.INFO].info('[SUCCESS]', ...args);
  },

  /**
   * 输出警告日志
   * @param args 日志参数，支持多个参数
   */
  warn: (...args: any[]): void => {
    loggers[LogType.WARN].warn(...args);
  },

  /**
   * 输出错误日志
   * @param args 日志参数，支持多个参数
   */
  error: (...args: any[]): void => {
    loggers[LogType.ERROR].error(...args);
  },

  /**
   * 输出调试日志
   * @param args 日志参数，支持多个参数
   */
  debug: (...args: any[]): void => {
    loggers[LogType.DEBUG].debug(...args);
  },

  /**
   * 输出带有自定义颜色的日志
   * @param color 颜色名称 (在 electron-log 中不使用，保留为兼容接口)
   * @param isBold 是否加粗 (在 electron-log 中不使用，保留为兼容接口)
   * @param args 日志参数，支持多个参数
   */
  custom: (color: string = 'white', isBold: boolean = false, ...args: any[]): void => {
    if (!args.length) {
      // 没有参数时，说明第一个参数可能是消息而非颜色
      loggers[LogType.INFO].info(color);
      return;
    }

    // 为了保持一定的区分度，在不同"颜色"下使用不同的日志级别和文件
    switch (color) {
      case 'blue':
        loggers[LogType.INFO].info('[CUSTOM]', ...args);
        break;
      case 'green':
        loggers[LogType.INFO].info('[CUSTOM]', ...args);
        break;
      case 'red':
        loggers[LogType.ERROR].error('[CUSTOM]', ...args);
        break;
      case 'yellow':
        loggers[LogType.WARN].warn('[CUSTOM]', ...args);
        break;
      case 'magenta':
        loggers[LogType.INFO].verbose('[CUSTOM]', ...args);
        break;
      case 'cyan':
        loggers[LogType.DEBUG].debug('[CUSTOM]', ...args);
        break;
      default:
        loggers[LogType.INFO].info('[CUSTOM]', ...args);
    }
  },

  /**
   * 标准日志输出，保持原始console.log行为
   * @param args 日志参数，支持多个参数
   */
  log: (...args: any[]): void => {
    loggers[LogType.INFO].info(...args);
  },

  /**
   * 输出表格数据
   * @param data 表格数据
   */
  table: (data: any[]): void => {
    loggers[LogType.INFO].info('[TABLE]', data);
  },

  /**
   * 输出带有标题的分组日志
   * @param title 分组标题
   * @param args 日志参数，支持多个参数
   */
  group: (title: string, ...args: any[]): void => {
    loggers[LogType.INFO].info(`[GROUP: ${title}]`);
    if (args.length > 0) {
      loggers[LogType.INFO].info(...args);
    }
  }
};

export default logger;
export { getLogPath };
