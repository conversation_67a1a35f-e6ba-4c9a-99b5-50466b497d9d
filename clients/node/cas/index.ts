import axios from '../utils/axios';
import { getMacAddress } from '../utils/mac';
import logger from '../utils/logger';

const decodeBase64 = (str: string) => {
  return Buffer.from(str, 'base64').toString('utf-8');
}

const getGuid = function () {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    S4() +
    S4()
  );
};

// casConfig 类型
export interface CasConfig {
  keyid: string;
  publickey: string;
  routing: string;
  authType: string;
  appid: string;
  token: string;
  transition: string;
  expireTime: number;
}

// Qrcode 类型
export interface Qrcode {
  imgId: string;
  imgUrl: string;
}

// QrcodeResponse 类型
export interface QrcodeResponse {
  success: boolean;
  data: {
    id: string;
    ticket: string;
    userCode: string;
    responseHeaderList: { [key: string]: string }[];
  };
}

export interface LoginResponse {
  success: boolean;
  data: {
    token: string;
    userCode: string;
    userName: string;
  }
}

export class CasClient {
  private casConfig: CasConfig;

  private qrcode: Qrcode | false = false;
  // 获取二维码接口
  public getQrcode = () => {
    const casConfigUrl = `${process.env.BASE_SERVER_MARKET_URL}/cas/getConfig`;
    const qrcodeUrl = `${process.env.BASE_SERVER_MARKET_URL}/cas/qrcode`;
    return new Promise<Qrcode | false>((resolve, reject) => {
      axios.get(casConfigUrl, {
        proxy: false,
      }).then((res) => {
        // console.log('getQrcode', res.data);
        if (res.data) {
          this.casConfig = res.data;
          const routing = this.casConfig.routing;
          logger.info(`获取二维码配置成功, 开始获取二维码`);
          axios.get(qrcodeUrl, {
            params: {
              routing,
            },
            proxy: false,
          }).then((res) => {
            if (res.data.data) {
              logger.info(`获取二维码成功, 开始获取二维码状态`);
              resolve({
                imgId: res.data.data.id as string,
                imgUrl: res.data.data.img as string,
              });
              this.qrcode = {
                imgId: res.data.data.id as string,
                imgUrl: res.data.data.img as string,
              };
            } else {
              logger.info(`获取二维码失败`);
              resolve(false);
              this.qrcode = false;
            }
          })
        } else {
          logger.info(`获取二维码配置失败`);
          resolve(false);
          this.qrcode = false;
        }
      }).catch((res) => {
        logger.info(`获取二维码配置失败`, res);
        resolve(false);
        this.qrcode = false;
      });
    });
  }

  // 获取二维码的状态
  public getQrcodeStatus = () => {
    const qrcodeStatusUrl = `${process.env.BASE_SERVER_MARKET_URL}/cas/jtqrcode`;
    return new Promise<LoginResponse>((resolve, reject) => {
      const falseResult: LoginResponse = {
        success: false,
        data: {
          token: '',
          userCode: '',
          userName: '',
        },
      }
      if (this.qrcode) {
        logger.info(`获取二维码状态接口: ${qrcodeStatusUrl}`);
        axios.get<QrcodeResponse>(qrcodeStatusUrl, {
          params: {
            id: this.qrcode.imgId,
            routing: this.casConfig.routing
          },
          proxy: false,
        }).then((res) => {
          logger.info(`获取二维码状态成功, 开始登录`, res.data);

          if (res.data.data) {
            const { ticket, userCode } = res.data.data;
            logger.info(`获取二维码状态成功, 开始登录`, res.data.data);
            if (ticket && userCode) {
              const loginUrl = `${process.env.TJ_URL}/login/cas3.0`;
              logger.info(`登录接口: ${loginUrl}`);
              const params = {
                st: ticket,
                appKey: process.env.TJ_APP_KEY,
                appSecret: process.env.TJ_APP_SECRET,
                deviceId: getMacAddress(),
                service: typeof this.qrcode === 'object' ? atob(atob(this.qrcode.imgId)) : process.env.CAS_SERVICE_ID,
                isReturnCallBackResult: true,
              }
              logger.info(`登录接口参数`, params);
              axios.post(loginUrl, params, {
                proxy: false
              }).then((res) => {
                logger.info(`登录接口返回结果`, res.data);
                if (res.data.obj && res.data.obj.token) {
                  logger.info(`登录成功, 开始返回结果`);
                  resolve({
                    success: true,
                    data: {
                      token: res.data.obj.token,
                      userCode: res.data.obj.userId,
                      userName: res.data.obj.userName,
                    }
                  })
                } else {
                  logger.info(`登录失败`);
                  resolve(falseResult);
                }
              });
            } else {
              logger.info(`登录失败`);
              resolve(falseResult);
            }

          } else {
            logger.info(`获取二维码状态失败`);
            resolve(falseResult);
          }
        });
      } else {
        logger.info(`获取二维码失败`);
        resolve(falseResult);
      }
    });
  }

  // 检查token是否有效
  public checkToken = (token: string) => {
    const checkTokenUrl = `${process.env.TJ_URL}/login/check_token`;
    return new Promise<boolean>((resolve, reject) => {
      axios.get(checkTokenUrl, {
        proxy: false,
        headers: {
          'token': token,
        }
      }).then((res) => {
        if (res.data.obj && res.data.success) {
          resolve(true);
        } else {
          resolve(false);
        }
      })
    })
  }

  // 退出登录
  public logout = (token: string) => {
    const logoutUrl = `${process.env.TJ_URL}/login/logout`;
    return new Promise<boolean>((resolve, reject) => {
      axios.get(logoutUrl, {
        headers: {
          'token': token,
        },
        proxy: false
      }).then((res) => {
        if (res.data.success) {
          resolve(true);
        } else {
          resolve(false);
        }
      })
    })
  }

}
