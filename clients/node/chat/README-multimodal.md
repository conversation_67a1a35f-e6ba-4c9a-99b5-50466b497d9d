# ChatClient 多模态支持使用指南

## 概述

`ChatClient` 现在支持多模态输入，包括文本、图片、音频、视频、文件、文档和网页内容。这个重构保持了向后兼容性，同时扩展了对多种模态内容的支持。

## 新增类型定义

### 基础类型

- `TextContent` - 文本内容
- `ImageContent` - 图片内容
- `AudioContent` - 音频内容
- `VideoContent` - 视频内容
- `FileContent` - 文件内容
- `DocumentContent` - 文档内容
- `WebContent` - 网页内容

### 统一接口

- `UnifiedPromptPart` - 统一的提示词部分类型（向后兼容）
- `MultiModalPromptPart` - 多模态提示词部分类型

## 使用示例

### 1. 纯文本消息（向后兼容）

```typescript
const chatClient = new ChatClient(/* ... */);

// 传统方式 - 仍然支持
const prompt = [
  {
    type: 'text',
    content: '请帮我分析这段代码',
  },
];

await chatClient.chat(prompt);
```

### 2. 图片分析

```typescript
const prompt = [
  {
    type: 'text',
    content: '请分析这张图片中的内容',
  },
  {
    type: 'image',
    content: 'base64EncodedImageData', // base64编码的图片数据
    imageUrl: 'https://example.com/image.jpg', // 可选：图片URL
    metadata: {
      filename: 'screenshot.png',
      size: 1024000,
      mimeType: 'image/png',
      width: 1920,
      height: 1080,
      quality: 'high',
    },
  },
];

await chatClient.chat(prompt);
```

### 3. 音频文件处理

```typescript
const prompt = [
  {
    type: 'text',
    content: '请分析这个音频文件的内容',
  },
  {
    type: 'audio',
    content: 'base64EncodedAudioData',
    audioUrl: 'https://example.com/audio.mp3',
    format: 'mp3',
    metadata: {
      filename: 'interview.mp3',
      duration: 300, // 5分钟
      mimeType: 'audio/mpeg',
    },
  },
];

await chatClient.chat(prompt);
```

### 4. 视频内容分析

```typescript
const prompt = [
  {
    type: 'text',
    content: '请总结这个视频的主要内容',
  },
  {
    type: 'video',
    content: 'base64EncodedVideoData',
    videoUrl: 'https://example.com/video.mp4',
    format: 'mp4',
    metadata: {
      filename: 'presentation.mp4',
      duration: 600, // 10分钟
      width: 1920,
      height: 1080,
      quality: 'high',
    },
  },
];

await chatClient.chat(prompt);
```

### 5. 文档内容分析

```typescript
const prompt = [
  {
    type: 'text',
    content: '请总结这个文档的主要内容',
  },
  {
    type: 'document',
    content: '文档的文本内容...',
    originalFormat: 'pdf',
    filePath: ['/path/to/document.pdf'],
    metadata: {
      filename: 'report.pdf',
      size: 2048000,
      mimeType: 'application/pdf',
    },
  },
];

await chatClient.chat(prompt);
```

### 6. 网页内容分析

```typescript
const prompt = [
  {
    type: 'text',
    content: '请分析这个网页的内容',
  },
  {
    type: 'web',
    content: '网页的HTML内容...',
    url: 'https://example.com/article',
    title: '示例文章标题',
    metadata: {
      mimeType: 'text/html',
    },
  },
];

await chatClient.chat(prompt);
```

### 7. 混合多模态内容

```typescript
const prompt = [
  {
    type: 'text',
    content: '请分析以下内容并提供综合报告：',
  },
  {
    type: 'image',
    content: 'base64ImageData',
    metadata: {
      filename: 'chart.png',
      quality: 'high',
    },
  },
  {
    type: 'document',
    content: 'PDF文档内容...',
    originalFormat: 'pdf',
    metadata: {
      filename: 'data.pdf',
    },
  },
  {
    type: 'web',
    content: '网页数据...',
    url: 'https://example.com/news',
    title: '相关新闻',
  },
];

await chatClient.chat(prompt);
```

## API 支持情况

### OpenAI API

- ✅ 文本内容：完全支持
- ✅ 图片内容：支持 base64 和 URL 格式
- ⚠️ 音频内容：转换为文本描述
- ⚠️ 视频内容：转换为文本描述
- ✅ 文件/文档内容：作为文本处理
- ✅ 网页内容：作为文本处理

### Anthropic API

- ✅ 文本内容：完全支持
- ⚠️ 图片内容：转换为文本描述（当前版本限制）
- ⚠️ 音频内容：转换为文本描述
- ⚠️ 视频内容：转换为文本描述
- ✅ 文件/文档内容：作为文本处理
- ✅ 网页内容：作为文本处理

## 重要特性

### 向后兼容性

- 现有的 `PromptPart[]` 类型仍然完全支持
- 不会破坏现有代码

### 智能内容处理

- 自动检测多模态内容
- 根据 API 类型（OpenAI/Anthropic）智能转换格式
- 优雅的降级处理（不支持的模态转换为文本描述）

### 类型安全

- 完整的 TypeScript 类型定义
- 编译时类型检查
- 智能代码补全

## 扩展指南

如果需要添加新的模态类型，请按以下步骤操作：

1. 在 `MultiModalContent` 联合类型中添加新接口
2. 在 `processOpenAIMultiModalContent` 方法中添加处理逻辑
3. 在 `processAnthropicMultiModalContent` 方法中添加处理逻辑
4. 更新 `isMultiModalContent` 方法的检测逻辑

## 注意事项

1. 大型媒体文件建议先压缩或使用 URL 引用
2. 注意 API 的请求大小限制
3. 音频和视频内容目前主要通过文本描述处理
4. 不同 API 提供商的多模态支持程度不同
