import { Model, Provider } from '../db/provider';
import { Message, saveMessage, AttachmentInfo } from '../db/messages';
import { OpenAI } from 'openai';
import { Anthropic } from '@anthropic-ai/sdk';
import { BrowserWindow } from 'electron';
import { Tool } from '@modelcontextprotocol/sdk/types';
import logger from '../utils/logger';
import { ModelAdapter } from './ModelAdapter';
import { Stream } from 'openai/streaming';
import { MessageStream } from '@anthropic-ai/sdk/lib/MessageStream';
import { trackEvent } from '../sa/sa';
import { getUserFromDb } from '../db/user';
import { getAttachmentContentsByUuids } from '../db/attachmentContents';
import { message } from 'antd';

export interface ToolCall {
  id: string;
  index: number;
  function: {
    name: string;
    arguments: string;
  }
}

// 基础媒体内容接口
export interface BaseMediaContent {
  type: string;
  content: string;
  metadata?: {
    filename?: string;
    size?: number;
    mimeType?: string;
    duration?: number; // 音视频时长（秒）
    width?: number;    // 图片/视频宽度
    height?: number;   // 图片/视频高度
    quality?: 'low' | 'medium' | 'high'; // 媒体质量
  };
}

// 文本内容
export interface TextContent extends BaseMediaContent {
  type: 'text';
  content: string;
}

// 图片内容
export interface ImageContent extends BaseMediaContent {
  type: 'image';
  content: string; // base64编码的图片数据或URL
  imageUrl?: string; // 图片URL（可选）
}

// 音频内容
export interface AudioContent extends BaseMediaContent {
  type: 'audio';
  content: string; // base64编码的音频数据或URL
  audioUrl?: string; // 音频URL（可选）
  format?: 'mp3' | 'wav' | 'flac' | 'opus'; // 音频格式
}

// 视频内容
export interface VideoContent extends BaseMediaContent {
  type: 'video';
  content: string; // base64编码的视频数据或URL
  videoUrl?: string; // 视频URL（可选）
  format?: 'mp4' | 'webm' | 'avi' | 'mov'; // 视频格式
}

// 文件内容
export interface FileContent extends BaseMediaContent {
  type: 'file';
  content: string; // 文件内容或路径
  filePath?: string[];
  encoding?: 'utf8' | 'base64' | 'binary'; // 文件编码方式
}

// 文档内容（特殊的文件类型）
export interface DocumentContent extends BaseMediaContent {
  type: 'document';
  content: string; // 文档内容（通常是提取的文本）
  originalFormat?: 'pdf' | 'docx' | 'txt' | 'md' | 'html'; // 原始格式
  filePath?: string[];
}

// 网页内容
export interface WebContent extends BaseMediaContent {
  type: 'web';
  content: string; // 网页内容
  url?: string; // 网页URL
  title?: string; // 网页标题
}

// 联合类型定义
export type MultiModalContent =
  | TextContent
  | ImageContent
  | AudioContent
  | VideoContent
  | FileContent
  | DocumentContent
  | WebContent;

// 保持向后兼容的PromptPart定义
export interface PromptPart {
  type: 'text' | 'file' | 'image';
  content: string;
  filePath?: string[];
}

// 新的多模态PromptPart定义
export interface MultiModalPromptPart {
  type: MultiModalContent['type'];
  content: string;
  filePath?: string[];
  imageUrl?: string;
  audioUrl?: string;
  videoUrl?: string;
  url?: string;
  title?: string;
  metadata?: BaseMediaContent['metadata'];
  encoding?: 'utf8' | 'base64' | 'binary';
  format?: string;
  originalFormat?: string;
}

// 统一的PromptPart类型（向后兼容）
export type UnifiedPromptPart = string | MultiModalPromptPart[];

const FUNCTION_CALLING_SPECIAL_LOGIC_MODEL = [
  'aiplat/Qwen3-235B-A22B',
  'aiplat/Qwen3-30B-A3B',
  'aiplat/Qwen3-32B',
  'aiplat/qwq:32b'
];

const DISABLED_THINKING_MODEL = [
  'Qwen/Qwen3-235B-A22B',
  'aiplat/Qwen3-235B-A22B',
  'aiplat/Qwen3-30B-A3B',
  'aiplat/Qwen3-32B',
];

const AIPLAT_DEEPSEEKR1_LIKE_MODEL = [
  'aiplat/deepseek-r1',
  'aiplat/qwq:32b'
];

const GOOGLE_MODDELS = [
  'gemini-2.5-pro',
  'gemini-2.5-flash',
  'gemini-2.0-flash'
]

// 添加 Anthropic 模型列表
const ANTHROPIC_MODELS = [
  // 'claude-3-5-sonnet-20240620',
  // 'claude-3-5-sonnet-20241022',
  // 'claude-3-7-sonnet-20250219',
  // 'claude-3-haiku-20240307',
  // 'claude-3-opus-20240229',
  // 'claude-3-sonnet-20240229',
  // 'claude-opus-4-20250514',
  // 'claude-sonnet-4-20250514',
  // 'claude-3-5-haiku-20241022',
  // 'claude/claude-sonnet-4',
];

export class ChatClient {
  // 大模型 - 支持 OpenAI 和 Anthropic
  private llm: OpenAI | Anthropic | null = null;

  // 添加标识当前使用的 API 类型
  private apiType: 'openai' | 'anthropic' = 'openai';

  // 模型配置
  private modelConfig: Model;

  // 提供商配置
  private providerConfig: Provider;

  // 中断控制器
  private abortController: AbortController | null = null;

  // 多轮会话的历史消息
  private messages: OpenAI.Chat.ChatCompletionMessageParam[] = [];

  // 多轮会话的历史消息
  private historyMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [];

  // 工具
  private tools: Tool[] = [];

  // 系统提示词
  private systemPrompt: string;

  // 会话ID
  private sessionId: number;

  // 温度
  private temperature: number;
  // 最大token
  private maxTokens: number;
  // 上文概率
  private topP: number;

  // 种子
  private seed: number;
  // top_k
  private topK: number;
  // 重复惩罚
  private repetitionPenalty: number;

  private messageId: number;

  private questionId: number;

  private adapter: ModelAdapter;

  private prompt: UnifiedPromptPart;

  // private onmessageCallback: (content: string, toolCalls: ToolCall[]) => void;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[] = [],
    systemPrompt: string = '',
    historyMessages: Message[] = [],
    sessionId: number = 0,
    messageId: number = 0,
    temperature: number = 0.7,
    maxTokens: number = 4096,
    topP: number = 1,
    seed: number = 1234,
    topK: number = 50,
    repetitionPenalty: number = 1,
    questionId: number = 0
  ) {
    this.modelConfig = modelConfig;
    this.providerConfig = providerConfig;
    this.tools = tools;
    this.systemPrompt = systemPrompt || '';
    this.messages = [];
    this.adapter = new ModelAdapter(this.modelConfig.model, this.tools);
    this.sessionId = sessionId;
    this.messageId = messageId;
    this.temperature = temperature;
    this.maxTokens = maxTokens;
    this.topP = topP;
    this.seed = seed;
    this.topK = topK;
    this.repetitionPenalty = repetitionPenalty;
    this.prompt = [];
    this.questionId = questionId;

    // 判断是否为 Anthropic 模型
    this.apiType = this.isAnthropicModel(this.modelConfig.model) ? 'anthropic' : 'openai';

    // 异步初始化历史消息（包含附件内容）
    // this.initHistoryMessages(historyMessages);

    this.init();
    // this.onmessageCallback = () => { };
  }

  // 判断是否为 Anthropic 模型
  private isAnthropicModel(model: string): boolean {
    return ANTHROPIC_MODELS.some(anthropicModel => model.includes(anthropicModel));
  }

  /**
   * 异步初始化历史消息，处理附件内容
   */
  public async initHistoryMessages(historyMessages: Message[]): Promise<void> {
    try {
      if (!historyMessages || historyMessages.length === 0) {
        this.historyMessages = [];
        return;
      }

      // 提取所有消息中的附件UUID
      const allAttachmentUuids: string[] = [];

      for (const message of historyMessages) {
        if (message.attachments) {
          try {
            const attachments: AttachmentInfo[] = JSON.parse(message.attachments);
            attachments.forEach(attachment => {
              if (attachment.id) {
                allAttachmentUuids.push(attachment.id);
              }
            });
          } catch (error) {
            logger.warn('解析消息附件失败', { messageId: message.id, error: error instanceof Error ? error.message : String(error) });
          }
        }
      }

      // 批量查询附件内容
      const attachmentContents = allAttachmentUuids.length > 0
        ? await getAttachmentContentsByUuids(allAttachmentUuids)
        : [];

      // 创建附件内容映射
      const attachmentContentMap = new Map();
      attachmentContents.forEach(content => {
        attachmentContentMap.set(content.file_uuid, content);
      });

      // 处理历史消息，为每个消息增强附件内容
      const enrichedMessages = await Promise.all(
        historyMessages.map(async (message) => {
          if (!message.attachments) {
            return message;
          }

          try {
            const attachments: AttachmentInfo[] = JSON.parse(message.attachments);
            const enrichedAttachments = attachments.map(attachment => {
              const content = attachmentContentMap.get(attachment.id);
              return {
                ...attachment,
                content: content || null
              };
            });

            return {
              ...message,
              attachments: JSON.stringify(enrichedAttachments)
            };
          } catch (error) {
            logger.warn('处理消息附件内容失败', { messageId: message.id, error: error instanceof Error ? error.message : String(error) });
            return message;
          }
        })
      );

      // 转换为标准消息格式
      this.historyMessages = this.getMessagesDefinition(enrichedMessages);

      logger.info('历史消息初始化完成', {
        messageCount: enrichedMessages.length,
        attachmentCount: allAttachmentUuids.length,
        processedAttachmentCount: attachmentContents.length
      });
      // console.log((this.historyMessages[2] as any).tool_calls[0]);

    } catch (error) {
      logger.error('初始化历史消息失败', error);
      // 如果处理失败，使用原始消息
      this.historyMessages = this.getMessagesDefinition(historyMessages);
    }
  }

  private getMainWindow = () => {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (!mainWindow) {
      throw new Error('mainWindow not found');
    }
    return mainWindow;
  }

  getSessionId = () => {
    return this.sessionId;
  }

  getModelConfig = () => {
    return this.modelConfig;
  }

  getProviderConfig = () => {
    return this.providerConfig;
  }

  getTemperature = () => {
    return this.temperature;
  }

  getMaxTokens = () => {
    return this.maxTokens;
  }

  getMessageId = () => {
    return this.messageId;
  }

  getQuestionId = () => {
    return this.questionId;
  }

  getTools = () => {
    return this.tools || [];
  }

  // 对存在的连续的历史记录进行合并，例如 [user,user, assistant, assistant, user, user]
  // 合并后：[user, assistant, user]
  mergeHistoryMessages = (messages: OpenAI.Chat.ChatCompletionMessageParam[]): OpenAI.Chat.ChatCompletionMessageParam[] => {
    const mergedMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [];
    let currentMessage: OpenAI.Chat.ChatCompletionMessageParam | null = null;
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      if (!currentMessage) {
        currentMessage = message;
        mergedMessages.push(message);
        continue;
      }
      if (currentMessage.role === message.role && typeof currentMessage.content === 'string') {
        currentMessage.content += message.content;
        continue;
      } else {
        currentMessage = message;
        mergedMessages.push(message);
        continue;
      }
    }
    return mergedMessages;
  }

  getMessagesDefinition = (messages: Message[]): OpenAI.Chat.ChatCompletionMessageParam[] => {
    // 将AI的参数进行组装，提升历史消息的兼容性
    const supportsFunctionCalling = this.adapter.getSupportsFunctionCalling();
    return messages.map((message) => {
      if (message.role === 'assistant') {

        // 如果消息是助手消息，则需要解析工具调用
        const currMessage = message.message;
        let messageStr = '';
        if (currMessage) {
          try {
            const jsonMessage = JSON.parse(currMessage);
            if (!supportsFunctionCalling) {
              if (Array.isArray(jsonMessage)) {
                jsonMessage.forEach((item) => {
                  if (item.role === 'assistant') {
                    messageStr += `${item.content}${item.tool_calls?.length > 0 ? `<blockquote data-type="tool_call">${JSON.stringify(item.tool_calls)}</blockquote>` : ''}`;
                  }
                  if (item.role === 'tool') {
                    messageStr += `<blockquote data-type="tool_call_result">${JSON.stringify(item)}</blockquote>`
                  }
                  if (item.role === 'user') {
                    messageStr += `${item.content}`
                  }
                });
              } else {
                messageStr += currMessage;
              }
            } else {
              // console.log(jsonMessage);
              if (jsonMessage && jsonMessage.tool_calls && jsonMessage.tool_calls.length > 0) {
                jsonMessage.tool_calls.forEach(toolCall => {
                  console.log(toolCall.function?.arguments);
                });
              }
              return jsonMessage
            }
          } catch (error) {
            messageStr += currMessage;
          }
        } else {
          messageStr += currMessage;
        }
        return {
          role: 'assistant' as const,
          content: messageStr,
          tool_calls: undefined
        }
      }
      if (message.role === 'user') {
        let messageStr = '';
        if (message.message) {
          messageStr += message.message;
        }

        // 处理附件内容
        if (message.attachments) {
          try {
            const attachments: (AttachmentInfo & { content?: any })[] = JSON.parse(message.attachments);
            const multiModalContent: OpenAI.Chat.ChatCompletionContentPart[] = [
              { type: 'text', text: messageStr }
            ];

            // 将附件转换为多模态内容
            for (const attachment of attachments) {
              if (attachment.content && attachment.content.processing_status === 'completed') {
                const contentPart = this.convertAttachmentToContentPart(attachment);
                if (contentPart) {
                  multiModalContent.push(contentPart);
                }
              }
            }

            // 如果有附件内容，返回多模态格式
            if (multiModalContent.length > 1) {
              return {
                role: 'user' as const,
                content: multiModalContent
              };
            }
          } catch (error) {
            logger.warn('处理历史消息附件失败', { messageId: message.id, error: error instanceof Error ? error.message : String(error) });
          }
        }

        return {
          role: 'user' as const,
          content: messageStr
        }
      }
      return null;
    }).flat().filter(Boolean) as OpenAI.Chat.ChatCompletionMessageParam[];
  }

  /**
   * 将附件内容转换为多模态内容部分
   */
  private convertAttachmentToContentPart(attachment: AttachmentInfo & { content?: any }): OpenAI.Chat.ChatCompletionContentPart | null {
    if (!attachment.content || attachment.content.processing_status !== 'completed') {
      return null;
    }

    const { content_type, base64_data, raw_content } = attachment.content;
    switch (content_type) {
      case 'image':
        if (base64_data) {
          const mimeType = this.getMimeTypeFromExtension(attachment.extension);
          return {
            type: 'image_url',
            image_url: {
              url: `data:${mimeType};base64,${base64_data}`,
              // detail: 'high'
            }
          };
        }
        break;

      case 'document':
      case 'text':
      case 'code':
        if (raw_content) {
          return {
            type: 'text',
            text: `[文件: ${attachment.originalName}]\n${raw_content}`
          };
        }
        break;

      default:
        logger.warn('不支持的附件内容类型', { contentType: content_type, fileName: attachment.originalName });
        break;
    }

    return null;
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeTypeFromExtension(extension: string): string {
    const mimeTypeMap: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp'
    };

    return mimeTypeMap[extension.toLowerCase()] || 'application/octet-stream';
  }

  init = () => {
    if (this.apiType === 'anthropic') {
      this.llm = new Anthropic({
        apiKey: this.providerConfig.currentAPIKey || this.providerConfig.defaultApiKey,
        baseURL: this.providerConfig.apiBaseUrl.replace('/v1', ''),
        logger: logger,
        logLevel: 'debug'
      });
    } else {
      this.llm = new OpenAI({
        apiKey: this.providerConfig.currentAPIKey || this.providerConfig.defaultApiKey,
        baseURL: this.providerConfig.apiBaseUrl
      });
    }
  }

  unshiftSystemPrompt = (chatMessages: OpenAI.Chat.ChatCompletionMessageParam[]) => {
    if (chatMessages[0].role === 'system') {
      chatMessages[0].content = this.adapter.enhanceSystemPrompt(this.systemPrompt);
    } else {
      const systemPrompt = this.adapter.enhanceSystemPrompt(this.systemPrompt);
      if (systemPrompt) {
        chatMessages.unshift({
          role: 'system' as const,
          content: systemPrompt
        });
      }
    }
  }

  getSupportsFunctionCalling = () => {
    return this.adapter.getSupportsFunctionCalling();
  }
  /**
   * 处理OpenAI格式的多模态内容
   */
  private processOpenAIMultiModalContent = (parts: MultiModalPromptPart[]): OpenAI.Chat.ChatCompletionContentPart[] => {
    const contentParts: OpenAI.Chat.ChatCompletionContentPart[] = [];

    parts.forEach(part => {
      switch (part.type) {
        case 'text':
          contentParts.push({
            type: 'text',
            text: part.content
          });
          break;

        case 'image':
          const imageUrl = (part as MultiModalPromptPart).imageUrl || part.content;
          contentParts.push({
            type: 'image_url',
            image_url: {
              url: imageUrl.startsWith('data:') ? imageUrl : `data:image/jpeg;base64,${part.content}`,
              // detail: this.getImageDetail(part as MultiModalPromptPart)
            }
          });
          break;

        case 'audio':
          // OpenAI 目前不直接支持音频输入，转换为文本描述
          contentParts.push({
            type: 'text',
            text: `[音频文件: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]`
          });
          break;

        case 'video':
          // OpenAI 目前不直接支持视频输入，转换为文本描述
          contentParts.push({
            type: 'text',
            text: `[视频文件: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]`
          });
          break;

        case 'file':
        case 'document':
          contentParts.push({
            type: 'text',
            text: `[文件名: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]\n[文件内容]\n${part.content}`
          });
          break;

        case 'web':
          const webPart = part as MultiModalPromptPart;
          const webText = `[网页内容${webPart.title ? ` - ${webPart.title}` : ''}${webPart.url ? ` (${webPart.url})` : ''}]\n${part.content}`;
          contentParts.push({
            type: 'text',
            text: webText
          });
          break;

        default:
          // 默认处理为文本
          contentParts.push({
            type: 'text',
            text: (part as any).content
          });
          break;
      }
    });

    logger.info('processOpenAIMultiModalContent', contentParts);

    contentParts.forEach(part => {
      logger.info('processOpenAIMultiModalContent', part);
    });

    return contentParts;
  }

  /**
 * 处理Anthropic格式的多模态内容
 */
  private processAnthropicMultiModalContent = (parts: MultiModalPromptPart[]): Anthropic.MessageParam['content'] => {
    const contentParts: Anthropic.ContentBlock[] = [];

    parts.forEach(part => {
      switch (part.type) {
        case 'text':
          contentParts.push({
            type: 'text',
            text: part.content,
            citations: []
          });
          break;

        case 'image':
          const imageUrl = (part as MultiModalPromptPart).imageUrl || part.content;
          // Anthropic 目前不直接支持图片输入，转换为文本描述
          contentParts.push({
            type: 'text',
            text: `[图片文件: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]`,
            citations: []
          });
          break;

        case 'audio':
          // Anthropic 目前不直接支持音频输入，转换为文本描述
          contentParts.push({
            type: 'text',
            text: `[音频文件: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]`,
            citations: []
          });
          break;

        case 'video':
          // Anthropic 目前不直接支持视频输入，转换为文本描述
          contentParts.push({
            type: 'text',
            text: `[视频文件: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]`,
            citations: []
          });
          break;

        case 'file':
        case 'document':
          contentParts.push({
            type: 'text',
            text: `[文件名: ${(part as MultiModalPromptPart).metadata?.filename || '未知文件'}]\n[文件内容]\n${part.content}`,
            citations: []
          });
          break;

        case 'web':
          const webPart = part as MultiModalPromptPart;
          const webText = `[网页内容${webPart.title ? ` - ${webPart.title}` : ''}${webPart.url ? ` (${webPart.url})` : ''}]\n${part.content}`;
          contentParts.push({
            type: 'text',
            text: webText,
            citations: []
          });
          break;

        default:
          // 默认处理为文本
          contentParts.push({
            type: 'text',
            text: (part as any).content,
            citations: []
          });
          break;
      }
    });

    return contentParts;
  }

  /**
   * 获取图片质量设置
   */
  private getImageDetail = (part: MultiModalPromptPart): 'low' | 'high' | 'auto' => {
    const quality = part.metadata?.quality;
    if (quality === 'low') return 'low';
    if (quality === 'high') return 'high';
    return 'high';
  }

  /**
   * 获取图片MIME类型
   */
  private getImageMimeType = (part: MultiModalPromptPart): string => {
    const mimeType = part.metadata?.mimeType;
    if (mimeType) return mimeType;

    // 根据格式推断MIME类型
    if (part.format) {
      switch (part.format.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'webp':
          return 'image/webp';
        default:
          return 'image/jpeg';
      }
    }

    return 'image/jpeg';
  }

  /**
   * 创建用户消息 - 支持多模态内容
   */
  createUserMessage = (prompt: UnifiedPromptPart) => {
    // 检查是否包含多模态内容
    let hasMultiModal = false;
    hasMultiModal = typeof prompt !== 'string';
    if (!hasMultiModal) {
      // 向后兼容：如果没有多模态内容，使用原来的格式
      return {
        role: 'user' as const,
        content: prompt
      };
    }

    // 处理多模态内容
    if (this.apiType === 'anthropic') {
      if (typeof prompt === 'string') {
        return {
          role: 'user' as const,
          content: prompt
        };
      }
      return {
        role: 'user' as const,
        content: this.processAnthropicMultiModalContent(prompt as MultiModalPromptPart[])
      };
    } else {
      // OpenAI 格式
      return {
        role: 'user' as const,
        content: this.processOpenAIMultiModalContent(prompt as MultiModalPromptPart[])
      };
    }
  }

  async chat(prompt: UnifiedPromptPart, continueChat: boolean = false, toolCallResult: string = '') {
    // 创建新的 AbortController
    this.abortController = new AbortController();

    if (prompt && !continueChat) {
      this.prompt = prompt;
    }
    if (continueChat && !this.adapter.getSupportsFunctionCalling()) {
      this.getMainWindow().webContents.send('stream_message', {
        type: 'message_continue',
        data: {
          sessionId: this.sessionId,
          content: `<blockquote data-type="tool_call_end">${toolCallResult}</blockquote> 这是我的执行结果，请根据我的需求，客观判断一下是否需要继续往下选择工具或者总结会话`
        }
      });
      this.messages.push({
        role: 'user' as const,
        content: `<blockquote data-type="tool_call_end">${toolCallResult}</blockquote> 这是我的执行结果，请根据我的需求，客观判断一下是否需要继续往下选择工具或者总结会话`
      });
    }
    const supportsFunctionCalling = this.adapter.getSupportsFunctionCalling();
    const chatMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      ...(!supportsFunctionCalling ? this.mergeHistoryMessages(this.historyMessages) : this.historyMessages),
      this.prompt ? this.createUserMessage(this.prompt) : null,
      ...this.messages
    ].filter(Boolean) as OpenAI.Chat.ChatCompletionMessageParam[];

    // console.log('chatMessages', chatMessages);

    // 添加系统提示词
    this.unshiftSystemPrompt(chatMessages);

    // console.log('chatMessages', chatMessages);

    try {
      if (this.apiType === 'anthropic') {
        return await this.chatWithAnthropic(chatMessages, supportsFunctionCalling);
      } else {
        return await this.chatWithOpenAI(chatMessages, supportsFunctionCalling);
      }
    } catch (error) {
      logger.error('error', error);
      this.getMainWindow().webContents.send('stream_message', {
        type: 'message_error',
        data: {
          sessionId: this.sessionId,
          content: `\`\`\`json
          ${JSON.stringify(error, null, 2)}`
        }
      });
      this.messages.push({
        role: 'assistant',
        content: `\`\`\`json
        ${JSON.stringify(error, null, 2)}`
      });
      saveMessage({
        role: 'assistant',
        message: JSON.stringify(this.messages),
        sessionId: this.sessionId.toString(),
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: this.topP,
        model: this.modelConfig.model,
        provider: this.providerConfig.name,
        id: this.messageId,
        avatar: this.modelConfig.logo
      });
      return;
    }

  }

  public appendToResult = (resultText: string) => {
    if (this.adapter.getSupportsFunctionCalling()) {
      this.messages.push({
        role: 'tool' as const,
        content: resultText,
        tool_call_id: JSON.parse(resultText).tool_call_id
      })
    } else {
      if (this.messages[this.messages.length - 1].role === 'assistant') {
        this.messages[this.messages.length - 1].content += resultText;
        // 兼容deepseek reasoning模型
        // this.messages[this.messages.length - 1]['prefix'] = true;
      } else {
        // 因为deepseek reasoning模型，不支持tool_call_id，所以，需要手动添加
        this.messages.push({
          role: 'assistant',
          content: resultText,
          // prefix: true,
        } as any);
      }
    }
    // const supportsFunctionCalling = this.adapter.getSupportsFunctionCalling();


    saveMessage({
      role: 'assistant',
      message: JSON.stringify(this.messages),
      sessionId: this.sessionId.toString(),
      temperature: this.temperature,
      max_tokens: this.maxTokens,
      top_p: this.topP,
      model: this.modelConfig.model,
      provider: this.providerConfig.name,
      id: this.messageId,
      avatar: this.modelConfig.logo
    });
  }

  private getToolDefinitions(): OpenAI.Chat.ChatCompletionTool[] | undefined {
    // console.log('this.tools', this.tools);
    if (this.tools.length === 0) {
      return undefined;
    }
    return this.tools.map((tool) => {
      // console.log('tool', tool.inputSchema.properties);
      const isEmpty = Object.keys(tool?.inputSchema?.properties || {}).length === 0;
      // console.log('tool', tool.name, isEmpty, tool.inputSchema?.properties);
      // if (tool.inputSchema?.properties) {
      //   Object.keys(tool.inputSchema.properties).forEach(key => {
      //     if (tool.inputSchema.properties) {
      //       if ((tool.inputSchema.properties[key] as any).anyOf && (tool.inputSchema.properties[key] as any).anyOf.length > 0) {
      //         (tool.inputSchema.properties[key] as any).anyOf.forEach((item: any) => {
      //           console.log('item', item);
      //         });
      //       }
      //     }
      //   });
      // }
      return {
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: isEmpty ? undefined : {
            type: tool.inputSchema.type,
            properties: isEmpty ? [] : tool.inputSchema.properties,
            required: isEmpty ? [] : tool.inputSchema.required,
          },
        }
      }
    })
  }

  public getAbortStatus = () => {
    return this.abortController?.signal.aborted;
  }

  // 中断当前的流式响应
  public interruptStream() {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
      this.getMainWindow().webContents.send('stream_message', {
        type: 'message_interrupted',
        data: {
          sessionId: this.sessionId,
          content: '<blockquote data-type="system">流式响应已中断</blockquote>\n'
        }
      });
      logger.info('---stream interrupted---');
    }
  }

  // OpenAI API 调用方法
  private async chatWithOpenAI(chatMessages: OpenAI.Chat.ChatCompletionMessageParam[], supportsFunctionCalling: boolean) {
    const params = {
      model: this.modelConfig.model,
      messages: chatMessages,
      stream: true,
      chat_template_kwargs: DISABLED_THINKING_MODEL.findIndex(model => model === this.modelConfig.model) >= 0 ? {
        enable_thinking: false
      } : undefined,
      separate_reasoning: DISABLED_THINKING_MODEL.findIndex(model => model === this.modelConfig.model) >= 0 ? false : undefined,
      enable_thinking: DISABLED_THINKING_MODEL.findIndex(model => model === this.modelConfig.model) >= 0 ? false : undefined,
      temperature: typeof this.temperature === 'number' ? this.temperature : undefined,
      max_tokens: typeof this.maxTokens === 'number' ? this.maxTokens : undefined,
      top_p: typeof this.topP === 'number' ? this.topP : undefined,
      seed: typeof this.seed === 'number' ? this.seed : undefined,
      frequency_penalty: typeof this.repetitionPenalty === 'number' ? this.repetitionPenalty : undefined,
      tools: supportsFunctionCalling ? this.getToolDefinitions() : undefined,
    };

    logger.info('OpenAI params', params);

    chatMessages.forEach(message => {
      if (message.role === 'assistant' && message.tool_calls) {
        logger.info('tool_calls', message.tool_calls);
      }
    })


    const stream: Stream<OpenAI.Chat.Completions.ChatCompletionChunk> & {
      _request_id?: string | null;
    } = await (this.llm as OpenAI)?.chat.completions.create(params, {
      signal: this.abortController ? this.abortController.signal : undefined,
      stream: true
    }) as Stream<OpenAI.Chat.Completions.ChatCompletionChunk> & {
      _request_id?: string | null;
    };

    return await this.handleOpenAIStream(stream, supportsFunctionCalling);
  }

  // Anthropic API 调用方法
  private async chatWithAnthropic(chatMessages: OpenAI.Chat.ChatCompletionMessageParam[], supportsFunctionCalling: boolean) {
    // 转换消息格式为 Anthropic 格式
    const anthropicMessages = this.convertToAnthropicMessages(chatMessages);

    const params: any = {
      model: this.modelConfig.model,
      messages: anthropicMessages.messages,
      stream: true,
      system: anthropicMessages.system,
      max_tokens: typeof this.maxTokens === 'number' ? this.maxTokens : 4096,
      temperature: typeof this.temperature === 'number' ? this.temperature : undefined,
      top_p: typeof this.topP === 'number' ? this.topP : undefined,
      tools: supportsFunctionCalling ? this.getToolDefinitions() : undefined,
    };

    logger.info('Anthropic params', params);


    params.messages.forEach((message) => {
      if (message.tool_calls) {
        logger.info(message.tool_calls);
      }
    });

    const stream: any = await (this.llm as OpenAI).chat.completions.create(params, {
      signal: this.abortController ? this.abortController.signal : undefined,
      headers: {
        'shadow': 'true'
      }
    });

    // stream.on('text', (text) => {
    //   logger.info('text', text);
    // });
    // stream.on('thinking', (thinking) => {
    //   logger.info('thinking', thinking);
    // });
    // stream.on('streamEvent', (event) => {
    //   logger.info('streamEvent', event);
    // });
    // stream.on('contentBlock', (contentBlock) => {
    //   logger.info('contentBlock', contentBlock);
    // });
    // stream.on('message', (message) => {
    //   logger.info('message', message);
    // });
    // stream.on('tool_use', (toolUse) => {
    //   logger.info('toolUse', toolUse);
    // });


    // logger.info('stream', JSON.stringify(stream));

    return await this.handleOpenAIStream(stream, supportsFunctionCalling);
  }

  // 转换消息格式为 Anthropic 格式
  private convertToAnthropicMessages(messages: OpenAI.Chat.ChatCompletionMessageParam[]): {
    messages: Anthropic.MessageParam[],
    system?: string
  } {
    let system = '';
    const anthropicMessages: Anthropic.MessageParam[] = [];

    for (const message of messages) {
      if (message.role === 'system') {
        system = typeof message.content === 'string' ? message.content : '';
      } else if (message.role === 'user') {
        anthropicMessages.push({
          role: 'user',
          content: typeof message.content === 'string' ? message.content : JSON.stringify(message.content)
        });
      } else if (message.role === 'assistant') {
        const content: Anthropic.ContentBlock[] = [];

        if (message.content) {
          content.push({
            type: 'text',
            text: typeof message.content === 'string' ? message.content : JSON.stringify(message.content),
            citations: []
          });
        }

        if (message.tool_calls && message.tool_calls.length > 0) {
          for (const toolCall of message.tool_calls) {
            content.push({
              type: 'tool_use',
              id: toolCall.id,
              name: toolCall.function.name,
              input: typeof toolCall.function.arguments === 'string'
                ? JSON.parse(toolCall.function.arguments)
                : toolCall.function.arguments
            });
          }
        }

        anthropicMessages.push({
          role: 'assistant',
          content: content
        });
      } else if (message.role === 'tool') {
        anthropicMessages.push({
          role: 'user',
          content: [
            {
              type: 'tool_result',
              tool_use_id: (message as any).tool_call_id,
              content: typeof message.content === 'string' ? message.content : JSON.stringify(message.content)
            }
          ]
        });
      }
    }

    return {
      messages: anthropicMessages,
      system: system || undefined
    };
  }

  // 获取 Anthropic 工具定义
  private getAnthropicToolDefinitions(): Anthropic.Tool[] | undefined {
    if (this.tools.length === 0) {
      return undefined;
    }

    return this.tools.map((tool) => {
      const isEmpty = Object.keys(tool?.inputSchema?.properties || {}).length === 0;
      return {
        name: tool.name,
        description: tool.description,
        input_schema: isEmpty ? {
          type: 'object',
          properties: {},
          required: []
        } : {
          type: tool.inputSchema.type as 'object',
          properties: tool.inputSchema.properties,
          required: Array.isArray(tool.inputSchema.required) ? tool.inputSchema.required : [],
        }
      };
    });
  }

  // 处理 OpenAI 流式响应
  private async handleOpenAIStream(stream: Stream<OpenAI.Chat.Completions.ChatCompletionChunk> & {
    _request_id?: string | null;
  }, supportsFunctionCalling: boolean) {
    // 这里将包含原来的 OpenAI 流处理逻辑
    let content = '';
    let reasoning_content = '';
    let toolCalls: ToolCall[] = [];
    let startingReasoning = false;
    let startingDeepseekR1Reasoning = AIPLAT_DEEPSEEKR1_LIKE_MODEL.findIndex(model => model === this.modelConfig.model) > -1;
    let deepseekR1Reasoning = false;
    let startingToolCalls = false;
    let currentCallIndex = -1;

    if (stream) {
      logger.info('---OpenAI stream---');
      process.stdout.write('\n');

      try {
        for await (const chunk of stream) {
          if (!chunk || !chunk.choices || chunk.choices.length === 0) {
            continue;
          }

          const delta = chunk.choices[0].delta || {};

          // 处理推理内容
          if (!startingDeepseekR1Reasoning && (delta as any).reasoning_content && typeof (delta as any).reasoning_content === 'string') {
            if (!startingReasoning) {
              startingReasoning = true;
              process.stdout.write('\n');
              logger.info('---reasoning start---');
              this.getMainWindow().webContents.send('stream_message', {
                type: 'message_delta_reasoning_send',
                data: {
                  content: '<blockquote data-type="reasoning_content">' + ((delta as any).reasoning_content || ''),
                  sessionId: this.sessionId
                }
              });
            } else {
              this.getMainWindow().webContents.send('stream_message', {
                type: 'message_delta_reasoning_send',
                data: {
                  content: ((delta as any).reasoning_content || ''),
                  sessionId: this.sessionId
                }
              });
            }
            reasoning_content += ((delta as any).reasoning_content || '');
            process.stdout.write((delta as any).reasoning_content || '');
          } else {
            if (startingReasoning) {
              startingReasoning = false;
              process.stdout.write('\n');
              logger.info('---reasoning_end---');
              this.getMainWindow().webContents.send('stream_message', {
                type: 'message_delta_reasoning_send',
                data: {
                  content: '</blockquote>',
                  sessionId: this.sessionId
                }
              });
            }
          }

          // 处理内容
          if (delta?.content) {
            const isString = typeof delta.content === 'string';
            if (startingDeepseekR1Reasoning && isString) {
              if (!deepseekR1Reasoning) {
                deepseekR1Reasoning = true;
                process.stdout.write('\n');
                logger.info('deepseek Reasoning start');
                process.stdout.write(delta.content || '');
                reasoning_content += delta.content;

                this.getMainWindow().webContents.send('stream_message', {
                  type: 'message_delta_reasoning_send',
                  data: {
                    content: `<blockquote data-type="reasoning_content">${delta.content}`,
                    sessionId: this.sessionId
                  }
                });
              }
            }
            if (isString && startingDeepseekR1Reasoning) {
              if (deepseekR1Reasoning) {
                if (delta.content?.trim() === '</think>' || reasoning_content.includes('</think>')) {
                  deepseekR1Reasoning = false;
                  startingDeepseekR1Reasoning = false;
                  process.stdout.write('\n');
                  logger.info('deepseek Reasoning end');
                  reasoning_content += '';
                  this.getMainWindow().webContents.send('stream_message', {
                    type: 'message_delta_reasoning_send',
                    data: {
                      content: '</blockquote>',
                      sessionId: this.sessionId
                    }
                  });
                } else {
                  reasoning_content += delta.content;
                  process.stdout.write(delta.content || '');
                  this.getMainWindow().webContents.send('stream_message', {
                    type: 'message_delta_reasoning_send',
                    data: {
                      content: delta.content,
                      sessionId: this.sessionId
                    }
                  });
                }
              }
            } else {
              if (isString) {
                content += (delta.content || '');
                process.stdout.write(delta.content || '');
              } else {
                content += JSON.stringify(delta.content);
                process.stdout.write(JSON.stringify(delta.content));
              }

              this.getMainWindow().webContents.send('stream_message', {
                type: 'message_delta_content_send',
                data: {
                  content: delta.content || '',
                  sessionId: this.sessionId
                }
              });
            }
          }

          // 处理工具调用
          if (delta.tool_calls) {
            for (const toolCallChunk of delta.tool_calls) {
              if (toolCallChunk) {
                if (!startingToolCalls) {
                  startingToolCalls = true;
                  logger.info('---tool_calls---');
                }
                process.stdout.write('calling:' + (toolCallChunk.function?.name || ''));
                process.stdout.write('\n');

                if (FUNCTION_CALLING_SPECIAL_LOGIC_MODEL.findIndex(model => model === this.modelConfig.model) >= 0) {
                  if (toolCallChunk.function?.name) {
                    toolCalls.push({
                      id: toolCallChunk.id as string,
                      index: toolCallChunk.index,
                      function: {
                        name: toolCallChunk.function?.name || '',
                        arguments: toolCallChunk.function?.arguments || ''
                      }
                    });
                    currentCallIndex += 1;
                  } else {
                    if (currentCallIndex !== -1) {
                      console.log('toolCallChunk', JSON.stringify(toolCallChunk));
                      toolCalls[currentCallIndex].function.arguments += toolCallChunk.function?.arguments || '';
                    }
                  }
                } else {
                  if (toolCalls.findIndex(toolCall => toolCall.index === toolCallChunk.index) === -1) {
                    currentCallIndex = toolCalls.length;
                    toolCalls.push({
                      id: toolCallChunk.id as string,
                      index: toolCallChunk.index,
                      function: {
                        name: '',
                        arguments: ''
                      }
                    })
                  } else {
                    currentCallIndex = toolCalls.findIndex(toolCall => toolCall.index === toolCallChunk.index);
                  }
                  console.log('toolCallChunk', JSON.stringify(toolCallChunk));
                  let currentCall = toolCalls[currentCallIndex];
                  if (toolCallChunk.function?.name) currentCall.function.name += (toolCallChunk.function?.name || '');
                  if (toolCallChunk.function?.arguments) currentCall.function.arguments += (toolCallChunk.function?.arguments || '');
                }

                if (supportsFunctionCalling) {
                  this.getMainWindow().webContents.send('stream_message', {
                    type: 'tool_call_pending',
                    data: {
                      content: `<blockquote data-type="tool_call">${JSON.stringify(toolCalls[currentCallIndex])}</blockquote>`,
                      toolCall: toolCallChunk,
                      toolCallId: toolCalls[currentCallIndex].id,
                      sessionId: this.sessionId,
                    }
                  });
                }
              }
            }
          }
        }
      } catch (error) {
        logger.error('OpenAI stream error', error);
        this.getMainWindow().webContents.send('stream_message', {
          type: 'message_error',
          data: {
            sessionId: this.sessionId,
            content: `\`\`\`json
            ${JSON.stringify(error, null, 2)}`
          }
        });
      }

      const parsedResponse = this.adapter.parseResponse({
        content: content,
        toolCalls: toolCalls
      });

      if (supportsFunctionCalling) {
        const currentToolCalls = toolCalls.map((toolCall) => {
          return {
            type: 'function' as const,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments || '{}'
            },
            id: toolCall.id
          }
        });

        this.messages.push({
          role: 'assistant',
          content: `${reasoning_content ? `<blockquote data-type="reasoning_content">${reasoning_content}</blockquote>\n` : ''}${parsedResponse.content}`,
          tool_calls: currentToolCalls?.length > 0 ? currentToolCalls : undefined,
        });
      } else {
        this.messages.push({
          role: 'assistant',
          content: `${reasoning_content ? `<blockquote data-type="reasoning_content">${reasoning_content}</blockquote>\n` : ''}${parsedResponse.content}${parsedResponse.toolCalls.map((toolCall) => {
            return `<blockquote data-type="tool_call">${JSON.stringify(toolCall)}</blockquote>\n`;
          }).join('')}`
        });
      }

      // 保存消息
      saveMessage({
        role: 'assistant',
        message: JSON.stringify(this.messages),
        sessionId: this.sessionId.toString(),
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: this.topP,
        model: this.modelConfig.model,
        provider: this.providerConfig.name,
        id: this.messageId,
        avatar: this.modelConfig.logo
      });


      trackEvent(getUserFromDb().userId, 'AI_PLAT_chat_response', {
        sessionId: this.sessionId.toString(),
        model: this.modelConfig.model + '',
        provider: this.providerConfig.name + '',
        temperature: this.temperature + '',
        maxTokens: this.maxTokens + '',
        topP: this.topP + '',
        seed: this.seed + '',
        topK: this.topK + '',
        questionId: this.questionId + '',
        messageId: this.messageId + '',
        toolCallLength: toolCalls.length + '',
        contentLength: parsedResponse.content.length + '',
        toolCallName: toolCalls.map((toolCall) => toolCall?.function?.name).join(','),
        enabledTools: this.tools.map((tool) => tool.name).join(','),
        enabledToolsCount: this.tools.length + ''
      });


      process.stdout.write('\n');
      logger.info('---end---');
      logger.info('parsedResponse', JSON.stringify(parsedResponse));

      return {
        content: parsedResponse.content,
        toolCalls: parsedResponse.toolCalls
      };
    }
  }

  // 处理 Anthropic 流式响应
  private async handleAnthropicStream(stream: MessageStream, supportsFunctionCalling: boolean) {
    let content = '';
    let toolCalls: ToolCall[] = [];
    let currentToolUse: any = null;

    if (stream) {
      logger.info('---Anthropic stream---');
      process.stdout.write('\n');
      console.log(stream['body']);

      console.log(typeof stream[Symbol.asyncIterator]);


      // const readableStream = stream.toReadableStream();
      // console.log(typeof readableStream[Symbol.asyncIterator]);

      try {
        for await (const chunk of stream) {
          logger.info('chunk', chunk);
          if (chunk.type === 'content_block_start') {
            if (chunk.content_block.type === 'text') {
              // 文本内容开始
            } else if (chunk.content_block.type === 'tool_use') {
              // 工具调用开始
              currentToolUse = {
                id: chunk.content_block.id,
                name: chunk.content_block.name,
                input: chunk.content_block.input || {}
              };
              logger.info('---tool_calls---');
              process.stdout.write('calling:' + chunk.content_block.name);
              process.stdout.write('\n');
            }
          } else if (chunk.type === 'content_block_delta') {
            if (chunk.delta.type === 'text_delta') {
              // 文本内容增量
              content += chunk.delta.text;
              process.stdout.write(chunk.delta.text);

              this.getMainWindow().webContents.send('stream_message', {
                type: 'message_delta_content_send',
                data: {
                  content: chunk.delta.text,
                  sessionId: this.sessionId
                }
              });
            } else if (chunk.delta.type === 'input_json_delta') {
              // 工具调用参数增量 - Anthropic 会逐步构建 JSON
              if (currentToolUse && chunk.delta.partial_json) {
                // 这里需要处理部分 JSON 更新
                try {
                  // 尝试解析完整的 JSON
                  currentToolUse.input = JSON.parse(chunk.delta.partial_json);
                } catch (e) {
                  // 如果不是完整的 JSON，暂时保存部分内容
                  currentToolUse.partialInput = (currentToolUse.partialInput || '') + chunk.delta.partial_json;
                }
              }
            }
          } else if (chunk.type === 'content_block_stop') {
            if (currentToolUse) {
              // 工具调用完成
              // 如果有部分输入，尝试最后解析一次
              if (currentToolUse.partialInput && !currentToolUse.input) {
                try {
                  currentToolUse.input = JSON.parse(currentToolUse.partialInput);
                } catch (e) {
                  logger.error('Failed to parse tool input:', currentToolUse.partialInput);
                  currentToolUse.input = {};
                }
              }

              toolCalls.push({
                id: currentToolUse.id,
                index: toolCalls.length,
                function: {
                  name: currentToolUse.name,
                  arguments: JSON.stringify(currentToolUse.input)
                }
              });

              if (supportsFunctionCalling) {
                this.getMainWindow().webContents.send('stream_message', {
                  type: 'tool_call_pending',
                  data: {
                    content: `<blockquote data-type="tool_call">${JSON.stringify({
                      id: currentToolUse.id,
                      function: {
                        name: currentToolUse.name,
                        arguments: currentToolUse.input
                      }
                    })}</blockquote>`,
                    toolCall: currentToolUse,
                    toolCallId: currentToolUse.id,
                    sessionId: this.sessionId,
                  }
                });
              }

              currentToolUse = null;
            }
          }
        }
      } catch (error) {
        logger.error('Anthropic stream error', error);
      }

      const parsedResponse = this.adapter.parseResponse({
        content: content,
        toolCalls: toolCalls
      });

      // 保存消息逻辑与 OpenAI 相同
      if (supportsFunctionCalling) {
        const currentToolCalls = toolCalls.map((toolCall) => {
          return {
            type: 'function' as const,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments
            },
            id: toolCall.id
          }
        });

        this.messages.push({
          role: 'assistant',
          content: parsedResponse.content,
          tool_calls: currentToolCalls?.length > 0 ? currentToolCalls : undefined,
        });
      } else {
        this.messages.push({
          role: 'assistant',
          content: `${parsedResponse.content}${parsedResponse.toolCalls.map((toolCall) => {
            return `<blockquote data-type="tool_call">${JSON.stringify(toolCall)}</blockquote>\n`;
          }).join('')}`
        });
      }

      // 保存消息
      saveMessage({
        role: 'assistant',
        message: JSON.stringify(this.messages),
        sessionId: this.sessionId.toString(),
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: this.topP,
        model: this.modelConfig.model,
        provider: this.providerConfig.name,
        id: this.messageId,
        avatar: this.modelConfig.logo
      });

      process.stdout.write('\n');
      logger.info('---end---');
      logger.info('parsedResponse', JSON.stringify(parsedResponse));

      return {
        content: parsedResponse.content,
        toolCalls: parsedResponse.toolCalls
      };
    }
  }
}
