import { ChatClient, PromptPart, ToolCall, UnifiedPromptPart } from './ChatClient';
import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { getMcpListByStatus, MCP, MCPConfig, saveMcp, getMcp, saveMcpTools } from '../db/mcp';
import { getUserFromDb } from '../db/user';
import { MCPClient } from '../mcp-core/MCPClient';
import logger from '../utils/logger';
import { trackEvent } from '../sa/sa';

// 这里需要是一个单例
export class ElectronAgent {
  private static instance: ElectronAgent | null = null;
  public mcpClients: MCPClient[]; // 改为public，允许ToolAgent访问

  public onInvokeFinish: (content: string, toolCalls: ToolCall[]) => void;

  private getMainWindow = () => {
    return BrowserWindow.getAllWindows()[0];
  }

  // 辅助方法：根据客户端支持情况添加结果
  private appendToolCallResult = (chatClient: ChatClient, toolCallResult: string, resultText: string) => {
    if (chatClient.getSupportsFunctionCalling()) {
      chatClient.appendToResult(toolCallResult);
    } else {
      chatClient.appendToResult(resultText);
    }
  }

  constructor() {
    this.mcpClients = getMcpListByStatus(1).map((mcp) => {
      const mcpConfig = JSON.parse(mcp.serverConfig) as MCPConfig;
      const user = getUserFromDb();
      const mcpClient = new MCPClient({
        name: mcp.id.toString(),
        //
        version: '1.0.0',
        // 获取cmd 为uvx 或者npx
        cmd: mcpConfig.cmd,
        // 获取args 为e
        args: mcpConfig.args,
        env: mcpConfig.env,
        serverUrl: mcpConfig.serverUrl,
        requestHeaders: {
          token: user.session,
          ...mcpConfig.requestHeaders,
        },
        serverType: mcpConfig.serverType,
        mcpName: mcp.serverName,
      });
      return mcpClient;
    });
  }

  // 客户端启动时，需要查询mcp的客户端的状态
  init = async () => {
    for (const mcpClient of this.mcpClients) {
      try {
        const tools = await mcpClient.getTools();
        if (tools.length > 0) {
          // logger.error(`mcpClient ${mcpClient.name} has no tools`);
          continue;
        }
        const result = await mcpClient.init();

        if (result) {
          const startTime = Date.now();
          const currentTools = await mcpClient.getTools();
          trackEvent(getUserFromDb().userId, 'AI_PLAT_list_tool', {
            trace_id: Math.random().toString(36).substring(2, 15),
            timestamp: Date.now() + '',
            client_name: 'alink',
            source_ip: '',
            endpoint: '',
            http_method: '',
            mcpserver_name: mcpClient.mcpName,
            jsonrpc_method: 'tools/list',
            jsonrpc_error_code: '',
            jsonrpc_error_msg: '',
            latency_ms: (Date.now() - startTime) + '',
            error_msg: '',
            retry_count: '',
            custom_tag: '',
          });
          const currentToolsName = currentTools.map(item => item.name);
          const serverTools = currentToolsName.join(',');
          saveMcpTools(
            Number(mcpClient.name),
            serverTools,
          );
          logger.info(`init mcpClient ${mcpClient.name} success`);
          continue;
        }
        logger.error(`init mcpClient ${mcpClient.name} error`);
        this.mcpClients = this.mcpClients.filter((_mcpClient) => _mcpClient.name !== mcpClient.name);
        saveMcp({
          id: Number(mcpClient.name),
          serverStatus: 0,
        });
      } catch (error) {
        logger.error(`init mcpClient ${mcpClient.name} error`, error);
        this.mcpClients = this.mcpClients.filter((_mcpClient) => _mcpClient.name !== mcpClient.name);
        saveMcp({
          id: Number(mcpClient.name),
          serverStatus: 0,
        });
      }
    }
    return this.mcpClients;
  }

  // 单独启动mcp client
  start = async (id: string) => {
    const mcpClient = this.mcpClients.find((mcpClient) => mcpClient.name === id);
    if (mcpClient) {
      try {
        const result = await mcpClient.init();
        if (result.success) {
          const startTime = Date.now();
          const currentTools = await mcpClient.getTools();
          const currentToolsName = currentTools.map(item => item.name);
          const serverTools = currentToolsName.join(',');
          saveMcpTools(
            Number(mcpClient.name),
            serverTools
          );
          trackEvent(getUserFromDb().userId, 'AI_PLAT_list_tool', {
            trace_id: Math.random().toString(36).substring(2, 15),
            timestamp: Date.now() + '',
            client_name: 'alink',
            source_ip: '',
            endpoint: '',
            http_method: '',
            mcpserver_name: mcpClient.mcpName,
            jsonrpc_method: 'tools/list',
            jsonrpc_error_code: '',
            jsonrpc_error_msg: '',
            latency_ms: (Date.now() - startTime) + '',
            error_msg: '',
            retry_count: '',
            custom_tag: '',
          });
          return result;
        }
        logger.error(`init mcpClient ${mcpClient.name} error`, result.error);
        this.mcpClients = this.mcpClients.filter((mcpClient) => mcpClient.name !== id);
        return {
          success: false,
          error: result.error,
        };
      } catch (error: any) {
        logger.error(`init mcpClient ${mcpClient.name} error`, error);
        this.mcpClients = this.mcpClients.filter((mcpClient) => mcpClient.name !== id);
        return {
          success: false,
          error: error.toString(),
        };
      }
    }
    return {
      success: false,
      error: 'mcp client not found',
    };
  }

  // 添加mcp client;
  addMcpClient = async (mcp: MCP) => {
    if (mcp) {
      const mcpConfig = JSON.parse(mcp.serverConfig) as MCPConfig;
      const user = getUserFromDb();
      const mcpClient = new MCPClient({
        name: mcp.id.toString(),
        version: '1.0.0',
        cmd: mcpConfig.cmd,
        args: mcpConfig.args,
        env: mcpConfig.env,
        serverUrl: mcpConfig.serverUrl,
        requestHeaders: {
          token: user.session,
          ...mcpConfig.requestHeaders,
        },
        serverType: mcpConfig.serverType,
        mcpName: mcp.serverName,
      });
      this.mcpClients.push(mcpClient);
      try {
        const result = await mcpClient.init();
        if (result.success) {
          const startTime = Date.now();
          const currentTools = await mcpClient.getTools();
          console.log('currentTools', currentTools);
          const currentToolsName = currentTools.map(item => item.name);
          const serverTools = currentToolsName.join(',');
          saveMcpTools(
            Number(mcpClient.name),
            serverTools,
          );
          trackEvent(getUserFromDb().userId, 'AI_PLAT_list_tool', {
            trace_id: Math.random().toString(36).substring(2, 15),
            timestamp: Date.now() + '',
            client_name: 'alink',
            source_ip: '',
            endpoint: '',
            http_method: '',
            mcpserver_name: mcpClient.mcpName,
            jsonrpc_method: 'tools/list',
            jsonrpc_error_code: '',
            jsonrpc_error_msg: '',
            latency_ms: (Date.now() - startTime) + '',
            error_msg: '',
            retry_count: '',
            custom_tag: '',
          });
          return result;
        }
        logger.error(`init mcpClient ${mcpClient.name} error`);
        this.mcpClients = this.mcpClients.filter((mcpClient) => mcpClient.name !== mcp.id.toString());
        return result;
      } catch (error: any) {
        logger.error(`init mcpClient ${mcpClient.name} error`, error);
        this.mcpClients = this.mcpClients.filter((mcpClient) => mcpClient.name !== mcpClient.name);
        return {
          success: false,
          error: error.toString(),
        };
      }
    }
    return {
      success: false,
      error: 'mcp client not found',
    };
  }

  stop = async (id: string) => {
    const mcpClient = this.mcpClients.find((mcpClient) => mcpClient.name === id);
    if (mcpClient) {
      await mcpClient.close();
      this.mcpClients = this.mcpClients.filter((mcpClient) => mcpClient.name !== id);
    }
  }


  close = async () => {
    for (const mcpClient of this.mcpClients) {
      await mcpClient.close();
    }
    this.mcpClients = [];
  }

  getMcpClient = (id: string) => {
    return this.mcpClients.find((mcpClient) => mcpClient.name === id);
  }

  getToolList = () => {
    return this.mcpClients.flatMap((mcpClient) => mcpClient.getTools());
  }

  getToolListByMcpIds = (mcpIds: string[]) => {
    const mcpClientList = this.mcpClients.filter((mcpClient) => mcpIds.includes(mcpClient.name + ''));
    if (mcpIds) {
      const tools: MCPClient['tools'] = [];
      for (const mcpClient of mcpClientList) {
        const mcpConfig = getMcp(Number(mcpClient.name));
        const serverTools = mcpConfig.serverTools || '';
        const toolList = serverTools.split(',').filter(item => item !== '');
        const mcpTools = mcpClient.getTools();
        tools.push(...mcpTools.filter(item => toolList.findIndex(tool => tool === item.name) > -1));
      }
      return tools;
    }
    return [];
  }



  getToolListByMcp = (id: string) => {
    logger.info('getToolListByMcp', id);
    const mcpClient = this.mcpClients.find((mcpClient) => mcpClient.name === id);
    if (mcpClient) {
      const mcpConfig = getMcp(Number(id));
      const serverTools = mcpConfig.serverTools || '';
      const toolList = serverTools.split(',');
      const tools = mcpClient.getTools();
      return tools.map(item => {
        return {
          ...item,
          enabled: toolList.findIndex(tool => tool === item.name) > -1,
        }
      })
    }
    return [];
  }

  invoke = async (prompt: UnifiedPromptPart, chatClient: ChatClient, mcpIds: string) => {
    const mainWindow = this.getMainWindow();

    let invokeTools: string[] = [];
    let invokeToolCount = 0;

    const invokeStartTime = Date.now();
    let callToolAbortController: AbortController[] = [];
    // 中断流执行函数
    function interruptStreamListener(event: any, data: any) {
      if (data.sessionId !== chatClient.getSessionId()) {
        return;
      }
      chatClient.interruptStream();
      callToolAbortController.forEach((controller) => {
        controller.abort();
      });
      callToolAbortController = [];
      mainWindow.webContents.send('stream_message', {
        type: 'message_interrupted',
        data: {
          sessionId: chatClient.getSessionId(),
        }
      });
      ipcMain.removeListener('interrupt_stream', interruptStreamListener);
    }
    ipcMain.on('interrupt_stream', interruptStreamListener);
    const mcpIdArr = (mcpIds || '').split(',');
    mainWindow.webContents.send('stream_message', {
      type: 'message_start_send',
      data: {
        sessionId: chatClient.getSessionId(),
      },
    });
    let response = await chatClient.chat(prompt);

    while (true) {
      if (chatClient.getAbortStatus()) {
        logger.info('stream interrupted');
        break;
      }
      let resultText = '';
      let toolCallResult = '';
      if (response && response.toolCalls && response.toolCalls.length > 0) {
        for (const toolCall of response.toolCalls) {
          const mcp = this.mcpClients.find((mcp) => mcp.getTools().find((tool) => tool.name === toolCall.function.name) && mcpIdArr.includes(mcp.name + ''));

          if (mcp) {
            const toolCallAbortController = new AbortController();
            callToolAbortController.push(toolCallAbortController);
            process.stdout.write('\n');
            logger.info(`invoke tool ${toolCall.function.name}`);
            logger.info(toolCall.function.arguments);
            try {
              if (chatClient.getSupportsFunctionCalling()) {
                mainWindow.webContents.send('stream_message', {
                  type: 'tool_call_start',
                  data: {
                    toolCall: toolCall,
                    sessionId: chatClient.getSessionId(),
                    mcpName: mcp.mcpName,
                    mcpId: mcp.name,
                    content: `<blockquote data-type="tool_call_start">${JSON.stringify({
                      ...toolCall,
                      mcpName: mcp.mcpName,
                    })}</blockquote>`,
                  }
                });
              }
              invokeTools.push(mcp.mcpName + '|' + toolCall.function.name);
              invokeToolCount++;
              const startTime = Date.now();
              let result: any = null;
              try {
                result = await mcp.callTool(toolCall.function.name, JSON.parse(toolCall.function.arguments?.trim() || '{}'), toolCallAbortController.signal);
              } catch (e: any) {
                logger.error(`invoke tool ${toolCall.function.name} error`, e.toString());
                result = {
                  error: e.toString(),
                  isSuccess: false,
                }
              }
              callToolAbortController = callToolAbortController.filter((controller) => controller !== toolCallAbortController);
              const endTime = Date.now();
              trackEvent(getUserFromDb().userId, 'AI_PLAT_invoke_tool_response', {
                trace_id: toolCall.id + '',
                timestamp: Date.now() + '',
                client_name: 'alink',
                provider: chatClient.getProviderConfig().name,
                temperature: chatClient.getTemperature() + '',
                maxTokens: chatClient.getMaxTokens() + '',
                mcpserver_name: mcp.mcpName,
                jsonrpc_method: 'tools/call',
                jsonrpc_tool_name: toolCall.function.name,
                jsonrpc_error_code: '0',
                jsonrpc_error_msg: '',
                payload_size: Buffer.byteLength(toolCall.function.arguments || '', 'utf8').toString(),
                query_fields: '',
                status_code: '200',
                latency_ms: (endTime - startTime) + '',
                retry_count: '0',
                custom_tag: '',
                questionId: chatClient.getQuestionId() + '',
                messageId: chatClient.getMessageId() + ''
              });

              logger.info(`invoke tool ${toolCall.function.name} result`, JSON.stringify(result));
              if (chatClient.getAbortStatus()) {
                logger.info('stream interrupted');
                break;
              }

              toolCallResult = JSON.stringify({
                tool_call_id: toolCall.id,
                content: JSON.stringify(result),
                mcpName: mcp.mcpName,
              });
              resultText = `<blockquote data-type="tool_call_result">${toolCallResult}</blockquote>\n`
              mainWindow.webContents.send('stream_message', {
                type: 'tool_call_end',
                data: {
                  content: resultText,
                  toolCall: toolCall,
                  sessionId: chatClient.getSessionId(),
                  mcpName: mcp.mcpName,
                }
              });
              this.appendToolCallResult(chatClient, toolCallResult, resultText);
            } catch (error) {
              logger.error(`invoketool ${toolCall.function.name} error`, error);
              if (chatClient.getAbortStatus()) {
                logger.info('stream interrupted');
                break;
              }
              toolCallResult = JSON.stringify({
                tool_call_id: toolCall.id,
                content: '工具执行失败',
              });
              resultText = `<blockquote data-type="tool_call_result">${toolCallResult}</blockquote>\n`
              this.appendToolCallResult(chatClient, toolCallResult, resultText);
              mainWindow.webContents.send('stream_message', {
                type: 'tool_call_end',
                data: {
                  content: resultText,
                  toolCall: toolCall,
                  sessionId: chatClient.getSessionId(),
                }
              });
            }
          } else {
            if (chatClient.getAbortStatus()) {
              logger.info('stream interrupted');
              break;
            }
            logger.error(`tool ${toolCall.function.name} not found`);
            toolCallResult = JSON.stringify({
              tool_call_id: toolCall.id,
              content: '工具不存在',
            });
            resultText = `<blockquote data-type="tool_call_result">${toolCallResult}</blockquote>\n`
            this.appendToolCallResult(chatClient, toolCallResult, resultText);
            mainWindow.webContents.send('stream_message', {
              type: 'tool_call_end',
              data: {
                content: resultText,
                toolCall: toolCall,
                sessionId: chatClient.getSessionId(),
              }
            });
          }
        }
        logger.info('invoke tool finish, continue');

        if (chatClient.getAbortStatus()) {
          logger.info('stream interrupted');
          break;
        }

        response = await chatClient.chat('', true, toolCallResult);
        continue;
      }
      ipcMain.removeListener('interrupt_stream', interruptStreamListener);
      const invokeEndTime = Date.now();
      trackEvent(getUserFromDb().userId, 'AI_PLAT_invoke_reponse', {
        sessionId: chatClient.getSessionId() + '',
        model: chatClient.getModelConfig().model,
        provider: chatClient.getProviderConfig().name,
        temperature: chatClient.getTemperature() + '',
        maxTokens: chatClient.getMaxTokens() + '',
        messageId: chatClient.getMessageId() + '',
        // 这次对话一共调用的工具
        invokeTools: invokeTools.join(','),
        // 这次对话一共调用的工具数量
        invokeToolCount: invokeToolCount + '',
        // 这次对话的问题ID
        questionId: chatClient.getQuestionId() + '',
        // 这次对话一共持续的时间
        invokeDuration: (invokeEndTime - invokeStartTime) + '',
        // 这次对话一共启用的工具
        enabledTools: chatClient.getTools().map((tool) => tool.name).join(','),
        // 这次对话启用工具的数量
        enabledToolsCounts: chatClient.getTools().length + '',
        // 这次对话启用的mcp数量
        enabledMcpCounts: (mcpIdArr).length + '',
        // 这次对话启用的mcp
        enabledMcp: mcpIdArr.map((mcpId) => {
          const mcp = (this.mcpClients || []).find((mcp) => mcp.name === mcpId);
          return mcp?.mcpName || '';
        }).join(','),
        // 该客户端的成功启动的mcp
        mcpClients: (this.mcpClients || []).map((mcp) => {
          return mcp.mcpName;
        }).join(','),
        // 该客户端的mcp数量
        mcpClientsCounts: (this.mcpClients || []).length + '',
      });
      if (response && response.content) {
        return response.content;
      }

      return '';
    }

  }


  public static destroyInstance() {
    if (this.instance) {
      // this.instance.close();
      this.instance = null;
    }
  }



  public static getInstance() {
    if (!this.instance) {
      this.instance = new ElectronAgent();
    }
    return this.instance;
  }
}

