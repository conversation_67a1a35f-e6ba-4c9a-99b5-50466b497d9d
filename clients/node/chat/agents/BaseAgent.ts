import { Model, Provider } from '../../db/provider';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { BrowserWindow } from 'electron';
import logger from '../../utils/logger';

/**
 * Base Agent class providing common functionality for all agent types
 */
export abstract class BaseAgent {
  protected modelConfig: Model;
  protected providerConfig: Provider;
  protected tools: Tool[];
  protected sessionId: number;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[] = [],
    sessionId: number = 0
  ) {
    this.modelConfig = modelConfig;
    this.providerConfig = providerConfig;
    this.tools = tools;
    this.sessionId = sessionId;
  }

  /**
   * Send stream message to frontend
   */
  protected sendStreamMessage(type: string, data: any) {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('stream_message', { type, data });
    }
  }

  /**
   * Stream content to frontend with typing effect
   */
  protected async streamContent(
    content: string, 
    sessionId: number, 
    type: string, 
    delay: number = 20
  ) {
    const chunks = content.match(/.{1,3}/g) || []; // Split into 3-character chunks
    
    for (const chunk of chunks) {
      this.sendStreamMessage(type, {
        sessionId,
        content: chunk
      });
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  /**
   * Get main window reference
   */
  protected getMainWindow(): BrowserWindow {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (!mainWindow) {
      throw new Error('Main window not found');
    }
    return mainWindow;
  }

  /**
   * Log agent activity
   */
  protected log(message: string, data?: any) {
    logger.info(`[${this.constructor.name}] ${message}`, data);
  }

  /**
   * Log agent errors
   */
  protected logError(message: string, error?: any) {
    logger.error(`[${this.constructor.name}] ${message}`, error);
  }
}
