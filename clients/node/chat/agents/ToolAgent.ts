import { BaseAgent } from './BaseAgent';
import { TodoItem } from './types';
import { Model, Provider } from '../../db/provider';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { OpenAI } from 'openai';
import { ElectronAgent } from '../ElectronAgent';
import { MCPClient } from '../../mcp-core/MCPClient';
import { AgentController } from '../AgentController';

/**
 * ToolAgent - Handles tool execution and MCP interactions
 */
export class ToolAgent extends BaseAgent {
  private llm: OpenAI | null = null;
  private electronAgent: ElectronAgent;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[],
    sessionId: number = 0,
    electronAgent: ElectronAgent,
    public context: AgentController,
  ) {
    super(modelConfig, providerConfig, tools, sessionId);
    this.electronAgent = electronAgent;
    this.initOpenAI();
  }

  /**
   * Initialize OpenAI client
   */
  private initOpenAI() {
    this.llm = new OpenAI({
      apiKey: this.providerConfig.currentAPIKey || this.providerConfig.defaultApiKey,
      baseURL: this.providerConfig.apiBaseUrl
    });
  }

  /**
   * Execute a tool-based step
   */
  async execute(step: TodoItem): Promise<string> {
    if (!step.tool_name) {
      throw new Error('Tool step missing tool name');
    }

    this.log('Executing tool step', { stepId: step.id, toolName: step.tool_name });

    const tool = this.tools.find(t => t.name === step.tool_name);
    if (!tool) {
      throw new Error(`Tool not found: ${step.tool_name}`);
    }
    const toolCall = {
      id: step.id,
      index: 0,
      function: {
        arguments: '{}',
        name: step.tool_name,
      }
    }

    // Send tool call pending message
    this.context.sendStreamMessage('tool_call_pending', {
      sessionId: this.sessionId,
      content: `<blockquote data-type="tool_call">${JSON.stringify(toolCall)}</blockquote>`,
      toolCallId: step.id,
      toolCall,
    });

    try {
      // First, get tool parameters from the model

      const toolCall = await this.getToolCallFromModel(tool, step);

      // Send tool call start message
      this.context.sendStreamMessage('tool_call_start', {
        sessionId: this.sessionId,
        content: `<blockquote data-type="tool_call_start">${JSON.stringify(toolCall)}</blockquote>`,
        toolCallId: step.id,
        toolCall: toolCall
      });

      // Execute the tool with the parameters
      const result = await this.executeToolCall(tool, toolCall);

      // Send tool result
      this.context.sendStreamMessage('tool_call_end', {
        sessionId: this.sessionId,
        content: `${result}\n`,
        toolCall: toolCall
      });

      this.log('Tool execution completed', { stepId: step.id, resultLength: result.length });
      return result;
    } catch (error: any) {
      const errorMsg = `工具调用失败: ${error.message}`;
      this.logError('Tool execution failed', error);

      this.context.sendStreamMessage('message_error', {
        sessionId: this.sessionId,
        content: errorMsg
      });

      throw new Error(errorMsg);
    }
  }

  /**
   * Convert MCP Tool to OpenAI Function format
   */
  private convertMcpToolToOpenAIFunction(tool: Tool): OpenAI.Chat.ChatCompletionTool {
    return {
      type: "function",
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema || {
          type: "object",
          properties: {},
          required: []
        }
      }
    };
  }

  /**
   * Get tool call parameters from the model using native OpenAI tool_calls
   */
  private async getToolCallFromModel(tool: Tool, step: TodoItem): Promise<any> {
    if (!this.llm) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.llm.chat.completions.create({
        model: this.modelConfig.model,
        messages: [
          {
            role: 'user',
            content: `
              - 历史上下文:
              ${this.context.context.assistantContext}
              - 当前任务:
              ${step}
            `,
          }
        ],
        tools: [this.convertMcpToolToOpenAIFunction(tool)],
        tool_choice: "required", // 强制模型调用工具
        temperature: 0.1,
        max_tokens: 4096,
      });

      const toolCalls = response.choices[0]?.message?.tool_calls;
      if (!toolCalls || toolCalls.length === 0) {
        throw new Error('Model did not return any tool calls');
      }

      const toolCall = toolCalls[0];
      if (toolCall.function.name !== tool.name) {
        throw new Error(`Expected tool ${tool.name}, got ${toolCall.function.name}`);
      }

      // Validate that arguments is valid JSON
      try {
        JSON.parse(toolCall.function.arguments);
      } catch (parseError) {
        throw new Error('Model returned invalid JSON in tool call arguments');
      }

      return {
        id: step.id,
        function: {
          name: toolCall.function.name,
          arguments: toolCall.function.arguments // 已经是JSON字符串
        }
      };
    } catch (error) {
      this.logError('Failed to get tool call from model', error);
      throw error;
    }
  }

  /**
   * Execute the actual tool call using real MCP client
   */
  private async executeToolCall(tool: Tool, toolCall: any): Promise<string> {
    this.log('Executing tool call', { toolName: tool.name, toolCall });

    try {
      // 1. 解析工具调用参数
      const parameters = JSON.parse(toolCall.function.arguments);
      // 2. 通过ElectronAgent找到对应的MCP客户端
      const mcpClient = this.findMcpClientForTool(tool.name);
      if (!mcpClient) {
        throw new Error(`MCP client not found for tool: ${tool.name}`);
      }
      // 3. 调用MCPClient的callTool方法
      const result = await mcpClient.callTool(tool.name, parameters, undefined);

      this.log('result', result);
      // 4. 格式化返回结果
      return this.formatToolResult(result, toolCall, mcpClient.mcpName);
    } catch (error: any) {
      this.logError('Tool execution failed', error);
      return this.formatErrorResult(error, toolCall);
    }
  }

  /**
   * 通过工具名找到对应的MCP客户端
   */
  private findMcpClientForTool(toolName: string): MCPClient | null {
    // 获取所有MCP客户端
    const mcpClients = this.electronAgent.mcpClients;

    // 遍历找到包含该工具的MCP客户端
    for (const mcpClient of mcpClients) {
      const tools = mcpClient.getTools();
      if (tools.some(tool => tool.name === toolName)) {
        return mcpClient;
      }
    }

    return null;
  }

  /**
   * 格式化成功的工具执行结果
   */
  private formatToolResult(result: any, toolCall: any, mcpName: string): string {
    const toolCallResult = JSON.stringify({
      tool_call_id: toolCall.id, // 使用模型返回的ID
      content: JSON.stringify(result),
      mcpName: mcpName
    });

    return `<blockquote data-type="tool_call_result">${toolCallResult}</blockquote>\n`;
  }

  /**
   * 格式化错误的工具执行结果
   */
  private formatErrorResult(error: any, toolCall: any): string {
    const toolCallResult = JSON.stringify({
      tool_call_id: toolCall.id, // 使用模型返回的ID
      content: `工具执行失败: ${error.message}`,
    });

    return `<blockquote data-type="tool_call_result">${toolCallResult}</blockquote>\n`;
  }

  /**
   * Get available tools
   */
  getAvailableTools(): Tool[] {
    return this.tools;
  }

  /**
   * Check if a tool is available
   */
  isToolAvailable(toolName: string): boolean {
    return this.tools.some(tool => tool.name === toolName);
  }

  /**
   * Get tool by name
   */
  getTool(toolName: string): Tool | undefined {
    return this.tools.find(tool => tool.name === toolName);
  }
}
