/**
 * Agent system type definitions
 */

// Task analysis result from PlanningAgent
export interface TaskAnalysis {
  isComplexTask: boolean;
  userIntent: string;
  complexity: number; // 1-5 stars
  estimatedSteps: number;
  requiresTools: boolean;
  taskType: 'coding' | 'analysis' | 'research' | 'general';
  confidence: number; // 0-1
}

// Individual todo item in execution plan
export interface TodoItem {
  id: string;
  content: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  agent_type: 'chat' | 'tool';
  tool_name?: string;
  estimated_time?: number; // in seconds
  dependencies?: string[]; // IDs of dependent tasks
  result?: string; // execution result
  error?: string; // error message if failed
}

// Agent stream message types
export interface AgentStreamMessage {
  type: 'agent_plan_start' |
  'agent_plan_will_start' |
  'agent_plan_update' |
  'agent_step_start' |
  'agent_step_complete' |
  'agent_progress_update' |
  'message_delta_content_send' |
  'tool_call_pending' |
  'tool_call_start' |
  'tool_call_end' |
  'message_error';
  data: {
    sessionId: number;
    content: string;
    stepId?: string;
    progress?: number;
    totalSteps?: number;
    currentStep?: number;
    toolCallId?: string;
    toolCall?: any;
    mcpName?: string;
    mcpId?: string;
  };
}

// Progress information
export interface ProgressInfo {
  currentStep: number;
  totalSteps: number;
  percentage: number;
  status: 'planning' | 'executing' | 'completed' | 'failed';
  message?: string;
}

// Agent execution context
export interface AgentContext {
  sessionId: number;
  messageId: number;
  questionId: number;
  userPrompt: string;
  assistantContext: string;
  historyMessages: any[];
  currentPlan?: TodoItem[];
  currentStepIndex?: number;
  executionResults?: string[];
}

// Agent configuration
export interface AgentConfig {
  enablePlanning: boolean;
  maxSteps: number;
  stepTimeout: number; // in milliseconds
  retryAttempts: number;
  streamDelay: number; // typing effect delay
}
