import { Model, Provider } from '../db/provider';
import { Message, saveMessage } from '../db/messages';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { BrowserWindow } from 'electron';
import logger from '../utils/logger';

import { PlanningAgent } from './agents/PlanningAgent';
import { ChatAgent } from './agents/ChatAgent';
import { ToolAgent } from './agents/ToolAgent';
import { TaskAnalysis, TodoItem, ProgressInfo, AgentContext } from './agents/types';
import { ElectronAgent } from './ElectronAgent';

/**
 * AgentController - Main orchestrator for the Agent system
 */
export class AgentController {
  private planningAgent: PlanningAgent;
  private chatAgent: ChatAgent;
  private toolAgent: ToolAgent;
  private currentPlan: TodoItem[] = [];
  private currentStepIndex: number = 0;
  public context: AgentContext;

  constructor(
    private modelConfig: Model,
    private providerConfig: Provider,
    private tools: Tool[],
    private systemPrompt: string,
    private historyMessages: Message[],
    private sessionId: number,
    private messageId: number,
    private temperature: number,
    private maxTokens: number,
    private topP: number,
    private seed: number,
    private topK: number,
    private repetitionPenalty: number,
    private questionId: number
  ) {
    // Initialize agents
    const electronAgent = ElectronAgent.getInstance();
    this.planningAgent = new PlanningAgent(modelConfig, providerConfig, tools, sessionId, this);
    this.chatAgent = new ChatAgent(modelConfig, providerConfig, tools, systemPrompt, sessionId, this);
    this.toolAgent = new ToolAgent(modelConfig, providerConfig, tools, sessionId, electronAgent, this);

    // Initialize context
    this.context = {
      sessionId,
      messageId,
      questionId,
      userPrompt: '',
      historyMessages,
      executionResults: [],
      assistantContext: '',
    };

    logger.info('[AgentController] Initialized with tools:', tools.map(t => t.name));
  }

  /**
   * Main entry point for processing messages
   */
  async processMessage(prompt: string, sessionId: number) {
    try {
      this.context.userPrompt = prompt;
      logger.info('[AgentController] Processing message', { prompt: prompt.substring(0, 100) });

      await this.streamContent('#### 📋 即将分析任务...\n\n', sessionId, 'agent_plan_will_start');

      // 1. PlanningAgent analyzes intent
      const analysis = await this.planningAgent.analyzeIntent(prompt);
      logger.info('[AgentController] Intent analysis completed', analysis);

      return await this.executeAgentWorkflow(prompt, analysis, sessionId);

    } catch (error) {
      logger.error('[AgentController] Message processing failed:', error);
      throw error;
    }
  }

  /**
   * Execute the full Agent workflow for complex tasks
   */
  private async executeAgentWorkflow(
    prompt: string,
    analysis: TaskAnalysis,
    sessionId: number
  ) {
    try {
      // Send planning start message
      this.streamContent('#### 📋 正在分析任务...\n\n', sessionId, 'agent_plan_start');

      // Generate execution plan
      this.currentPlan = await this.planningAgent.createPlan(prompt, analysis);
      this.context.currentPlan = this.currentPlan;

      // Send planning content
      const planMarkdown = this.generatePlanMarkdown(analysis, this.currentPlan);
      await this.streamContent(planMarkdown, sessionId, 'agent_plan_update');

      // Execute steps
      await this.executeSteps(this.currentPlan, sessionId);

      // Save final message
      await this.saveFinalMessage(this.context.assistantContext, sessionId);

      return { toolCalls: [] };
    } catch (error) {
      logger.error('[AgentController] Agent workflow failed:', error);
      throw error;
    }
  }

  /**
   * Execute all steps in the plan
   */
  private async executeSteps(plan: TodoItem[], sessionId: number): Promise<void> {
    this.sendStreamMessage('message_delta_content_send', '\n---\n\n## 📋 执行过程\n\n')
    this.context.executionResults = [];

    for (let i = 0; i < plan.length; i++) {
      const step = plan[i];
      this.currentStepIndex = i;

      // Send step start message
      this.sendStreamMessage('agent_step_start', {
        sessionId,
        content: `\n### 🔄 执行步骤 ${i + 1}: ${step.content}\n\n`,
        stepId: step.id,
        currentStep: i + 1,
        totalSteps: plan.length
      });

      try {
        // Execute step
        let stepResult: string;
        step.status = 'in_progress';

        if (step.agent_type === 'tool') {
          stepResult = await this.toolAgent.execute(step);
        } else {
          stepResult = await this.chatAgent.processStep(step);
        }

        // Update step status
        step.status = 'completed';
        step.result = stepResult;

        // Send step complete message
        await this.updateStepComplete(step, stepResult, i + 1, plan.length, sessionId);

        // this.sendStreamMessage('message_delta_content_send', {
        //   content: `步骤 ${i + 1} 执行成功：${stepResult}`,
        //   sessionId,
        // });

        this.context.executionResults?.push(stepResult);

      } catch (error: any) {
        const errorMsg = `步骤 ${i + 1} 执行失败: ${error.message}`;
        step.status = 'failed';
        step.error = error.message;

        this.sendStreamMessage('message_delta_content_send', {
          content: `**步骤 ${i + 1} 错误:**\n${errorMsg}\n\n`,
          sessionId
        });

        // Send error message
        this.sendStreamMessage('message_error', {
          sessionId,
          content: errorMsg
        });

        logger.error(`[AgentController] Step ${i + 1} failed:`, error);
      }
    }
  }

  /**
   * Update step completion status
   */
  private async updateStepComplete(
    step: TodoItem,
    result: string,
    currentStep: number,
    totalSteps: number,
    sessionId: number
  ) {
    const progress = Math.round((currentStep / totalSteps) * 100);

    // Send step complete message
    this.sendStreamMessage('agent_step_complete', {
      sessionId,
      content: `\n\n✅ **步骤 ${currentStep} 已完成**\n\n`,
      stepId: step.id,
      progress,
      currentStep,
      totalSteps
    });

    // Send progress update
    this.sendStreamMessage('agent_progress_update', {
      sessionId,
      content: `### 📊 当前进度\n进度：${progress}% (${currentStep}/${totalSteps} 已完成)\n\n`,
      progress,
      currentStep,
      totalSteps
    });
  }

  /**
   * Generate plan markdown content
   */
  private generatePlanMarkdown(analysis: TaskAnalysis, plan: TodoItem[]): string {
    return `## 📋 任务规划

### 🎯 任务分析
用户需求：${analysis.userIntent}
复杂度评估：${'⭐'.repeat(analysis.complexity)} (${analysis.complexity}/5)
任务类型：${analysis.taskType}

### 📝 执行计划
${plan.map((item, index) =>
      `- [ ] **步骤${index + 1}**: ${item.content} \`[${item.agent_type}${item.tool_name ? ':' + item.tool_name : ''}]\``
    ).join('\n')}

### 📊 当前进度
进度：0% (0/${plan.length} 已完成)
当前步骤：准备开始执行...

`;
  }

  /**
   * Send stream message to frontend
   */
  public sendStreamMessage = (type: string, data: any) => {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      this.context.assistantContext += data?.content || '';
      mainWindow.webContents.send('stream_message', { type, data });
    }
  }

  /**
   * Stream content with typing effect
   */
  private async streamContent(content: string, sessionId: number, type: string) {
    const words = content.split(' ');
    // this.context.assistantContext += content;
    for (let i = 0; i < words.length; i++) {
      this.sendStreamMessage(type, {
        sessionId,
        content: words[i] + ' '
      });
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  /**
   * Save final message to database
   */
  private async saveFinalMessage(content: string, sessionId: number) {
    try {
      await saveMessage({
        role: 'assistant',
        message: content,
        sessionId: sessionId.toString(),
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: this.topP,
        model: this.modelConfig.model,
        provider: this.providerConfig.name,
        id: this.messageId,
        avatar: this.modelConfig.logo
      });
    } catch (error) {
      logger.error('[AgentController] Failed to save final message:', error);
    }
  }
}
