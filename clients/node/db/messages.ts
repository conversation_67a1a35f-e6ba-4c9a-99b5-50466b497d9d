import { partialUpdate } from './dbUtils';
import { dbInstance } from './dbInstance';
export interface AttachmentInfo {
  id: string;           // UUID
  originalName: string; // 原始文件名
  fileName: string;     // 存储文件名(UUID + 扩展名)
  path: string;         // 完整路径
  size: number;         // 文件大小
  extension: string;    // 文件扩展名
  uploadTime: string;   // 上传时间
}

export interface Message {
  id: number;
  message: string;
  avatar: string;
  name: string;
  role: string;
  provider: string;
  model: string;
  temperature: number;
  top_p: number;
  max_tokens: number;
  createdAt: string;
  updatedAt: string;
  sessionId: string;
  userName: string;
  isDeleted: number;
  attachments?: string; // JSON字符串，存储AttachmentInfo[]
}

export const saveMessage = ({
  message,
  avatar,
  name,
  role,
  provider,
  model,
  temperature,
  top_p,
  max_tokens,
  sessionId,
  userName,
  id,
  isDeleted = 0,
  attachments
}: Partial<Message>) => {
  const db = dbInstance.message.db;
  if (id && getMessageById(id)) {
    partialUpdate(db, 'messages', 'id', id, { message, avatar, name, role, provider, model, temperature, top_p, max_tokens, sessionId, userName, isDeleted, attachments });
    return getMessageById(id) as Message;
  }
  const result = db.prepare(
    `INSERT INTO messages (
    message,
    avatar,
    name,
    role,
    provider,
    model,
    temperature,
    top_p,
    max_tokens,
    sessionId,
    userName,
    isDeleted,
    attachments
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
  ).run(
    message,
    avatar,
    name,
    role,
    provider,
    model,
    temperature,
    top_p,
    max_tokens,
    sessionId,
    userName,
    isDeleted,
    attachments
  );
  return getMessageById(result.lastInsertRowid as number) as Message;
}

export const getMessageById = (id: number) => {
  const db = dbInstance.message.db;
  return db.prepare('SELECT * FROM messages WHERE id = ?').get(id) as Message;
}

export const getMessagesBySessionId = (sessionId: number) => {
  const db = dbInstance.message.db;
  return db.prepare('SELECT * FROM messages WHERE sessionId = ? AND isDeleted = 0 ').all(String(sessionId)) as Message[];
}

export const getMessages = () => {
  const db = dbInstance.message.db;
  return db.prepare('SELECT * FROM messages WHERE isDeleted = 0').all() as Message[];
}

export const updateMessage = (id: number, message: string) => {
  const db = dbInstance.message.db;
  db.prepare('UPDATE messages SET message = ? WHERE id = ?').run(message, id);
}

export const deleteMessage = (id: number) => {
  const db = dbInstance.message.db;
  const result = db.prepare('SELECT * FROM messages WHERE id = ?').get(id) as Message;
  if (result) {
    saveMessage({
      ...result,
      isDeleted: 1
    })
    return result;
  }
  return false;
}

export const createClearContext = (sessionId: number) => {
  const clearContextMessage = {
    message: '清除上下文',
    role: 'clear-context',
    provider: undefined,
    model: undefined,
    temperature: undefined,
    top_p: undefined,
    max_tokens: undefined,
    sessionId: String(sessionId),
    userName: undefined,
  }
  const messages = getMessagesBySessionId(Number(sessionId));
  const lastMessage = messages[messages.length - 1];
  // 避免增加两条连续的清除上下文信息
  if (lastMessage.role === 'clear-context') {
    return lastMessage;
  }
  const result = saveMessage(clearContextMessage);
  return result;
}

export const deleteMessagesBySessionId = (sessionId: number) => {
  const db = dbInstance.message.db;
  db.prepare('DELETE FROM messages WHERE sessionId = ?').run(String(sessionId));
}

