import { dbInstance } from "./dbInstance";
import logger from "../utils/logger";

export interface AttachmentContent {
  id?: number;
  file_uuid: string;
  file_name: string;
  file_extension: string;
  file_size: number;
  content_type: 'image' | 'document' | 'text' | 'code';
  raw_content?: string;
  base64_data?: string;
  parsed_content?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  error_message?: string;
  created_at?: string;
  updated_at?: string;
}

export const createAttachmentContent = (content: AttachmentContent): AttachmentContent => {
  const db = dbInstance.message.db;
  try {
    const stmt = db.prepare(`
      INSERT INTO attachment_contents (
        file_uuid, file_name, file_extension, file_size, content_type,
        raw_content, base64_data, parsed_content, processing_status, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      content.file_uuid,
      content.file_name,
      content.file_extension,
      content.file_size,
      content.content_type,
      content.raw_content || null,
      content.base64_data || null,
      content.parsed_content || null,
      content.processing_status,
      content.error_message || null
    );
    
    return getAttachmentContentByUuid(content.file_uuid)!;
  } catch (error) {
    logger.error('创建附件内容记录失败', error);
    throw error;
  }
};

export const getAttachmentContentByUuid = (fileUuid: string): AttachmentContent | null => {
  const db = dbInstance.message.db;
  try {
    const stmt = db.prepare(`SELECT * FROM attachment_contents WHERE file_uuid = ?`);
    return stmt.get(fileUuid) as AttachmentContent || null;
  } catch (error) {
    logger.error('查询附件内容失败', error);
    return null;
  }
};

export const getAttachmentContentsByUuids = (fileUuids: string[]): AttachmentContent[] => {
  if (fileUuids.length === 0) return [];
  
  const db = dbInstance.message.db;
  try {
    const placeholders = fileUuids.map(() => '?').join(', ');
    const stmt = db.prepare(`SELECT * FROM attachment_contents WHERE file_uuid IN (${placeholders})`);
    return stmt.all(...fileUuids) as AttachmentContent[];
  } catch (error) {
    logger.error('批量查询附件内容失败', error);
    return [];
  }
};

export const updateAttachmentContent = (fileUuid: string, updates: Partial<AttachmentContent>): boolean => {
  const db = dbInstance.message.db;
  try {
    const updateFields: string[] = [];
    const values: any[] = [];
    
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'file_uuid' && key !== 'id' && key !== 'created_at') {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    });
    
    if (updateFields.length === 0) return false;
    
    // 添加 updated_at 字段
    updateFields.push(`updated_at = DATETIME('now', 'localtime')`);
    values.push(fileUuid);
    
    const stmt = db.prepare(`
      UPDATE attachment_contents 
      SET ${updateFields.join(', ')} 
      WHERE file_uuid = ?
    `);
    
    const result = stmt.run(...values);
    return result.changes > 0;
  } catch (error) {
    logger.error('更新附件内容失败', error);
    return false;
  }
};

export const updateProcessingStatus = (
  fileUuid: string, 
  status: 'pending' | 'processing' | 'completed' | 'failed', 
  errorMessage?: string
): boolean => {
  return updateAttachmentContent(fileUuid, {
    processing_status: status,
    error_message: errorMessage || undefined
  });
};

export const deleteAttachmentContent = (fileUuid: string): boolean => {
  const db = dbInstance.message.db;
  try {
    const stmt = db.prepare(`DELETE FROM attachment_contents WHERE file_uuid = ?`);
    const result = stmt.run(fileUuid);
    return result.changes > 0;
  } catch (error) {
    logger.error('删除附件内容失败', error);
    return false;
  }
};

export const getProcessingStatusCounts = (): Record<string, number> => {
  const db = dbInstance.message.db;
  try {
    const stmt = db.prepare(`
      SELECT processing_status, COUNT(*) as count 
      FROM attachment_contents 
      GROUP BY processing_status
    `);
    const results = stmt.all() as { processing_status: string, count: number }[];
    
    const counts: Record<string, number> = {};
    results.forEach(({ processing_status, count }) => {
      counts[processing_status] = count;
    });
    
    return counts;
  } catch (error) {
    logger.error('查询处理状态统计失败', error);
    return {};
  }
};