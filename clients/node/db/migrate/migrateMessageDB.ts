import Database from "better-sqlite3";

export function migrateMessageDB(db: Database.Database) {
  // 最新的数据库版本号
  const LATEST_VERSION = 5;

  // 创建版本控制表
  db.prepare(
    `CREATE TABLE IF NOT EXISTS db_version (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      version INTEGER,
      updatedAt TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();

  // 初始化或获取当前数据库版本
  const versionRow = db.prepare('SELECT version FROM db_version ORDER BY id DESC LIMIT 1').get() as { version: number } | undefined;
  let currentVersion = versionRow ? versionRow.version : 0;

  // 如果当前版本已经是最新的，不需要迁移

  if (currentVersion >= LATEST_VERSION) {
    return;
  }

  // 开始事务
  db.prepare('BEGIN TRANSACTION').run();
  try {
    if (currentVersion < 1) {
      // 添加status字段
      const columnExists = db.prepare('PRAGMA table_info(sessions)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'system_prompt')) {
        db.prepare('ALTER TABLE sessions ADD COLUMN system_prompt TEXT DEFAULT null').run();
      }
      if (currentVersion === 0) {
        db.prepare('INSERT INTO db_version (version) VALUES (?)').run(1);
      } else {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(1);
      }
      currentVersion = 1;
    }



    if (currentVersion < 2) {
      // 需要先判断一下column 是否存在
      const columnExists = db.prepare('PRAGMA table_info(messages)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'userName')) {
        db.prepare('ALTER TABLE messages ADD COLUMN userName TEXT DEFAULT null').run();
      }
      // db.prepare('ALTER TABLE messages ADD COLUMN sessionId TEXT DEFAULT null').run();
      if (currentVersion === 1) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(2);
      }
      currentVersion = 2;
    }

    if (currentVersion < 3) {
      const columnExists = db.prepare('PRAGMA table_info(messages)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'isDeleted')) {
        db.prepare('ALTER TABLE messages ADD COLUMN isDeleted INTEGER DEFAULT 0').run();
      }
      if (currentVersion === 2) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(3);
      }
      currentVersion = 3;
    }

    if (currentVersion < 4) {
      const columnExists = db.prepare('PRAGMA table_info(sessions)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'seed')) {
        db.prepare('ALTER TABLE sessions ADD COLUMN seed INTEGER DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'top_k')) {
        db.prepare('ALTER TABLE sessions ADD COLUMN top_k INTEGER DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'repetition_penalty')) {
        db.prepare('ALTER TABLE sessions ADD COLUMN repetition_penalty REAL DEFAULT NULL').run();
      }
      if (currentVersion === 3) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(4);
      }
      currentVersion = 4;
    }

    if (currentVersion < 5) {
      const columnExists = db.prepare('PRAGMA table_info(messages)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'attachments')) {
        db.prepare('ALTER TABLE messages ADD COLUMN attachments TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 4) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(5);
      }
      currentVersion = 5;
    }

    // 未来可以在这里添加更多的迁移逻辑，如:
    // if (currentVersion < 2) { ... }

    // 提交事务
    db.prepare('COMMIT').run();
  } catch (error) {
    // 出错时回滚
    db.prepare('ROLLBACK').run();
    console.error('数据库迁移失败:', error);
    throw error;
  }
}