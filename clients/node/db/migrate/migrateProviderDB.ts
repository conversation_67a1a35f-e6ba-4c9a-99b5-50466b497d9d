import Database from "better-sqlite3";
import logger from "../../utils/logger";
import { Provider } from "../provider";
import { AIPLAT_API_BASE_URL, AIPLAT_MODEL_URL, AIPLAT_BASE_API_KEY } from "../../constant";
import path from "path";
import fs from "fs";
import { isWin } from "../../utils/constant";

// 根据操作系统获取可执行文件路径
function getExecutablePath(toolName: string): string {
  const externalDir = path.join(process.cwd(), 'external');
  return path.join(externalDir, isWin ? `${toolName}.exe` : toolName);
}

// 根据操作系统获取默认目录
function getDefaultAllowedDirectory(): string {
  if (isWin) {
    return process.env.USERPROFILE || 'C:\\Users';
  } else {
    return process.env.HOME || '/tmp';
  }
}

// 内置工具配置
const BUILTIN_TOOLS = [
  {
    serverName: 'filesystem-mcp',
    serverConfig: JSON.stringify({
      serverType: 'stdio',
      cmd: getExecutablePath('filesystem-mcp'),
      args: ['--log-level', 'error', '--allowed-directories', getDefaultAllowedDirectory()],
      env: {}
    }),
    serverDesc: 'Built-in filesystem operations for file and directory management',
    serverStatus: 0, // 默认未启用
    serverFromObj: '',
    serverLogo: '',
    serverTools: 'list_directory,read_file,write_file,create_directory,delete,get_file_info,search_files,list_allowed_directories,search_content',
    isBuiltin: 1
  }
  // 可以在这里添加更多内置工具
];

// 初始化内置工具
function initBuiltinTools(db: Database.Database) {
  const insertStmt = db.prepare(`
    INSERT OR IGNORE INTO mcp (serverName, serverConfig, serverDesc, serverStatus, serverFromObj, serverLogo, serverTools, isBuiltin)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  BUILTIN_TOOLS.forEach(tool => {
    // 检查工具二进制文件是否存在
    const toolPath = JSON.parse(tool.serverConfig).cmd;
    if (fs.existsSync(toolPath)) {
      insertStmt.run(
        tool.serverName,
        tool.serverConfig,
        tool.serverDesc,
        tool.serverStatus,
        tool.serverFromObj,
        tool.serverLogo,
        tool.serverTools,
        tool.isBuiltin
      );
      logger.info(`Built-in tool initialized: ${tool.serverName}`);
    } else {
      logger.warn(`Built-in tool binary not found: ${toolPath}`);
    }
  });
}

// 数据库迁移函数
export function migrateProviderDB(db: Database.Database, currentVersion: number) {
  // 最新的数据库版本号
  const LATEST_VERSION = 13; // 增加版本号
  logger.info('currentVersion', currentVersion);

  // 如果当前版本已经是最新的，不需要迁移
  if (currentVersion >= LATEST_VERSION) {
    return;
  }

  // 开始事务
  db.prepare('BEGIN TRANSACTION').run();
  try {
    if (currentVersion < 1) {
      // 添加status字段
      db.prepare('ALTER TABLE provider ADD COLUMN status INTEGER DEFAULT 0').run();
      if (currentVersion === 0) {
        db.prepare('INSERT INTO db_version (version) VALUES (?)').run(1);
      } else {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(1);
      }
      currentVersion = 1;
    }

    if (currentVersion < 2) {
      // 添加status字段
      const columnExists = db.prepare('PRAGMA table_info(mcp)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'serverFromObj')) {
        db.prepare('ALTER TABLE mcp ADD COLUMN serverFromObj TEXT DEFAULT NULL').run();
      }
      const columnExists2 = db.prepare('PRAGMA table_info(mcp)').all() as { name: string }[] | undefined;
      if (!columnExists2 || !columnExists2.find((column) => column.name === 'serverLogo')) {
        db.prepare('ALTER TABLE mcp ADD COLUMN serverLogo TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 1) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(2);
      }
      currentVersion = 2;
    }

    if (currentVersion < 3) {
      const columnExists = db.prepare('PRAGMA table_info(models)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'temperature')) {
        db.prepare('ALTER TABLE models ADD COLUMN temperature REAL DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'top_p')) {
        db.prepare('ALTER TABLE models ADD COLUMN top_p REAL DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'max_tokens')) {
        db.prepare('ALTER TABLE models ADD COLUMN max_tokens INTEGER DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'top_k')) {
        db.prepare('ALTER TABLE models ADD COLUMN top_k INTEGER DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'repetition_penalty')) {
        db.prepare('ALTER TABLE models ADD COLUMN repetition_penalty REAL DEFAULT NULL').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'seed')) {
        db.prepare('ALTER TABLE models ADD COLUMN seed INTEGER DEFAULT NULL').run();
      }
      if (currentVersion === 2) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(3);
      }
      currentVersion = 3;
    }

    if (currentVersion < 4) {
      const columnExists = db.prepare('PRAGMA table_info(models)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'enabled')) {
        db.prepare('ALTER TABLE models ADD COLUMN enabled INTEGER DEFAULT 1').run();
      }
      if (!columnExists || !columnExists.find((column) => column.name === 'is_default')) {
        db.prepare('ALTER TABLE models ADD COLUMN is_default INTEGER DEFAULT 0').run();
      }
      if (currentVersion === 3) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(4);
      }
      currentVersion = 4;
    }

    if (currentVersion < 5) {
      const columnExists = db.prepare('PRAGMA table_info(models)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'logo')) {
        db.prepare('ALTER TABLE models ADD COLUMN logo TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 4) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(5);
      }
      currentVersion = 5;
    }

    if (currentVersion < 6) {
      const columnExists = db.prepare('PRAGMA table_info(provider)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'modelUrl')) {
        db.prepare('ALTER TABLE provider ADD COLUMN modelUrl TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 5) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(6);
      }
      currentVersion = 6;
    }

    if (currentVersion < 7) {
      const columnExists = db.prepare('PRAGMA table_info(provider)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'currentAPIKey')) {
        db.prepare('ALTER TABLE provider ADD COLUMN currentAPIKey TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 6) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(7);
      }
      currentVersion = 7;
    }

    if (currentVersion < 8) {
      const columnExists = db.prepare('PRAGMA table_info(provider)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'defaultBaseModelUrl')) {
        db.prepare('ALTER TABLE provider ADD COLUMN defaultBaseModelUrl TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 7) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(8);
      }
      currentVersion = 8;
    }

    if (currentVersion < 9) {
      const aiProvider = db.prepare('SELECT * FROM provider WHERE provider = ?').get('AIPLAT');
      if (aiProvider) {
        // 刷领慧的数据
        const defaultAIProvider: Provider = {
          id: 0,
          provider: 'AIPLAT',
          name: 'AIPLAT',
          apiBaseUrl: AIPLAT_API_BASE_URL,
          modelUrl: AIPLAT_MODEL_URL,
          defaultBaseModelUrl: AIPLAT_API_BASE_URL,
          defaultApiKey: AIPLAT_BASE_API_KEY,
          createdAt: new Date().toISOString(),
          status: 1,
          currentAPIKey: AIPLAT_BASE_API_KEY,
          isSetDefault: 0,
        }
        db.prepare(`UPDATE
          provider SET
          provider = ?,
          name = ?,
          apiBaseUrl = ?,
          modelUrl = ?,
          defaultBaseModelUrl = ?,
          defaultApiKey = ?,
          createdAt = ?,
          status = ?,
          currentAPIKey = ?,
          isSetDefault = ?
          WHERE provider = ?`
        ).run(
          defaultAIProvider.provider,
          defaultAIProvider.name,
          defaultAIProvider.apiBaseUrl,
          defaultAIProvider.modelUrl,
          defaultAIProvider.defaultBaseModelUrl,
          defaultAIProvider.defaultApiKey,
          defaultAIProvider.createdAt,
          defaultAIProvider.status,
          defaultAIProvider.currentAPIKey,
          defaultAIProvider.isSetDefault,
          defaultAIProvider.provider
        );
      }
      if (currentVersion === 8) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(9);
      }
      currentVersion = 9;
    }

    if (currentVersion < 10) {
      const columnExists = db.prepare('PRAGMA table_info(mcp)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'serverTools')) {
        db.prepare('ALTER TABLE mcp ADD COLUMN serverTools TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 9) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(10);
      }
      currentVersion = 10;
    }
    if (currentVersion < 11) {
      const columnExists = db.prepare('PRAGMA table_info(models)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'modelTag')) {
        db.prepare('ALTER TABLE models ADD COLUMN modelTag TEXT DEFAULT NULL').run();
      }
      if (currentVersion === 10) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(11);
      }
      currentVersion = 11;
    }

    if (currentVersion < 12) {
      const columnExists = db.prepare('PRAGMA table_info(provider)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'isSetDefault')) {
        db.prepare('ALTER TABLE provider ADD COLUMN isSetDefault INTEGER DEFAULT 0').run();
      }
      if (currentVersion === 11) {
        db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(12);
      }
      currentVersion = 12;
    }

    // 新增版本13: 初始化内置工具
    if (currentVersion < 13) {
      const columnExists = db.prepare('PRAGMA table_info(mcp)').all() as { name: string }[] | undefined;
      if (!columnExists || !columnExists.find((column) => column.name === 'isBuiltin')) {
        db.prepare('ALTER TABLE mcp ADD COLUMN isBuiltin INTEGER DEFAULT 0').run();
      }
      // initBuiltinTools(db);
      db.prepare('UPDATE db_version SET version = ?, updatedAt = DATETIME(\'now\', \'localtime\') WHERE id = (SELECT MAX(id) FROM db_version)').run(13);
      currentVersion = 13;
    }

    // 提交事务
    db.prepare('COMMIT').run();
  } catch (error) {
    // 出错时回滚
    db.prepare('ROLLBACK').run();
    console.error('数据库迁移失败:', error);
    throw error;
  }
}
