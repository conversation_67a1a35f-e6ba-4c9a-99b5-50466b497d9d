import { partialUpdate } from './dbUtils';
import { dbInstance } from './dbInstance';
import { getUserFromDb } from './user';

let provider = dbInstance.provider;

export interface MCP {
  id: number;
  serverName: string;
  serverConfig: string;
  serverDesc: string;
  serverStatus: number;
  serverFromObj: string;
  serverLogo: string;
  isBuiltin: number;
  /**
   * 工具列表，用,隔开
   */
  serverTools: string;
}

export interface MCPConfig {
  serverUrl: string;
  requestHeaders: Record<string, string>;
  serverType: 'stdio' | 'sse' | 'streamable_http';
  env: Record<string, string>;
  cmd: string;
  args: string[];
}


export const saveMcpTools = (id: number, tools: string) => {
  const mcp = getMcp(id);
  if (mcp) {
    const currentTools = mcp.serverTools || '';
    const currentToolList = currentTools.split(',').filter(item => item !== '');
    const toolList = tools.split(',').filter(item => item !== '');
    // console.log('currentToolList', currentToolList);
    // console.log('toolList', toolList);
    if (currentToolList.length > 0 && typeof mcp.serverTools === 'string') {
      return true;
    } else {
      // console.log('currentToolList', currentToolList);
      // console.log('toolList', toolList);
      const newToolList = new Set([...currentToolList, ...toolList]);
      const newServerTools = Array.from(newToolList).join(',');
      saveMcp({ id, serverTools: newServerTools });
      return true;
    }
  }
}



export const saveMcp = ({
  id,
  serverConfig,
  serverDesc,
  serverStatus,
  serverName,
  serverLogo,
  serverFromObj,
  serverTools,
  isBuiltin,
}: {
  id?: number,
  serverConfig?: string,
  serverDesc?: string,
  serverStatus?: number,
  serverName?: string,
  serverLogo?: string,
  serverFromObj?: string,
  serverTools?: string,
  isBuiltin?: number,
}) => {
  const mcp: any = id ? getMcp(id) : null;
  const db = dbInstance.provider.db;

  const other_mcp = serverName ? getMcpByName(serverName) : null;


  if (other_mcp && other_mcp.id !== id) {
    return {
      success: false,
      errorMsg: '已存在同名mcp',
    };
  }

  const user = getUserFromDb();

  if (!user) {
    return {
      success: false,
      errorMsg: '用户不存在',
    };
  }

  if (mcp && id) {
    partialUpdate(db, 'mcp', 'id', id, {
      serverConfig,
      serverDesc,
      serverStatus,
      serverName,
      serverLogo,
      serverFromObj,
      serverTools,
      isBuiltin,
    });

    return {
      success: true,
      result: getMcp(id),
    }
  } else {
    if (!serverName) {
      return {
        success: false,
        errorMsg: '服务器名称不能为空',
      };
    }
    const currMcp = provider.db.prepare(
      `SELECT * FROM mcp WHERE serverName = ?`
    ).get(serverName) as MCP;
    if (currMcp) {
      return {
        success: false,
        errorMsg: '已存在同名mcp',
      };
    }
    const result = provider.db.prepare(
      `INSERT INTO mcp (serverName, serverConfig, serverDesc, serverLogo, serverFromObj, serverTools, isBuiltin) VALUES (?, ?, ?, ?, ?, ?, ?)`
    ).run(serverName, serverConfig, serverDesc, serverLogo, serverFromObj, serverTools, isBuiltin || 0);
    return {
      success: true,
      result: getMcp(result.lastInsertRowid as number),
    }
  }
}

export const deleteMcp = (id: number) => {
  provider.db.prepare(
    `DELETE FROM mcp WHERE id = ?`
  ).run(id);
}

export const getMcpList = (name?: string): MCP[] => {
  if (name) {
    return provider.db.prepare(
      `SELECT * FROM mcp WHERE serverName LIKE ?`
    ).all(`%${name}%`) as MCP[];
  } else {
    return provider.db.prepare(
      `SELECT * FROM mcp`
    ).all() as MCP[];
  }
}

export const getMcp = (id: number) => {
  return provider.db.prepare(
    `SELECT * FROM mcp WHERE id = ?`
  ).get(id) as MCP;
}


export const getMcpByName = (name: string) => {
  return provider.db.prepare(
    `SELECT * FROM mcp WHERE serverName = ?`
  ).get(name) as MCP;
}


export const getMcpListByStatus = (status: number): MCP[] => {
  return provider.db.prepare(
    `SELECT * FROM mcp WHERE serverStatus = ?`
  ).all(status) as MCP[];
}
