import Database from "better-sqlite3";
import { app } from "electron";
import path from "path";
import logger from "../utils/logger";
import { migrateMessageDB } from "./migrate/migrateMessageDB";
import { partialUpdate } from "./dbUtils";
import { dbInstance } from "./dbInstance";

export interface Session {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  provider: string;
  model: string;
  temperature: number;
  top_p: number;
  userId: string;
  max_tokens: number;
  system_prompt: string;
  top_k: number;
  repetition_penalty: number;
  seed: number;
}

export const createSessionsDB = (userId: string) => {
  const db = new Database(path.join(app.getPath('userData'), `messages_${userId}_${process.env.APP_ENV}.sqlite`), {
    verbose: logger.info,
  });
  db.prepare(`CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    createdAt TEXT DEFAULT (DATETIME('now', 'localtime')),
    updatedAt TEXT DEFAULT (DATETIME('now', 'localtime')),
    provider TEXT,
    model TEXT,
    temperature REAL,
    top_p REAL,
    userId TEXT,
    max_tokens INTEGER,
    repetition_penalty REAL DEFAULT NULL,
    seed INTEGER DEFAULT NULL,
    system_prompt TEXT DEFAULT null,
    top_k INTEGER DEFAULT NULL
    )`).run();

  db.prepare(
    `CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      message TEXT,
      createdAt TEXT DEFAULT (DATETIME('now', 'localtime')),
      avatar TEXT,
      name TEXT,
      role TEXT,
      provider TEXT,
      model TEXT,
      temperature REAL,
      top_p REAL,
      max_tokens INTEGER,
      sessionId TEXT,
      userName TEXT,
      isDeleted INTEGER DEFAULT 0,
      attachments TEXT DEFAULT NULL
    )`
  ).run();

  // 创建附件内容表
  db.prepare(
    `CREATE TABLE IF NOT EXISTS attachment_contents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_uuid TEXT NOT NULL UNIQUE,
      file_name TEXT NOT NULL,
      file_extension TEXT NOT NULL,
      file_size INTEGER NOT NULL,
      content_type TEXT NOT NULL,
      raw_content TEXT,
      base64_data TEXT,
      parsed_content TEXT,
      processing_status TEXT DEFAULT 'pending',
      error_message TEXT,
      created_at TEXT DEFAULT (DATETIME('now', 'localtime')),
      updated_at TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();

  // 创建索引
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_attachment_contents_uuid ON attachment_contents(file_uuid)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_attachment_contents_status ON attachment_contents(processing_status)`).run();
  dbInstance.message.db = db;

  migrateMessageDB(db);

};

export const getSessionsList = (userId: string) => {
  const db = dbInstance.message.db;
  return db.prepare(`SELECT * FROM sessions WHERE userId = ? ORDER BY createdAt DESC`).all(userId) as Session[];
}

export const getSession = (id: number) => {
  const db = dbInstance.message.db;
  return db.prepare(`SELECT * FROM sessions WHERE id = ?`).get(id) as Session;
}

// 获取数据库里ID最大的session
export const getSessionIndex = () => {
  const db = dbInstance.message.db;
  const session = db.prepare(`SELECT MAX(id) as maxId FROM sessions`).get() as { maxId: number };
  return session.maxId;
}

export const saveSession = (session: Partial<Session>) => {
  const { name, provider, model, temperature, top_p, userId, max_tokens, id, system_prompt, top_k, repetition_penalty, seed } = session;
  const db = dbInstance.message.db;
  if (id) {
    const currentSession = getSession(id);
    if (currentSession) {
      const updates = {
        name,
        provider,
        model,
        temperature,
        top_p,
        userId,
        max_tokens,
        system_prompt,
        top_k,
        repetition_penalty,
        seed
      };
      const updated = partialUpdate(db, 'sessions', 'id', id, updates);
      if (updated) {
        return getSession(id);
      }
      return false;
    }
  } else {
    const inserted = db.prepare(`INSERT INTO sessions (name, provider, model, temperature, top_p, userId, max_tokens, system_prompt, top_k, repetition_penalty, seed) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`).run(
      name,
      provider,
      model,
      temperature,
      top_p,
      userId,
      max_tokens,
      system_prompt,
      top_k,
      repetition_penalty,
      seed
    );
    return getSession(inserted.lastInsertRowid as number);
  }
}

export const deleteSession = (id: number) => {
  const db = dbInstance.message.db;
  const session = getSession(id);
  db.prepare(`DELETE FROM sessions WHERE id = ?`).run(id);
  return session;
}


export const deleteAllMessages = (sessionId: string) => {
  const db = dbInstance.message.db;
  const selected = db.prepare('SELECT * FROM messages WHERE sessionId = ?').get(sessionId);
  if (selected) {
    db.prepare(`DELETE FROM messages WHERE sessionId = ?`).run(sessionId);
    return true;
  }
  return false;
}
