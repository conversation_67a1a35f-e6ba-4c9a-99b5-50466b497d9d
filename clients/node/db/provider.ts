import DataBase from 'better-sqlite3';
import { app } from 'electron';
import logger from '../utils/logger';
import { migrateProviderDB } from './migrate/migrateProviderDB';
import { partialUpdate } from './dbUtils';
import { dbInstance } from './dbInstance';


export interface Provider {
  id: number;
  provider: string;
  name: string;
  apiBaseUrl: string;
  defaultApiKey: string;
  status: number;
  createdAt: string;
  modelUrl: string;
  currentAPIKey: string;
  defaultBaseModelUrl: string;
  isSetDefault: number;
}

export interface Model {
  id: number;
  provider: string;
  model: string;
  temperature: number;
  top_p: number;
  max_tokens: number;
  top_k: number;
  repetition_penalty: number;
  seed: number;
  createdAt: string;
  enabled: number;
  logo: string;
  modelTag: string;
}

/**
 * 客户端初始化db
 */
export const createProviderDB = (userId: string) => {
  let db: DataBase.Database;
  const path = require('path');
  const providerDBPath = path.join(app.getPath('userData'), `provider_${userId}_${process.env.APP_ENV}.sqlite`);

  db = new DataBase(providerDBPath, {
    verbose: logger.info,
  });



  // 创建版本控制表
  db.prepare(
    `CREATE TABLE IF NOT EXISTS db_version (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      version INTEGER,
      updatedAt TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();

  // 初始化或获取当前数据库版本
  const versionRow = db.prepare('SELECT version FROM db_version ORDER BY id DESC LIMIT 1').get() as { version: number } | undefined;
  const currentVersion = versionRow ? versionRow.version : 0;

  // 创建provider表，第1版
  // status 为0 表示模型厂商 未开启
  // status 为1 表示模型厂商 已开启
  db.prepare(
    `CREATE TABLE IF NOT EXISTS provider (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      provider TEXT,
      name TEXT,
      apiBaseUrl TEXT,
      defaultApiKey TEXT,
      modelUrl TEXT DEFAULT NULL,
      defaultBaseModelUrl TEXT DEFAULT NULL,
      currentAPIKey TEXT DEFAULT NULL,
      isSetDefault INTEGER DEFAULT 0,
      createdAt TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();

  // 创建models表，第1版
  db.prepare(
    `CREATE TABLE IF NOT EXISTS models (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      provider TEXT,
      model TEXT,
      temperature REAL,
      top_p REAL DEFAULT NULL,
      max_tokens INTEGER DEFAULT NULL,
      top_k INTEGER DEFAULT NULL,
      repetition_penalty REAL DEFAULT NULL,
      seed INTEGER DEFAULT NULL,
      enabled INTEGER DEFAULT 1,
      is_default INTEGER DEFAULT 0,
      logo TEXT DEFAULT NULL,
      createdAt TEXT DEFAULT (DATETIME('now', 'localtime')),
      modelTag TEXT DEFAULT NULL
    )`
  ).run();
  db.prepare(
    `CREATE TABLE IF NOT EXISTS mcp (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      serverName TEXT,
      serverConfig TEXT,
      serverDesc TEXT,
      serverStatus INTEGER DEFAULT 0,
      serverType INTEGER DEFAULT 0,
      serverFromObj TEXT DEFAULT NULL,
      serverLogo TEXT DEFAULT NULL,
      serverTools TEXT DEFAULT NULL,
      isBuiltin INTEGER DEFAULT 0,
      createdAt TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();

  dbInstance.provider.db = db;
  // 执行数据库迁移
  migrateProviderDB(db, currentVersion);

  return db;
}

/**
 * 保存模型厂商开启或者关闭的状态
 */
export const saveProviderStatus = (provider: string, status: number) => {
  const db = dbInstance.provider.db;
  const currentProvider = db.prepare('SELECT * FROM provider WHERE provider = ?').get(provider);

  if (currentProvider) {
    // 使用通用部分更新函数
    partialUpdate(db, 'provider', 'provider', provider, { status });
  } else {
    // 如果provider不存在，则创建一个新记录
    // db.prepare('INSERT INTO provider (provider, status) VALUES (?, ?)').run(provider, status);
  }
}

/**
 * 保存模型厂商当前API Key
 */
export const saveProviderCurrentAPIKey = (provider: string, currentAPIKey: string) => {
  const db = dbInstance.provider.db;
  const currentProvider = db.prepare('SELECT * FROM provider WHERE provider = ?').get(provider);

  if (currentProvider) {
    // 使用通用部分更新函数
    partialUpdate(db, 'provider', 'provider', provider, { currentAPIKey });
  }
}

/**
 * 删除provider
 */
export const deleteProvider = (provider: string) => {
  const db = dbInstance.provider.db;
  db.prepare('DELETE FROM provider WHERE provider = ?').run(provider);
}

/**
 * 更新或者新增provider - 使用通用部分更新函数
 */
export const saveProvider = (provider: string, updates: Partial<Provider>) => {
  const db = dbInstance.provider.db;
  const currentProvider = db.prepare('SELECT * FROM provider WHERE provider = ?').get(provider);

  if (currentProvider) {
    // 使用通用部分更新函数
    partialUpdate<Provider>(db, 'provider', 'provider', provider, updates);
  } else {
    // 对于新增，需要确保provider字段包含在内
    const fieldsToInsert = { ...updates, provider };

    // 构建INSERT语句
    const fields = Object.keys(fieldsToInsert);
    const placeholders = fields.map(() => '?').join(', ');
    const values = fields.map(field => fieldsToInsert[field]);

    const sql = `INSERT INTO provider (${fields.join(', ')}) VALUES (${placeholders})`;
    db.prepare(sql).run(...values);
  }
}

/**
 * 获取provider
 */
export const getProvider = (provider: string): Provider | undefined => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM provider WHERE provider = ?').get(provider) as Provider | undefined;
}


/**
 * 获取provider全部列表
 */
export const getProviderList = (): Provider[] => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM provider').all() as Provider[];
}

/**
 * 保存模型
 */
export const saveModel = (provider: string, model: string, updates?: Partial<Model>) => {
  const db = dbInstance.provider.db;
  const currentModel = db.prepare('SELECT * FROM models WHERE provider = ? and model = ?').get(provider, model) as { id: number } | undefined;

  if (currentModel) {
    // 使用通用部分更新函数更新现有模型的其他属性
    if (updates) {
      partialUpdate<Model>(db, 'models', 'id', currentModel.id, updates);
    }
  } else {
    // 创建新模型记录
    const modelData = { provider, model, ...(updates || {}) };

    // 构建INSERT语句
    const fields = Object.keys(modelData);
    const placeholders = fields.map(() => '?').join(', ');
    const values = fields.map(field => modelData[field]);

    const sql = `INSERT INTO models (${fields.join(', ')}) VALUES (${placeholders})`;
    const result = db.prepare(sql).run(...values);
  }
}

export const getModel = (provider: string, model: string): Model | undefined => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models WHERE provider = ? AND model = ? AND enabled = 1').get(provider, model) as Model | undefined;
}

export const getModelList = (): Model[] => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models where model IS NOT NULL').all() as Model[];
}

export const getModelListByProvider = (provider: string): Model[] => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models WHERE provider = ? and model IS NOT NULL').all(provider) as Model[];
}

export const getDefaultModel = (provider: string): Model | undefined => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models WHERE provider = ? AND enabled = 1 AND is_default = 1 AND model IS NOT NULL').get(provider) as Model | undefined;
}

export const getEnabledModelList = (): Model[] => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models WHERE provider IN (SELECT provider FROM provider WHERE status = 1) and model IS NOT NULL and enabled = 1').all() as Model[];
}



export const getEnabledModelListByProvider = (provider: string): Model[] => {
  const db = dbInstance.provider.db;
  return db.prepare('SELECT * FROM models WHERE provider = ? AND enabled = 1 and model IS NOT NULL').all(provider) as Model[];
}

export const updateModel = (id: number, updates: Partial<Model>) => {
  const db = dbInstance.provider.db;
  const result = partialUpdate<Model>(db, 'models', 'id', id, updates);
  if (result) {
    return db.prepare('SELECT * FROM models WHERE id = ?').get(id) as Model;
  }
  return null;
}

export const setModelDefault = (id: number) => {
  const db = dbInstance.provider.db;
  const currentModel = db.prepare('SELECT * FROM models WHERE id = ?').get(id) as Model;
  if (currentModel) {
    db.prepare('UPDATE models SET is_default = 0').run();
    db.prepare('UPDATE models SET is_default = 1 WHERE id = ?').run(id);
  }
  return currentModel;
}


export const updateProvider = (provider: string, updates: Partial<Provider>) => {
  const db = dbInstance.provider.db;
  const result = partialUpdate<Provider>(db, 'provider', 'provider', provider, updates);
  if (result) {
    return db.prepare('SELECT * FROM provider WHERE provider = ?').get(provider) as Provider;
  }
}

export const deleteAllModelByProvider = (provider: string) => {
  const db = dbInstance.provider.db;
  db.prepare('DELETE FROM models WHERE provider = ?').run(provider);
}

export const deleteModel = (provider: string, model: string) => {
  const db = dbInstance.provider.db;
  db.prepare('DELETE FROM models WHERE provider = ? AND model = ?').run(provider, model);
}
