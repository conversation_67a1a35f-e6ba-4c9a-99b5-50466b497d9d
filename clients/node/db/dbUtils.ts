import Database from 'better-sqlite3';

/**
 * 通用部分更新函数 - 只更新提供的字段
 * @param db 数据库实例
 * @param table 表名
 * @param whereField 条件字段名
 * @param whereValue 条件字段值
 * @param updates 需要更新的字段和值的对象
 * @returns 更新操作的结果
 */
export const partialUpdate = <T extends Record<string, any>>(
  db: Database.Database,
  table: string,
  whereField: string,
  whereValue: any,
  updates: Partial<T>
) => {
  // 如果没有要更新的字段，直接返回
  if (!updates || Object.keys(updates).length === 0) return null;

  // 构建SET部分 - 过滤掉undefined值
  const fields = Object.keys(updates).filter(key => updates[key] !== undefined);
  if (fields.length === 0) return null;

  // 构建SET子句和值数组
  const setClause = fields.map(field => `${field} = ?`).join(', ');
  const values = fields.map(field => updates[field]);

  // 添加WHERE条件的值
  values.push(whereValue);

  // 构建并执行SQL更新语句
  const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereField} = ?`;
  try {
    return db.prepare(sql).run(...values);
  } catch (error) {
    console.error(`数据库更新错误: ${error}`);
    return null;
  }
}

/**
 * 通用记录插入函数
 * @param db 数据库实例
 * @param table 表名
 * @param data 要插入的数据对象
 * @returns 插入操作的结果
 */
export const insertRecord = <T extends Record<string, any>>(
  db: Database.Database,
  table: string,
  data: T
) => {
  // 过滤掉undefined值
  const filteredData: Record<string, any> = {};
  Object.keys(data).forEach(key => {
    if (data[key] !== undefined) {
      filteredData[key] = data[key];
    }
  });

  // 如果没有有效数据，直接返回
  if (Object.keys(filteredData).length === 0) return null;

  // 构建INSERT语句
  const fields = Object.keys(filteredData);
  const placeholders = fields.map(() => '?').join(', ');
  const values = fields.map(field => filteredData[field]);

  // 执行插入
  try {
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    return db.prepare(sql).run(...values);
  } catch (error) {
    console.error(`数据库插入错误: ${error}`);
    return null;
  }
}
