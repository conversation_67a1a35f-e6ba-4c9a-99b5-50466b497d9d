import DataBase from 'better-sqlite3';
import { app } from 'electron';
import logger from '../utils/logger';
import { dbInstance } from './dbInstance';

export interface User {
  /**
   * 数据库ID
   */
  id: number;
  /**
   * 用户工号
   */
  userId: string;
  /**
   * 用户名
   */
  userName: string;
  /**
   *  登录的天玑token
   */
  session: string;
}
export function createUserDB() {
  let db: DataBase.Database;
  const path = require('path');
  const userDBPath = path.join(app.getPath('userData'), `user_${process.env.APP_ENV}.sqlite`);
  db = new DataBase(userDBPath);
  dbInstance.user.db = db;

  db.prepare(
    `CREATE TABLE IF NOT EXISTS user (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      userId TEXT,
      userName TEXT,
      session TEXT,
      createdAt TEXT DEFAULT (DATETIME('now', 'localtime'))
    )`
  ).run();
  return db;
}

export function saveUserToDb(userId: string, userName: string, session: string): User {
  // 如果user存在 则更新
  const db = dbInstance.user.db;
  // user表里只允许存在一个用户；
  db.prepare('DELETE FROM user').run();
  db.prepare('INSERT INTO user (userId, userName, session) VALUES (?, ?, ?)').run(userId, userName, session);
  const user = db.prepare('SELECT * FROM user').get() as User;
  return user;
}

export function getUserFromDb() {
  const db = dbInstance.user.db;
  const user = db.prepare('SELECT * FROM user').get() as User;
  return user;
}

export function deleteUserFromDb() {
  const db = dbInstance.user.db;
  db.prepare('DELETE FROM user').run();
}


