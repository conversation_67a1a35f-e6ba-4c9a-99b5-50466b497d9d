{"name": "alink", "version": "1.0.3", "author": "jimmy", "description": "领慧MCP助手", "main": "main.js", "scripts": {"start": "bdpfe-rs start:dev", "build": "npm run build:tools && bdpfe-rs build", "build:tools": "node scripts/build-builtin-tools.js", "i18next-scanner": "i18next-scanner", "dev": "node scripts/generate-env-config.js --env sit --force && node scripts/generate-builder-config.js --env sit && npm run replace-sdk-files && npm run build:tools && cross-env NODE_ENV=development npm run build:main && cross-env NODE_ENV=development electron .", "build:main": "tsc --project tsconfig.main.json", "electron:install-app-deps": "electron-builder install-app-deps", "dist:win-x64-sit": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env sit --force && node scripts/generate-builder-config.js --env sit --arch x64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --win", "dist:win-x64-production": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env production --force && node scripts/generate-builder-config.js --env production --arch x64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --win", "dist:mac-x64-sit": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env sit --force && node scripts/generate-builder-config.js --env sit --arch x64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --mac", "dist:mac-arm64-sit": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env sit --force && node scripts/generate-builder-config.js --env sit --arch arm64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --mac", "dist:mac-x64-production": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env production --force && node scripts/generate-builder-config.js --env production --arch x64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --mac", "dist:mac-arm64-production": "npm run clean && npm run build:tools && node scripts/generate-env-config.js --env production --force && node scripts/generate-builder-config.js --env production --arch arm64 && npm run replace-sdk-files && npm run build && tsc --project tsconfig.main.json && electron-builder --mac", "replace-sdk-files": "node replace_sdk_files.js", "publish": "node scripts/publish.js", "clean": "rimraf release"}, "browserslist": "> 0.25%, not dead", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@bdpfe/axios": "^1.2.14", "@modelcontextprotocol/sdk": "^1.10.0", "adm-zip": "^0.5.16", "better-sqlite3": "^11.10.0", "chalk": "^5.4.1", "dotenv": "^16.5.0", "electron-log": "^5.3.4", "electron-updater": "^6.6.2", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "openai": "^5.10.1", "sa-sdk-node": "^1.2.6", "tar": "^7.4.3", "uuid": "^11.1.0", "web-streams-polyfill": "^4.1.0"}, "devDependencies": {"@ant-design/icons": "^6.0.0", "@bdpfe/components": "^2.3.34", "@bdpfe/env": "^2.0.1", "@bdpfe/theme": "^2.1.0", "@types/better-sqlite3": "^7.6.13", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "ahooks": "^3.7.8", "antd": "4.24.10", "bdpfe-rs": "^0.0.16", "core-js": "^3.42.0", "cross-env": "^7.0.3", "echarts": "^5.6.0", "electron": "^35.1.5", "electron-builder": "^26.0.12", "form-data": "^4.0.3", "interactjs": "^1.10.27", "less": "4.1.1", "mermaid": "^11.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-resizable": "^3.0.5", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-rehype": "^11.1.2", "typescript": "5.x"}}