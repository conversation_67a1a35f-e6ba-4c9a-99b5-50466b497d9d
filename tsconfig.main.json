{"compilerOptions": {"target": "ESNext", "module": "CommonJS", "lib": ["ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "noEmit": false, "baseUrl": ".", "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": false, "strictPropertyInitialization": false}, "include": ["clients/**/*"], "exclude": ["node_modules", "dist", "build", "src"]}