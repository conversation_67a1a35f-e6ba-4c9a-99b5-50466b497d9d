<!DOCTYPE html>
<html>
<head>
 <title>数据分析图表</title>
 <style>
 body {font-family:Arial,sans-serif; margin:20px;}
 .chart {margin-bottom:40px; border:1px solid #eee; padding:20px; border-radius:8px;}
 h2 {color:#333;}
 </style>
</head>
<body>
 <h1>数据分析图表汇总</h1>

 <div class="chart">
 <h2>1. 年度销售额趋势(折线图)</h2>
 <img src="https://mdn.alipayobjects.com/one_clip/afts/img/CnZiTYUi77UAAAAAAAAAAAAAoEACAQFr/original" width="600">
 <p>数据：2020(30) → 2021(50) → 2022(80) → 2023(65)</p>
 </div>

 <div class="chart">
 <h2>2. 产品销量对比(柱状图)</h2>
 <img src="https://mdn.alipayobjects.com/one_clip/afts/img/6OPZTpaGyq8AAAAAAAAAAAAAoEACAQFr/original" width="600">
 <p>数据：产品A(45) | 产品B(35) | 产品C(65)</p>
 </div>

 <div class="chart">
 <h2>3. 市场份额分布(饼图)</h2>
 <img src="https://mdn.alipayobjects.com/one_clip/afts/img/W65-Ta3EzKkAAAAAAAAAAAAAoEACAQFr/original" width="600">
 <p>数据：北京(40%) | 上海(30%) | 广州(20%) | 深圳(10%)</p>
 </div>
</body>
</html>