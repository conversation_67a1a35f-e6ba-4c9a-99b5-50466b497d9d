declare module '*.css';
declare module '*.less';
declare module '*.webp';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare let SSR: boolean;

declare interface Window {
  bdpStudioAPI: {
    minimize: () => void;
    maximize: () => void;
    fullscreen: () => void;
    close: () => void;
    sensors: (event: string, properties: any) => void;
    isWindows: () => boolean;
    isMac: () => boolean;
    copyClipboard: (text: string) => void;
    getHomePath: () => string;
    getProcessPath: () => string;
    getUserDataPath: () => string;
    openExternalUrl: (url: string) => void;
    openAiPlatformUrl: (url: string) => void;
    getUser: () => Promise<{
      userId: string;
      userName: string;
      session: string;
    } | boolean>;
    getModelProviderList: () => Promise<{
      id: number;
      provider: string;
      name: string;
      apiBaseUrl: string;
      defaultApiKey: string;
      status: number;
      createdAt: string;
      currentAPIKey: string;
    }[]>;
    getModelListByProvider: (provider: string) => Promise<{
      id: number;
      provider: string;
      model: string;
    }[]>;
    getEnabledModelList: () => Promise<{
      id: number;
      provider: string;
      model: string;
      logo: string;
      modelTag: 'reasoning' | 'function_call' | 'vision';
    }[]>;
    saveProviderCurrentAPIKey: (provider: string, currentAPIKey: string) => Promise<boolean>;
    verifyModelProvider: (provider: string) => Promise<boolean>;
    enabledModel: (id: number, enabled: boolean) => Promise<boolean>;
    setDefaultModel: (id: number) => Promise<boolean>;
    getAllModelList: () => Promise<{
      id: number;
      provider: string;
      model: string;
    }[]>;
    onCookieUpdate: (callback: (cookie: string) => void) => void;
    removeCookieListener: () => void;
    saveProviderStatus: (provider: string, status: number) => Promise<void>;
    installUv: () => Promise<boolean>;
    installBun: () => Promise<boolean>;
    installNodejs: () => Promise<boolean>;
    installMset: () => Promise<boolean>;
    getMsetStatus: () => Promise<boolean>;
    getMsetPath: () => Promise<string>;
    getUvStatus: () => Promise<boolean>;
    getBunStatus: () => Promise<boolean>;
    getUvPath: () => Promise<string>;
    getBunPath: () => Promise<string>;
    getNodejsPath: () => Promise<string>;
    getNodejsStatus: () => Promise<boolean>;
    getMcpList: (name?: string) => Promise<{
      serverName: string;
      serverConfig: string;
      serverDesc: string;
      serverStatus: number;
      createdAt: string;
      id: number;
      serverLogo: string;
      serverFromObj: string;
      serverTools: string;
      isBuiltin: number;
    }[]>;
    saveMcp: ({
      id,
      serverConfig,
      serverDesc,
      serverName,
      serverLogo,
    }: {
      id?: number;
      serverConfig?: string;
      serverDesc?: string;
      serverName?: string;
      serverLogo?: string;
      serverFromObj?: string;
    }) => Promise<{
      success: boolean;
      errorMsg: string;
    } | {
      success: boolean;
      result: {
        serverName: string;
        serverConfig: string;
        serverDesc: string;
        serverStatus: number;
        createdAt: string;
        id: number;
        serverLogo: string;
        serverFromObj: string;
        serverTools: string;
      }
    }>;
    deleteMcp: (id: number) => Promise<boolean | string>;
    startMcp: (id: number) => Promise<{
      success: boolean;
      error: string | null;
    }>;
    stopMcp: (id: number) => Promise<boolean | string>;
    getToolList: () => Promise<{
      name: string;
      description: string;
      inputSchema: import('@modelcontextprotocol/sdk/types').Tool['inputSchema'];
    }[]>;
    chat: (prompt: string, sessionId: number, mcpIds: string, answerMessageId?: number, questionMessageId?: number, attachments?: import('./clients/node/db/messages').AttachmentInfo[]) => Promise<import('./clients/node/db/messages').Message>;
    createSession: (userId: string, provider?: string) => Promise<import('./clients/node/db/sessions').Session>;
    getSessionList: () => Promise<import('./clients/node/db/sessions').Session[]>;
    deleteSession: (id: number) => Promise<void>;
    updateSession: (id: number, updates: Partial<import('./clients/node/db/sessions').Session>) => Promise<import('./clients/node/db/sessions').Session>;
    getMessageList: (sessionId: number) => Promise<import('./clients/node/db/messages').Message[]>;
    getToolListByMcp: (id: number) => Promise<{
      name: string;
      description: string;
      enabled: boolean;
      inputSchema: import('@modelcontextprotocol/sdk/types').Tool['inputSchema'];
    }[]>;
    enabledTool: (id: number, toolName: string) => Promise<boolean>;
    disabledTool: (id: number, toolName: string) => Promise<boolean>;
    onMessage: (key: string, callback: (event: any, data: any) => void) => void;
    removeMessageListener: (key: string) => void;
    deleteMessage: (id: number) => Promise<import('./clients/node/db/messages').Message>;
    deleteAllMessages: (sessionId: number) => Promise<import('./clients/node/db/messages').Message>;
    clearContext: (sessionId: number) => Promise<import('./clients/node/db/messages').Message>;
    onClearContext: (key: string, callback: (event: any, data: any) => void) => void;
    removeClearContextListener: (key: string) => void;
    sendInterruptStream: (sessionId: number) => void;
    onInterruptStream: (key: string, callback: (event: any, data: any) => void) => void;
    removeInterruptStreamListener: (key: string) => void;
    getMcpMarketList: (pageNum: number, pageSize: number, name: string) => Promise<{
      id: number;
      serverName: string;
      serverDesc: string;
      serverConfig: string;
      serverStatus: number;
      icon: string;
    }[]>;
    resetDefaultBaseModelUrl: (provider: string) => Promise<boolean>;
    updateProvider: (provider: string, updates: Partial<import('./clients/node/db/provider').Provider>) => Promise<import('./clients/node/db/provider').Provider>;
    logger: {
      log: (...args: any[]) => void;
      info: (...args: any[]) => void;
      warn: (...args: any[]) => void;
      error: (...args: any[]) => void;
      debug: (...args: any[]) => void;
    };
    getQrCode: () => Promise<import('./clients/node/cas').Qrcode>;
    getQrCodeStatus: () => Promise<import('./clients/node/cas').QrcodeStatus>;
    checkToken: (token: string) => Promise<boolean>;
    getUser: () => Promise<import('./clients/node/db/user').User>;
    saveUser: (user: import('./clients/node/db/user').User) => Promise<import('./clients/node/db/user').User>;
    init: () => Promise<boolean>;
    logout: () => Promise<boolean>;
    onLogout: (key: string, callback: (event: any, data: any) => void) => void;
    offLogout: (key: string) => void;
    checkLatestVersion: () => Promise<{
      version: string;
      sys: string;
      url: string;
      desc: string;
      is_force: 0 | 1;
    } | false>;
    getAppVersion: () => Promise<string>;
    getBaseServerUrl: () => string;
    getUpdateServerUrl: () => string;
    updater: {
      checkForUpdates: () => Promise<import('electron-updater').UpdateCheckResult>;
      quitAndInstall: () => Promise<void>;
      getVersion: () => Promise<string>;
      onCheckingForUpdate: (callback: (event: any, data: any) => void) => void;
      onUpdateAvailable: (callback: (event: any, data: any) => void) => void;
      onUpdateNotAvailable: (callback: (event: any, data: any) => void) => void;
      onUpdateDownloaded: (callback: (event: any, data: any) => void) => void;
      onDownloadProgress: (callback: (event: any, data: any) => void) => void;
      onError: (callback: (event: any, data: any) => void) => void;
      onManualCheckNoUpdate: (callback: (event: any, data: any) => void) => void;
      onManualCheckError: (callback: (event: any, data: any) => void) => void;
      removeAllListeners: () => void;
      downloadUpdate: () => Promise<void>;
    }
    summarizeMessageTitle: (sessionId: number, provider: string, model: string) => Promise<string | false>;
    getDefaultApiKey: () => Promise<import('./clients/node/db/provider').Provider>;
    onUpdateProviderApiKey: (key: string, callback: () => void) => void;
    offUpdateProviderApiKey: (key: string) => void;
    uploadAttachment: () => Promise<{
      success: boolean;
      data: {
        id: string;
        originalName: string;
        fileName: string;
        path: string;
        size: number;
        extension: string;
        uploadTime: string;
      };
      message: string;
      canceled: boolean;
    }>;
    uploadAttachmentFromClipboard: (fileData: { buffer: number[], originalName: string, mimeType: string, size: number }) => Promise<{
      success: boolean;
      data: {
        id: string;
        originalName: string;
        fileName: string;
        path: string;
        size: number;
        extension: string;
        uploadTime: string;
      };
      message: string;
    }>;
    getAttachmentsDirectory: () => Promise<string>;
    deleteAttachment: (fileName: string) => Promise<{
      success: boolean;
      message: string;
    }>;
    readAttachment: (fileName: string) => Promise<{
      success: boolean;
      data: {
        base64Data: string;
        mimeType: string;
      };
      message: string;
    }>;

    parseDocument: (filename: string) => any;

    // 获取待处理的URL
    getPendingUrl: () => Promise<string | null>;

    // 清空待处理的URL
    clearPendingUrl: () => Promise<boolean>;

    // 监听URL接收事件
    onMcpUrlReceived: (callback: (result: { hasPendingUrl: boolean; url: string }) => void) => void;

    // 移除URL接收事件监听
    removeMcpUrlListener: () => void;
  };
}

