const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const toolsDir = path.join(__dirname, '../tools');
const externalDir = path.join(__dirname, '../external');
const isWin = process.platform === 'win32';

// 确保external目录存在
if (!fs.existsSync(externalDir)) {
  fs.mkdirSync(externalDir, { recursive: true });
}

function buildTools() {
  console.log('开始构建内置工具...');

  try {
    // 检查是否存在Cargo.toml
    const cargoTomlPath = path.join(toolsDir, 'Cargo.toml');
    if (!fs.existsSync(cargoTomlPath)) {
      console.log('未找到 tools/Cargo.toml，跳过工具构建');
      return;
    }

    // 检查是否安装了Rust
    try {
      execSync('cargo --version', { stdio: 'ignore' });
    } catch (error) {
      console.log('未安装 Rust，跳过工具构建');
      return;
    }

    // 构建所有工具
    console.log('正在构建 Rust 工具...');

    // 添加跨平台构建支持
    const buildCommand = isWin ? 'cargo build --release' : 'cargo build --release';

    execSync(buildCommand, {
      cwd: toolsDir,
      stdio: 'inherit'
    });

    // 复制二进制文件到external目录
    const targetDir = path.join(toolsDir, 'target', 'release');

    if (fs.existsSync(targetDir)) {
      const files = fs.readdirSync(targetDir);
      let copiedFiles = 0;

      files.forEach(file => {
        const filePath = path.join(targetDir, file);
        const stat = fs.statSync(filePath);

        // 检查是否为可执行文件
        const isExecutable = stat.isFile() && (
          // Windows: 检查.exe扩展名
          (isWin && path.extname(file) === '.exe') ||
          // Unix: 检查可执行权限且无扩展名
          (!isWin && !path.extname(file) && stat.mode & 0o111)
        );

        if (isExecutable) {
          // 生成目标文件名：Windows添加.exe后缀，Unix保持原样
          const destFileName = isWin ? `${path.basename(file, '.exe')}.exe` : file;
          const destPath = path.join(externalDir, destFileName);

          // 如果目标路径已存在且是目录，先删除
          if (fs.existsSync(destPath) && fs.statSync(destPath).isDirectory()) {
            fs.rmSync(destPath, { recursive: true, force: true });
          }

          fs.copyFileSync(filePath, destPath);

          // 确保文件可执行（Unix系统）
          if (!isWin) {
            fs.chmodSync(destPath, 0o755);
          }
          console.log(`已复制工具: ${file} -> ${destPath}`);
          copiedFiles++;
        }
      });

      if (copiedFiles === 0) {
        console.log('未找到可执行文件，可能需要检查构建配置');

        // 为跨平台构建提供提示
        if (isWin) {
          console.log('提示：在 Windows 平台上，请确保 Rust 工具能够正确构建 .exe 文件');
        } else {
          console.log('提示：在 Unix 平台上，请确保生成的二进制文件具有可执行权限');
        }
      }
    } else {
      console.log('构建目录不存在，构建可能失败');
    }

    console.log('内置工具构建完成！');

  } catch (error) {
    console.error('构建工具时出错:', error.message);

    // 提供更详细的错误信息
    if (error.message.includes('cargo')) {
      console.error('请确保：');
      console.error('1. 已安装 Rust 工具链');
      console.error('2. 当前目录有 tools/Cargo.toml 文件');
      console.error('3. 网络连接正常，可以下载依赖');
    }

    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  buildTools();
}

module.exports = { buildTools };
