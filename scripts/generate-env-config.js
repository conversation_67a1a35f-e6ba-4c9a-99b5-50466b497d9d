#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const packageJson = require('../package.json');

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    env: process.env.NODE_ENV || 'production',
    output: '.env',
    help: false,
    force: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--env':
      case '-e':
        options.env = args[++i];
        break;
      case '--output':
      case '-o':
        options.output = args[++i];
        break;
      case '--force':
      case '-f':
        options.force = true;
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      default:
        if (arg.startsWith('--')) {
          console.warn(`⚠️  未知参数: ${arg}`);
        }
    }
  }

  return options;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
🌍 环境配置文件生成器

用法:
  node generate-env-config.js [选项]

选项:
  -e, --env <环境>      设置环境 (production, sit, development)
  -o, --output <文件>   输出文件名 (默认: .env)
  -f, --force          强制覆盖现有文件
  -h, --help           显示帮助信息

示例:
  node generate-env-config.js --env sit
  node generate-env-config.js -e production -o .env.local
  node generate-env-config.js --env sit --force

环境变量:
  NODE_ENV             设置构建环境
  `);
}

/**
 * 获取环境配置
 * @param {string} env 环境名称
 * @returns {Object} 环境配置对象
 */
function getEnvConfig(env) {
  const platform = process.platform;
  const arch = process.arch;
  const isWindows = platform === 'win32' || platform === 'win64';
  const isMac = platform === 'darwin';
  const ossDir = isWindows ? 'win-x64' : isMac ? `mac-${arch}` : '';
  const configs = {
    production: {
      BASE_SERVER_MARKET_URL: "https://ai.sf-express.com/server-api/mcp-square-api/api/v1",
      CAS_URL: "https://cas.sf-express.com",
      CAS_SERVICE_ID: "ai.sf-express.com",
      TJ_URL: "https://ai.sf-express.com/server-api/apis-auth",
      TJ_APP_KEY: "inc-aiplat-core-mcp",
      TJ_APP_SECRET: "inc-aiplat-core-mcp",
      STUDIO_NAME: "alink",
      APP_VERSION: packageJson.version,
      APP_ENV: "PROD",
      UPDATE_SERVER_URL: `https://mcp-square-inc-aiplat-core-shenzhen-longhua1-oss.sf-express.com/v1/AUTH_mcp-square-INC-AIPLAT-CORE/client-pkg/release/${ossDir}`
    },
    sit: {
      BASE_SERVER_MARKET_URL: "https://ai-dev3.sit.sf-express.com/server-api/mcp-square-api/api/v1",
      CAS_URL: "https://cas.sit.sf-express.com",
      CAS_SERVICE_ID: "ai-dev3.sit.sf-express.com",
      TJ_URL: "https://ai-dev3.sit.sf-express.com/server-api/apis-auth",
      TJ_APP_KEY: "inc-aiplat-core-mcp",
      TJ_APP_SECRET: "inc-aiplat-core-mcp",
      STUDIO_NAME: "ailinksit",
      APP_VERSION: packageJson.version,
      APP_ENV: "SIT",
      UPDATE_SERVER_URL: `https://mcp-square-inc-aiplat-core-guian-guian1-oss.sit.sf-express.com/v1/AUTH_mcp-square-INC-AIPLAT-CORE/client-pkg/release/${ossDir}`
    }
  };

  if (!configs[env]) {
    throw new Error(`不支持的环境: ${env}。支持的环境: ${Object.keys(configs).join(', ')}`);
  }

  return configs[env];
}

/**
 * 将配置对象转换为.env格式的字符串
 * @param {Object} config 配置对象
 * @param {string} env 环境名称
 * @returns {string} .env格式的字符串
 */
function configToEnvString(config, env) {
  const timestamp = new Date().toISOString();
  const header = `# 环境配置文件 - ${env.toUpperCase()}
# 自动生成于: ${timestamp}
# 请勿手动修改此文件，使用 npm run generate:env 重新生成

`;

  const envLines = Object.entries(config)
    .map(([key, value]) => `${key}="${value}"`)
    .join('\n');

  return header + envLines + '\n';
}

/**
 * 验证配置
 * @param {Object} config 配置对象
 * @returns {boolean} 是否有效
 */
function validateConfig(config) {
  const required = [
    'BASE_SERVER_MARKET_URL',
    'CAS_URL',
    'CAS_SERVICE_ID',
    'TJ_URL',
    'TJ_APP_KEY',
    'TJ_APP_SECRET',
    'STUDIO_NAME',
    'APP_VERSION',
    'APP_ENV',
    'UPDATE_SERVER_URL',
  ];

  for (const field of required) {
    if (!config[field]) {
      console.error(`❌ 缺少必需字段: ${field}`);
      return false;
    }
  }

  // 验证URL格式
  const urlFields = ['BASE_SERVER_MARKET_URL', 'CAS_URL', 'TJ_URL'];
  for (const field of urlFields) {
    try {
      new URL(config[field]);
    } catch (error) {
      console.error(`❌ 无效的URL格式: ${field} = ${config[field]}`);
      return false;
    }
  }

  return true;
}

/**
 * 检查文件是否存在并询问是否覆盖
 * @param {string} filePath 文件路径
 * @param {boolean} force 是否强制覆盖
 * @returns {boolean} 是否可以写入
 */
function checkFileOverwrite(filePath, force) {
  if (!fs.existsSync(filePath)) {
    return true;
  }

  if (force) {
    console.log(`🔄 强制覆盖现有文件: ${filePath}`);
    return true;
  }

  console.warn(`⚠️  文件已存在: ${filePath}`);
  console.log('💡 使用 --force 参数强制覆盖，或指定不同的输出文件名');
  return false;
}

/**
 * 生成.env文件
 * @param {Object} options 选项
 */
function generateEnvConfig(options = {}) {
  try {
    const { env, output, force } = options;

    // 获取配置
    const config = getEnvConfig(env);

    // 验证配置
    if (!validateConfig(config)) {
      process.exit(1);
    }

    const outputPath = path.resolve(process.cwd(), output);

    // 检查文件覆盖
    if (!checkFileOverwrite(outputPath, force)) {
      process.exit(1);
    }

    // 转换为.env格式
    const envContent = configToEnvString(config, env);

    // 确保目录存在
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 写入文件
    fs.writeFileSync(outputPath, envContent, 'utf8');

    console.log('✅ 环境配置文件生成成功!');
    console.log(`📁 文件路径: ${outputPath}`);
    console.log(`🏗️  环境: ${env}`);
    console.log(`🏢 工作室名称: ${config.STUDIO_NAME}`);
    console.log(`📱 应用版本: ${config.APP_VERSION}`);
    console.log(`🌐 服务器地址: ${config.BASE_SERVER_MARKET_URL}`);

    // 显示配置摘要
    console.log('\n📋 配置摘要:');
    Object.entries(config).forEach(([key, value]) => {
      // 隐藏敏感信息
      const displayValue = key.includes('SECRET') || key.includes('KEY')
        ? '*'.repeat(value.length)
        : value;
      console.log(`   ${key}: ${displayValue}`);
    });

  } catch (error) {
    console.error('❌ 生成环境配置文件失败:', error.message);
    process.exit(1);
  }
}

/**
 * 从现有.env文件读取配置
 * @param {string} filePath .env文件路径
 * @returns {Object} 配置对象
 */
function readEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`文件不存在: ${filePath}`);
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const config = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        let value = valueParts.join('=');
        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) ||
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }
        config[key.trim()] = value;
      }
    }
  });

  return config;
}

/**
 * 比较两个配置的差异
 * @param {Object} config1 配置1
 * @param {Object} config2 配置2
 * @returns {Object} 差异信息
 */
function compareConfigs(config1, config2) {
  const differences = {
    added: {},
    removed: {},
    changed: {}
  };

  const allKeys = new Set([...Object.keys(config1), ...Object.keys(config2)]);

  for (const key of allKeys) {
    if (!(key in config1)) {
      differences.added[key] = config2[key];
    } else if (!(key in config2)) {
      differences.removed[key] = config1[key];
    } else if (config1[key] !== config2[key]) {
      differences.changed[key] = {
        old: config1[key],
        new: config2[key]
      };
    }
  }

  return differences;
}

// 主函数
function main() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  // 设置环境变量
  process.env.NODE_ENV = options.env;

  generateEnvConfig(options);
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  getEnvConfig,
  generateEnvConfig,
  validateConfig,
  configToEnvString,
  readEnvFile,
  compareConfigs
};
