const bdpfeaxios = require('@bdpfe/axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = bdpfeaxios.default;

// OSS上传接口配置
const OSS_UPLOAD_URL = 'http://mcp-square-api.sit.sf-express.com:1080/api/v1/file/uploadClient';

// 获取系统平台信息
const platform = process.platform;
const arch = process.arch;

// 根据平台和架构确定目录名
function getPathByPlatform() {
  if (platform === 'win32') {
    return 'release/win-x64';
  } else if (platform === 'darwin') {
    if (arch === 'arm64') {
      return 'release/mac-arm64';
    } else {
      return 'release/mac-x64';
    }
  }
  throw new Error(`不支持的平台: ${platform}-${arch}`);
}

// 获取需要上传的文件列表
function getFilesToUpload() {
  const releaseDir = path.join(__dirname, '..', 'release');
  const files = [];

  if (!fs.existsSync(releaseDir)) {
    throw new Error('release目录不存在，请先构建应用');
  }

  const dirFiles = fs.readdirSync(releaseDir);

  if (platform === 'win32') {
    // Windows系统需要上传的文件
    dirFiles.forEach(file => {
      if (file.endsWith('.exe') || file.endsWith('.zip') || file === 'latest.yml') {
        files.push({
          filePath: path.join(releaseDir, file),
          fileName: file
        });
      }
    });
  } else if (platform === 'darwin') {
    // Mac系统需要上传的文件
    dirFiles.forEach(file => {
      if (file.endsWith('.dmg') || file.endsWith('.zip') || file === 'latest-mac.yml') {
        files.push({
          filePath: path.join(releaseDir, file),
          fileName: file
        });
      }
    });
  }

  return files;
}

// 上传单个文件到OSS
async function uploadFile(filePath, fileName, targetPath) {
  try {
    console.log(`📤 开始上传文件: ${fileName}`);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    // 获取文件大小
    const fileStats = fs.statSync(filePath);
    const fileSize = fileStats.size;
    const fileSizeMB = (fileSize / 1024 / 1024).toFixed(2);
    console.log(`📁 文件大小: ${fileSizeMB} MB`);

        console.log(`🚀 正在上传到: ${OSS_UPLOAD_URL}`);
    const startTime = Date.now();

    // 创建一个自定义的进度跟踪FormData
    const formData = new FormData();

    // 创建一个可以跟踪进度的文件流
    let uploadedBytes = 0;
    let lastProgressUpdate = startTime;
    let progressInterval;

    const fileStream = fs.createReadStream(filePath);

    // 监听文件流数据，用于跟踪读取进度
    fileStream.on('data', (chunk) => {
      uploadedBytes += chunk.length;
      const now = Date.now();

      // 每2秒更新一次进度
      if (now - lastProgressUpdate >= 2000) {
        const progress = ((uploadedBytes / fileSize) * 100).toFixed(1);
        const uploadedMB = (uploadedBytes / 1024 / 1024).toFixed(2);
        const elapsedTime = (now - startTime) / 1000;
        const speedMBps = elapsedTime > 0 ? (uploadedBytes / 1024 / 1024 / elapsedTime).toFixed(2) : '0.00';

        console.log(`📊 读取进度: ${progress}% (${uploadedMB}/${fileSizeMB} MB) - 读取速度: ${speedMBps} MB/s`);
        lastProgressUpdate = now;
      }
    });

    formData.append('file', fileStream);
    formData.append('name', fileName);
    formData.append('path', targetPath);

    // 设置一个定时器来显示上传状态
    let uploadPhase = '准备上传';
    progressInterval = setInterval(() => {
      const elapsedTime = (Date.now() - startTime) / 1000;
      console.log(`⏳ ${uploadPhase}... 已耗时: ${elapsedTime.toFixed(1)}秒`);
    }, 3000); // 每3秒显示一次状态
    try {
      uploadPhase = '正在上传';

      // 发送上传请求
      const response = await axios.post(OSS_UPLOAD_URL, formData, {
        headers: {
          ...formData.getHeaders(),
          'Content-Type': 'multipart/form-data'
        },
        timeout: 300000, // 5分钟超时，因为文件可能比较大
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
        // 保留onUploadProgress回调，如果能触发就更好
        onUploadProgress: (progressEvent) => {
          clearInterval(progressInterval); // 如果回调触发了，就停止定时器

          if (progressEvent.total) {
            const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(1);
            const loadedMB = (progressEvent.loaded / 1024 / 1024).toFixed(2);
            const totalMB = (progressEvent.total / 1024 / 1024).toFixed(2);

            // 计算上传速度
            const elapsedTime = (Date.now() - startTime) / 1000;
            const speedMBps = elapsedTime > 0 ? (progressEvent.loaded / 1024 / 1024 / elapsedTime).toFixed(2) : '0.00';

            // 估算剩余时间
            const remainingBytes = progressEvent.total - progressEvent.loaded;
            const remainingTime = speedMBps > 0 ? (remainingBytes / 1024 / 1024 / parseFloat(speedMBps)) : 0;
            const remainingTimeStr = remainingTime > 60 ? `${Math.ceil(remainingTime / 60)}分钟` : `${Math.ceil(remainingTime)}秒`;

            console.log(`🚀 网络上传进度: ${progress}% (${loadedMB}/${totalMB} MB) - 速度: ${speedMBps} MB/s - 预计剩余: ${remainingTimeStr}`);
          }
        }
      });

      // 清除进度定时器
      clearInterval(progressInterval);
      return response.data;
    } catch (error) {
      // 清除进度定时器
      clearInterval(progressInterval);
      throw error;
    }
  } catch (error) {
    console.error(`❌ ${fileName} 上传失败:`);
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应数据:', error.response.data);
    } else if (error.request) {
      console.error('🌐 请求错误:', error.message);
    } else {
      console.error('⚠️ 错误信息:', error.message);
    }
    throw error;
  }
}

// 主函数：批量上传文件
async function publishToOSS() {
  try {
    console.log('🚀 开始发布到OSS...');
    console.log(`🖥️  当前平台: ${platform}-${arch}`);

    const targetPath = getPathByPlatform();
    console.log(`📂 目标路径: ${targetPath}`);

    const filesToUpload = getFilesToUpload();

    if (filesToUpload.length === 0) {
      console.log('⚠️  没有找到需要上传的文件');
      return;
    }

    // 计算总文件大小
    let totalSize = 0;
    filesToUpload.forEach(file => {
      const stats = fs.statSync(file.filePath);
      totalSize += stats.size;
    });
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

    console.log(`\n📦 找到 ${filesToUpload.length} 个文件需要上传 (总大小: ${totalSizeMB} MB):`);
    filesToUpload.forEach((file, index) => {
      const stats = fs.statSync(file.filePath);
      const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`  ${index + 1}. ${file.fileName} (${sizeMB} MB)`);
    });

    console.log('\n' + '='.repeat(60));

    // 逐个上传文件（OSS不支持批量上传）
    const results = [];
    const overallStartTime = Date.now();

    for (let i = 0; i < filesToUpload.length; i++) {
      const file = filesToUpload[i];
      console.log(`\n📤 [${i + 1}/${filesToUpload.length}] 准备上传: ${file.fileName}`);

      try {
        const result = await uploadFile(file.filePath, file.fileName, targetPath);
        results.push({
          fileName: file.fileName,
          success: true,
          result: result
        });
        console.log(`✅ [${i + 1}/${filesToUpload.length}] ${file.fileName} 上传完成`);
      } catch (error) {
        results.push({
          fileName: file.fileName,
          success: false,
          error: error.message
        });
        console.error(`❌ [${i + 1}/${filesToUpload.length}] ${file.fileName} 上传失败: ${error.message}`);
      }

      // 显示整体进度
      const overallProgress = ((i + 1) / filesToUpload.length * 100).toFixed(1);
      console.log(`📊 整体进度: ${overallProgress}% (${i + 1}/${filesToUpload.length})`);

      if (i < filesToUpload.length - 1) {
        console.log('-'.repeat(40));
      }
    }

    const overallEndTime = Date.now();
    const overallTime = ((overallEndTime - overallStartTime) / 1000).toFixed(1);
    const overallSpeed = (totalSize / 1024 / 1024 / overallTime).toFixed(2);

    // 输出最终结果
    console.log('\n' + '='.repeat(60));
    console.log('📋 上传结果汇总:');
    console.log(`⏱️  总耗时: ${overallTime}秒`);
    console.log(`🚀 总体平均速度: ${overallSpeed} MB/s`);
    console.log(`📊 总上传大小: ${totalSizeMB} MB`);

    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    console.log(`✅ 成功: ${successCount} 个文件`);
    console.log(`❌ 失败: ${failCount} 个文件`);

    if (failCount > 0) {
      console.log('\n💥 失败的文件详情:');
      results.filter(r => !r.success).forEach((r, index) => {
        console.log(`  ${index + 1}. ${r.fileName}: ${r.error}`);
      });
      process.exit(1);
    } else {
      console.log('\n🎉 所有文件上传成功！');
      console.log('🔗 上传目标: ' + OSS_UPLOAD_URL);
    }

  } catch (error) {
    console.error('❌ 发布过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行发布
if (require.main === module) {
  publishToOSS().catch(error => {
    console.error('发布失败:', error);
    process.exit(1);
  });
}

module.exports = {
  publishToOSS,
  uploadFile,
  getFilesToUpload,
  getPathByPlatform
};
