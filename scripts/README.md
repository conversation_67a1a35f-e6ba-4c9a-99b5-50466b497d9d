# Electron Builder 配置生成器

这个脚本用于动态生成 `electron-builder.json` 配置文件，支持不同环境和架构的配置。

## 功能特性

- 🏗️ 支持多环境配置（production、sit、development）
- 💻 支持多架构配置（x64、arm64、ia32）
- 📁 自定义输出文件名和路径
- ✅ 配置验证和错误检查
- 🎨 友好的命令行界面
- 📦 可作为模块导入使用

## 安装和使用

### 1. 直接运行脚本

```bash
# 生成默认配置（production环境，当前架构）
node scripts/generate-builder-config.js

# 生成sit环境配置
node scripts/generate-builder-config.js --env sit

# 生成arm64架构配置
node scripts/generate-builder-config.js --arch arm64

# 自定义输出文件名
node scripts/generate-builder-config.js --output my-builder-config.json

# 查看帮助
node scripts/generate-builder-config.js --help
```

### 2. 通过 npm scripts 使用

在 `package.json` 中添加以下脚本：

```json
{
  "scripts": {
    "build:config": "node scripts/generate-builder-config.js",
    "build:config:sit": "node scripts/generate-builder-config.js --env sit",
    "build:config:dev": "node scripts/generate-builder-config.js --env development",
    "prebuild": "npm run build:config",
    "electron:build": "electron-builder",
    "electron:build:sit": "npm run build:config:sit && electron-builder"
  }
}
```

然后运行：

```bash
npm run build:config
npm run electron:build:sit
```

### 3. 作为模块使用

```javascript
const { getConfig, generateBuilderConfig } = require('./scripts/generate-builder-config');

// 获取配置对象
const config = getConfig({ env: 'sit', arch: 'arm64' });

// 生成配置文件
generateBuilderConfig({
  env: 'production',
  arch: 'x64',
  output: 'custom-builder.json',
});
```

## 命令行选项

| 选项       | 简写 | 描述                                    | 默认值                  |
| ---------- | ---- | --------------------------------------- | ----------------------- |
| `--env`    | `-e` | 设置环境 (production, sit, development) | `production`            |
| `--arch`   | `-a` | 设置目标架构 (x64, arm64, ia32)         | 当前系统架构            |
| `--output` | `-o` | 输出文件名                              | `electron-builder.json` |
| `--help`   | `-h` | 显示帮助信息                            | -                       |

## 环境配置

### Production 环境

- 应用 ID: `com.alink.app`
- 产品名称: `领慧助手`
- 输出目录: `release`

### SIT 环境

- 应用 ID: `com.ailinksit.app`
- 产品名称: `领慧助手测试版`
- 输出目录: `release`

### Development 环境

- 应用 ID: `com.alink.app`
- 产品名称: `领慧助手 (开发版)`
- 输出目录: `release-dev`

## 生成的配置结构

```json
{
  "appId": "com.alink.app",
  "productName": "领慧助手",
  "files": [
    "dist/**/*",
    "main.js",
    "preload.js",
    "clients/**/*",
    "!clients/**/*.ts",
    "!clients/**/*.tsx",
    "resources/**/*",
    "utils/**/*",
    "build/**/*"
  ],
  "directories": {
    "output": "release"
  },
  "mac": {
    "category": "public.app-category.developer-tools",
    "target": [
      {
        "target": "dmg",
        "arch": ["x64"]
      }
    ],
    "icon": "build/icons/icon.icns",
    "artifactName": "${productName}-${version}-${arch}.${ext}"
  },
  "win": {
    "target": ["nsis"],
    "icon": "build/icons/icon.ico"
  },
  "nsis": {
    "oneClick": false,
    "allowElevation": true,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true
  }
}
```

## 配置验证

脚本会自动验证以下内容：

- ✅ 必需字段是否存在
- ✅ 图标文件是否存在
- ✅ 输出目录是否可创建

## 错误处理

- 如果配置验证失败，脚本会退出并显示错误信息
- 如果图标文件不存在，会显示警告但不会阻止生成
- 如果输出目录不存在，会自动创建

## 示例用法

```bash
# 为不同平台生成配置
NODE_ENV=sit node scripts/generate-builder-config.js --arch arm64
NODE_ENV=production node scripts/generate-builder-config.js --arch x64

# 在CI/CD中使用
npm run build:config:sit
electron-builder --config electron-builder.json
```

## 注意事项

1. 确保 `build/icons/` 目录下有相应的图标文件
2. 脚本会覆盖现有的配置文件
3. 建议在构建前运行此脚本以确保配置是最新的
