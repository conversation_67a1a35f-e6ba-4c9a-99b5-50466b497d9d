# 环境配置文件生成器

这个脚本用于动态生成 `.env` 配置文件，支持不同环境的配置管理。

## 功能特性

- 🌍 支持多环境配置（production、sit、development）
- 📁 自定义输出文件名和路径
- ✅ 配置验证和 URL 格式检查
- 🔒 敏感信息保护（在日志中隐藏）
- 🎨 友好的命令行界面
- 📦 可作为模块导入使用
- 🔄 文件覆盖保护机制

## 安装和使用

### 1. 完整版脚本 (scripts/generate-env-config.js)

```bash
# 生成默认配置（production环境）
node scripts/generate-env-config.js

# 生成sit环境配置
node scripts/generate-env-config.js --env sit

# 生成development环境配置
node scripts/generate-env-config.js --env development

# 自定义输出文件名
node scripts/generate-env-config.js --output .env.local

# 强制覆盖现有文件
node scripts/generate-env-config.js --env sit --force

# 查看帮助
node scripts/generate-env-config.js --help
```

### 2. 简化版脚本 (generate-env.js)

```bash
# 生成production环境配置
node generate-env.js production

# 生成sit环境配置
node generate-env.js sit

# 指定输出文件
node generate-env.js sit .env.sit
```

### 3. 通过 npm scripts 使用

在 `package.json` 中添加以下脚本：

```json
{
  "scripts": {
    "generate:env": "node scripts/generate-env-config.js",
    "generate:env:sit": "node scripts/generate-env-config.js --env sit",
    "generate:env:prod": "node scripts/generate-env-config.js --env production",
    "generate:env:force": "node scripts/generate-env-config.js --force",
    "prebuild": "npm run generate:env",
    "prebuild:sit": "npm run generate:env:sit"
  }
}
```

然后运行：

```bash
npm run generate:env:sit
npm run prebuild:sit
```

### 4. 作为模块使用

```javascript
const { getEnvConfig, generateEnvConfig } = require('./scripts/generate-env-config');

// 获取配置对象
const config = getEnvConfig('sit');

// 生成配置文件
generateEnvConfig({
  env: 'production',
  output: '.env.prod',
  force: true,
});
```

## 命令行选项

### 完整版脚本选项

| 选项       | 简写 | 描述                                    | 默认值       |
| ---------- | ---- | --------------------------------------- | ------------ |
| `--env`    | `-e` | 设置环境 (production, sit, development) | `production` |
| `--output` | `-o` | 输出文件名                              | `.env`       |
| `--force`  | `-f` | 强制覆盖现有文件                        | `false`      |
| `--help`   | `-h` | 显示帮助信息                            | -            |

### 简化版脚本参数

```bash
node generate-env.js [环境] [输出文件]
```

## 环境配置

### Production 环境

- 服务器地址: `https://ai.sf-express.com`
- 工作室名称: `alink`
- 应用版本: `1.0.1`

### SIT 环境

- 服务器地址: `https://ai-dev3.sit.sf-express.com`
- 工作室名称: `ailinksit`
- 应用版本: `1.0.1`

### Development 环境

- 服务器地址: `https://ai-dev3.sit.sf-express.com`
- 工作室名称: `ailinksit-dev`
- 应用版本: `1.0.1-dev`

## 生成的.env 文件格式

```env
# 环境配置文件 - SIT
# 自动生成于: 2024-01-01T00:00:00.000Z
# 请勿手动修改此文件，使用 npm run generate:env 重新生成

BASE_SERVER_MARKET_URL="https://ai-dev3.sit.sf-express.com/server-api/mcp-square-api/api/v1"
CAS_URL="https://cas.sit.sf-express.com"
CAS_SERVICE_ID="ai-dev3.sit.sf-express.com"
TJ_URL="https://ai-dev3.sit.sf-express.com/server-api/apis-auth"
TJ_APP_KEY="inc-aiplat-core-mcp"
TJ_APP_SECRET="inc-aiplat-core-mcp"
STUDIO_NAME="ailinksit"
APP_VERSION="1.0.1"
```

## 配置验证

脚本会自动验证以下内容：

- ✅ 必需字段是否存在
- ✅ URL 格式是否正确
- ✅ 配置完整性检查

## 安全特性

- 🔒 在控制台输出中隐藏敏感信息（KEY、SECRET）
- 🛡️ 文件覆盖保护（需要 `--force` 参数）
- ✅ 输入验证和错误处理

## 高级功能

### 配置比较

```javascript
const { readEnvFile, compareConfigs } = require('./scripts/generate-env-config');

const oldConfig = readEnvFile('.env.old');
const newConfig = readEnvFile('.env');
const diff = compareConfigs(oldConfig, newConfig);

console.log('配置差异:', diff);
```

### 从现有文件读取配置

```javascript
const { readEnvFile } = require('./scripts/generate-env-config');

const config = readEnvFile('.env.sit');
console.log('当前配置:', config);
```

## 示例用法

```bash
# 开发流程
npm run generate:env:sit
npm run build:config:sit
npm run dev

# 生产构建
npm run generate:env:prod
npm run build:config
npm run build

# CI/CD 流程
NODE_ENV=sit npm run generate:env
npm run build

# 批量生成不同环境配置
node scripts/generate-env-config.js --env sit --output .env.sit
node scripts/generate-env-config.js --env production --output .env.prod
```

## 与 electron-builder 集成

结合 electron-builder 配置生成器使用：

```bash
# 生成完整的构建配置
npm run generate:env:sit
npm run build:config:sit
npm run electron:build
```

## 注意事项

1. 🔄 脚本会在文件头部添加时间戳和警告信息
2. 📝 建议将生成的 `.env` 文件添加到 `.gitignore`
3. 🔒 敏感配置信息请妥善保管
4. ⚡ 建议在构建前自动生成配置文件
5. 🛠️ 可以根据项目需要扩展更多环境配置

## 错误处理

- 如果环境不存在，会使用默认的 production 配置
- 如果 URL 格式无效，会显示错误并退出
- 如果文件已存在且未使用 `--force`，会提示用户
- 所有错误都会显示友好的中文提示信息
