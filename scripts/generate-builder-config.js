#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    env: process.env.NODE_ENV || 'production',
    arch: process.arch,
    output: 'electron-builder.json',
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--env':
      case '-e':
        options.env = args[++i];
        break;
      case '--arch':
      case '-a':
        options.arch = args[++i];
        break;
      case '--output':
      case '-o':
        options.output = args[++i];
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      default:
        if (arg.startsWith('--')) {
          console.warn(`⚠️  未知参数: ${arg}`);
        }
    }
  }

  return options;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
📦 Electron Builder 配置生成器

用法:
  node generate-builder-config.js [选项]

选项:
  -e, --env <环境>      设置环境 (production, sit, development)
  -a, --arch <架构>     设置目标架构 (x64, arm64, ia32)
  -o, --output <文件>   输出文件名 (默认: electron-builder.json)
  -h, --help           显示帮助信息

示例:
  node generate-builder-config.js --env sit --arch arm64
  node generate-builder-config.js -e production -o builder-config.json

环境变量:
  NODE_ENV             设置构建环境
  `);
}

/**
 * 获取electron-builder配置
 * @param {Object} options 配置选项
 * @returns {Object} electron-builder配置对象
 */
function getConfig(options = {}) {
  const { env = 'production', arch = process.arch } = options;
  const platform = process.platform;
  const isWindows = platform === 'win32' || platform === 'win64';
  ''
  const isMac = platform === 'darwin';
  const ossDir = isWindows ? 'win-x64' : isMac ? `mac-${arch}` : '';
  const isSit = env === 'sit';
  const isDev = env === 'development';

  const arsarUnpackMap = {
    win: [
      "external/windows-mset/**/*",
      "external/windows-parser/**/*",
      "external/filesystem-mcp.exe"
    ],
    intel: [
      "external/intel-mset/**/*",
      "external/intel-parser/**/*",
      "external/filesystem-mcp"
    ],
    arm64: [
      "external/arm64-mset/**/*",
      "external/arm64-parser/**/*",
      "external/filesystem-mcp"
    ]
  }

  let arsarUnpack = [];

  if (isWindows) {
    arsarUnpack = arsarUnpackMap.win;
  } else if (isMac) {
    arsarUnpack = arch === 'arm64' ? arsarUnpackMap.arm64 : arsarUnpackMap.intel;
  }

  // const arsarUnpack = isWindows ? arsarUnpackMap.win : isMac ? arsarUnpackMap.arm64 : arsarUnpackMap.intel;

  const config = {
    "appId": isSit ? "com.ailinksit.app" : "com.alink.app",
    "productName": isSit ? "领慧助手测试版" : "领慧助手",
    "files": [
      "dist/**/*",
      "main.js",
      "preload.js",
      "clients/**/*",
      "!clients/**/*.ts",
      "!clients/**/*.tsx",
      "resources/**/*",
      "external/**/*",
      "utils/**/*",
      "build/**/*",
      ".env"
    ],
    asarUnpack: arsarUnpack,
    publish: [
      {
        provider: 'generic',
        url: isSit ? `https://mcp-square-inc-aiplat-core-guian-guian1-oss.sit.sf-express.com/v1/AUTH_mcp-square-INC-AIPLAT-CORE/client-pkg/release/${ossDir}`
        : `https://mcp-square-inc-aiplat-core-shenzhen-longhua1-oss.sf-express.com/v1/AUTH_mcp-square-INC-AIPLAT-CORE/client-pkg/release/${ossDir}`
      }
    ],
    "directories": {
      "output": "release"
    },
    "mac": {
      "category": "public.app-category.developer-tools",
      "identity": isWindows ? null : undefined,
      "protocols": [
      {
        "name": isSit ? "ailinksit": "alink",
        "schemes": [isSit ? "ailinksit": 'alink']
      }
    ],
      "target": [
        {
          "target": "dmg",
          "arch": [arch]
        },
        {
          "target": "zip",
          "arch": [arch]
        }
      ],
      "icon": "build/icons/icon.icns",
      "artifactName": "alink-setup.mac-${arch}.${version}.${ext}",
    },
    "win": {
      "target": ["nsis", "zip"],
      "icon": "build/icons/icon.ico",
      "artifactName": "alink-setup.win-${arch}.${version}.${ext}",
      "protocols": [
      {
        "name": isSit ? "ailinksit": "alink",
        "schemes": [isSit ? "ailinksit": 'alink']
      }
    ],
    },
    "nsis": {
      "oneClick": false,
      "allowElevation": true,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "perMachine": false
    }
  };

  // 开发环境特殊配置
  if (isDev) {
    config.directories.output = "release-dev";
    config.productName += " (开发版)";
  }

  return config;
}

/**
 * 验证配置
 * @param {Object} config 配置对象
 * @returns {boolean} 是否有效
 */
function validateConfig(config) {
  const required = ['appId', 'productName', 'files'];

  for (const field of required) {
    if (!config[field]) {
      console.error(`❌ 缺少必需字段: ${field}`);
      return false;
    }
  }

  // 验证文件路径
  const iconPaths = [
    config.mac?.icon,
    config.win?.icon
  ].filter(Boolean);

  for (const iconPath of iconPaths) {
    if (!fs.existsSync(iconPath)) {
      console.warn(`⚠️  图标文件不存在: ${iconPath}`);
    }
  }

  return true;
}

/**
 * 生成electron-builder.json文件
 * @param {Object} options 选项
 */
function generateBuilderConfig(options = {}) {
  try {
    const config = getConfig(options);

    // 验证配置
    if (!validateConfig(config)) {
      process.exit(1);
    }

    const configPath = path.join(process.cwd(), options.output || 'electron-builder.json');

    // 将配置对象转换为格式化的JSON字符串
    const jsonContent = JSON.stringify(config, null, 2);

    // 确保目录存在
    const dir = path.dirname(configPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 写入文件
    fs.writeFileSync(configPath, jsonContent, 'utf8');

    console.log('✅ electron-builder配置生成成功!');
    console.log(`📁 文件路径: ${configPath}`);
    console.log(`🏗️  环境: ${options.env}`);
    console.log(`💻 架构: ${options.arch}`);
    console.log(`📱 应用名称: ${config.productName}`);
    console.log(`🆔 应用ID: ${config.appId}`);
    console.log(`📦 输出目录: ${config.directories.output}`);

  } catch (error) {
    console.error('❌ 生成配置文件失败:', error.message);
    process.exit(1);
  }
}

// 主函数
function main() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  // 设置环境变量
  process.env.NODE_ENV = options.env;

  generateBuilderConfig(options);
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  getConfig,
  generateBuilderConfig,
  validateConfig
};
