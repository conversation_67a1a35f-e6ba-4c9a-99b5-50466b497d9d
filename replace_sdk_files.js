const fs = require('node:fs');
const path = require('node:path');

// 脚本假设 'sse.e.js' 和 'sse.c.js' 文件位于项目根目录 (与此脚本相同的目录)
const esmSourceFilename = 'sse.e.js';
const cjsSourceFilename = 'sse.c.js';

// 目标路径
const esmDestinationPath = 'node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js';
const cjsDestinationPath = 'node_modules/@modelcontextprotocol/sdk/dist/cjs/client/sse.js';

// 获取项目根目录的绝对路径
const projectRoot = __dirname;

// 构建源文件的绝对路径
const esmSourcePath = path.join(projectRoot, esmSourceFilename);
const cjsSourcePath = path.join(projectRoot, cjsSourceFilename);

// 构建目标文件的绝对路径
const esmDestPathAbs = path.join(projectRoot, esmDestinationPath);
const cjsDestPathAbs = path.join(projectRoot, cjsDestinationPath);

function copyFileIfSourceExists(sourcePath, destinationPath, sourceFilename) {
  if (fs.existsSync(sourcePath)) {
    try {
      // 确保目标目录存在
      const destDir = path.dirname(destinationPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
        console.log(`创建目录成功: ${destDir}`);
      }
      fs.copyFileSync(sourcePath, destinationPath);
      console.log(`成功将 ${sourceFilename} (${sourcePath}) 覆盖到 ${destinationPath}`);
    } catch (err) {
      console.error(`复制 ${sourceFilename} (${sourcePath}) 到 ${destinationPath} 时出错:`, err);
    }
  } else {
    console.warn(`警告: 源文件 ${sourceFilename} (${sourcePath}) 未找到。跳过此操作。`);
  }
}

console.log('开始执行文件覆盖脚本...');
console.log(`项目根目录: ${projectRoot}`);

// 执行覆盖操作
copyFileIfSourceExists(esmSourcePath, esmDestPathAbs, esmSourceFilename);
copyFileIfSourceExists(cjsSourcePath, cjsDestPathAbs, cjsSourceFilename);

console.log('脚本执行完毕。');
