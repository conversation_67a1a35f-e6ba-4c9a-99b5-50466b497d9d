const { contextBridge, ipcRenderer, app, clipboard } = require('electron');
const { homedir } = require('os');
const {
  openExternalUrl,
  getModelProviderList,
  getModelListByProvider,
  getAllModelList,
  getEnabledModelList,
  enabledModel,
  setDefaultModel,
  getMcpList,
  startMcp,
  stopMcp,
  saveMcp,
  deleteMcp,
  getMcpMarketList,
  saveProviderStatus,
  installUv,
  installBun,
  getUvStatus,
  getBunStatus,
  getUvPath,
  getBunPath,
  installMset,
  getMsetStatus,
  getMsetPath,
  getToolList,
  chat,
  createSession,
  getSessionList,
  deleteSession,
  deleteAllMessages,
  updateSession,
  getMessageList,
  getToolListByMcp,
  onMessage,
  removeMessageListener,
  deleteMessage,
  clearContext,
  onClearContext,
  removeClearContextListener,
  sendInterruptStream,
  // 中断流
  onInterruptStream,
  // 移除中断流监听
  removeInterruptStreamListener,
  // 保存模型厂商当前API Key
  saveProviderCurrentAPIKey,
  // 验证模型厂商
  verifyModelProvider,
  // 重置模型厂商默认模型URL
  resetDefaultBaseModelUrl,
  // 更新模型厂商
  updateProvider,
  // 获取二维码
  getQrCode,
  // 获取二维码状态
  getQrCodeStatus,
  // 检查token
  checkToken,
  // 获取用户
  getUser,
  // 保存用户
  saveUser,
  logout,
  onLogout,
  // 检查最新版本
  checkLatestVersion,
  // 获取应用版本
  getAppVersion,
  installNodejs,
  getNodejsPath,
  getNodejsStatus,
  enabledTool,
  disabledTool,
  summarizeMessageTitle,
  getDefaultApiKey,
  offLogout,
  onUpdateProviderApiKey,
  offUpdateProviderApiKey,
  uploadAttachment,
  uploadAttachmentFromClipboard,
  getAttachmentsDirectory,
  deleteAttachment,
  readAttachment,
  parseDocument,
  openAiPlatformUrl,
} = require('./clients/web/common-api');
contextBridge.exposeInMainWorld('bdpStudioAPI', {
  minimize: () => ipcRenderer.send('window-minimize'),
  fullscreen: () => ipcRenderer.invoke('toggle-fullscreen'),
  maximize: () => ipcRenderer.send('window-maximize'),
  close: () => ipcRenderer.send('window-close'),
  sensors: async (event, properties) => {
    const user = await getUser();
    if (user) {
      ipcRenderer.send('track-event', user.userId, event, properties);
    }
  },

  getUpdateServerUrl: () => {
    return process.env.UPDATE_SERVER_URL;
  },
  // 获取基础服务地址
  getBaseServerUrl: () => {
    return process.env.BASE_SERVER_MARKET_URL;
  },
  isWindows: () => {
    // return true;
    return process.platform === 'win32' || process.platform === 'win64';
  },
  isMac: () => {
    return process.platform === 'darwin';
  },
  // 复制文本到剪贴板
  copyClipboard: (text) => {
    clipboard.writeText(text);
  },
  getHomePath: () => {
    return homedir();
  },
  getProcessPath: () => {
    return process.cwd();
  },
  getUserDataPath: () => {
    return app.getPath('userData');
  },
  openExternalUrl,
  openAiPlatformUrl,
  getUser,
  // 获取模型厂商列表
  getModelProviderList,
  // 根据模型厂商列表返回模型列表
  getModelListByProvider,
  // 获取所有模型列表
  getAllModelList,
  // 获取开启的模型列表
  getEnabledModelList,
  // 开启或者关闭模型
  enabledModel,
  // 设置默认模型
  setDefaultModel,
  // 获取mcp 服务器列表
  getMcpList,
  // 启动mcp 服务器
  startMcp,
  // 停止mcp 服务器
  stopMcp,
  // 更新mcp 服务器
  saveMcp,
  // 删除mcp 服务器
  deleteMcp,
  // 获取mcp市场列表
  getMcpMarketList,
  // cookie 更新
  onCookieUpdate: (callback) => {
    ipcRenderer.on('cookie-update', (_, cookie) => callback(cookie));
  },
  // 删除cookie 更新监听
  removeCookieListener: () => ipcRenderer.removeAllListeners('cookie-update'),

  // 获取待处理的URL
  getPendingUrl: () => {
    return ipcRenderer.invoke('get-pending-url');
  },

  // 清空待处理的URL
  clearPendingUrl: () => {
    return ipcRenderer.invoke('clear-pending-url');
  },

  // 监听URL接收事件
  onMcpUrlReceived: (callback) => {
    ipcRenderer.on('mcp-url-received', (_, result) => callback(result));
  },

  // 移除URL接收事件监听
  removeMcpUrlListener: () => {
    ipcRenderer.removeAllListeners('mcp-url-received');
  },
  // 保存模型厂商开启或者关闭的状态
  saveProviderStatus,
  // 安装uv
  installUv,
  // 安装bun
  installBun,
  // 获取uv状态
  getUvStatus,
  // 获取bun状态
  getBunStatus,
  // 获取uv路径
  getUvPath,
  // 获取bun路径
  getBunPath,
  // 安装nodejs
  installNodejs,
  // 获取nodejs路径
  getNodejsPath,
  // 获取nodejs状态
  getNodejsStatus,
  // 安装mset
  installMset,
  // 获取mset状态
  getMsetStatus,
  // 获取mset路径
  getMsetPath,
  // 获取工具列表
  getToolList,
  // 获取mcp工具列表
  getToolListByMcp,
  // 启用工具
  enabledTool,
  // 禁用工具
  disabledTool,
  // 聊天
  chat,
  // 创建会话
  createSession,
  // 获取会话列表
  getSessionList,
  // 删除会话
  deleteSession,
  // 更新会话
  updateSession,
  // 获取消息列表
  getMessageList,
  // 监听消息
  onMessage,
  removeMessageListener,
  // 删除消息
  deleteMessage,
  // 删除所有消息
  deleteAllMessages,
  // 清除上下文
  clearContext,
  // 监听清除上下文
  onClearContext,
  // 移除清除上下文监听
  removeClearContextListener,
  //  监听中断流
  onInterruptStream,
  // 移除中断流监听
  removeInterruptStreamListener,
  // 发送中断流
  sendInterruptStream,
  // 保存模型厂商当前API Key
  saveProviderCurrentAPIKey,
  // 验证模型厂商
  verifyModelProvider,
  // 重置模型厂商默认模型URL
  resetDefaultBaseModelUrl,
  // 更新模型厂商
  updateProvider,
  // 添加日志API
  logger: {
    log: (...args) => ipcRenderer.send('console-log', 'log', ...args),
    info: (...args) => ipcRenderer.send('console-log', 'info', ...args),
    warn: (...args) => ipcRenderer.send('console-log', 'warn', ...args),
    error: (...args) => ipcRenderer.send('console-log', 'error', ...args),
    debug: (...args) => ipcRenderer.send('console-log', 'debug', ...args),
  },
  // 获取二维码
  getQrCode,
  // 获取二维码状态
  getQrCodeStatus,
  // 检查token
  checkToken,
  // 保存用户
  saveUser,
  // 初始化
  init: () => ipcRenderer.invoke('init'),
  // 退出登录
  logout,
  onLogout,
  offLogout,
  // 检查最新版本
  checkLatestVersion,
  // 获取应用版本
  getAppVersion,

  // 自动更新相关 API
  updater: {
    // 检查更新
    checkForUpdates: () => ipcRenderer.invoke('updater-check-for-updates'),
    // 退出并安装更新
    quitAndInstall: () => ipcRenderer.invoke('updater-quit-and-install'),

    downloadUpdate: () => ipcRenderer.invoke('updater-download-update'),
     // 监听更新事件
    onCheckingForUpdate: (callback) => {
      ipcRenderer.on('updater-checking-for-update', callback);
    },
    onUpdateAvailable: (callback) => {
      ipcRenderer.on('updater-update-available', (_, info) => callback(info));
    },
    onUpdateNotAvailable: (callback) => {
      ipcRenderer.on('updater-update-not-available', (_, info) => callback(info));
    },
    onUpdateDownloaded: (callback) => {
      ipcRenderer.on('updater-update-downloaded', (_, info) => callback(info));
    },
    onDownloadProgress: (callback) => {
      ipcRenderer.on('updater-download-progress', (_, progress) => callback(progress));
    },
    onError: (callback) => {
      ipcRenderer.on('updater-error', (_, error) => callback(error));
    },
    onManualCheckNoUpdate: (callback) => {
      ipcRenderer.on('updater-manual-check-no-update', callback);
    },
    onManualCheckError: (callback) => {
      ipcRenderer.on('updater-manual-check-error', (_, error) => callback(error));
    },
    // 移除监听器
    removeAllListeners: () => {
      ipcRenderer.removeAllListeners('updater-checking-for-update');
      ipcRenderer.removeAllListeners('updater-update-available');
      ipcRenderer.removeAllListeners('updater-update-not-available');
      ipcRenderer.removeAllListeners('updater-update-downloaded');
      ipcRenderer.removeAllListeners('updater-download-progress');
      ipcRenderer.removeAllListeners('updater-error');
      ipcRenderer.removeAllListeners('updater-manual-check-no-update');
      ipcRenderer.removeAllListeners('updater-manual-check-error');
    }
  },
  summarizeMessageTitle,
  getDefaultApiKey,
  onUpdateProviderApiKey,
  offUpdateProviderApiKey,
  // 附件相关API
  uploadAttachment,
  uploadAttachmentFromClipboard,
  getAttachmentsDirectory,
  deleteAttachment,
  readAttachment,
  parseDocument,

});
