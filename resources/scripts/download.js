

/**
 * Format bytes as human-readable text
 * @param {number} bytes Number of bytes
 * @param {boolean} si True to use metric (SI) units, false for binary
 * @returns {string} Formatted string
 */
function formatBytes(bytes, si = false) {
  const thresh = si ? 1000 : 1024;
  if (Math.abs(bytes) < thresh) {
    return bytes + ' B';
  }
  const units = si
    ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
  let u = -1;
  const r = 10**1;
  do {
    bytes /= thresh;
    ++u;
  } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);
  return bytes.toFixed(1) + ' ' + units[u];
}

/**
 * Create a progress bar string
 * @param {number} progress Progress percentage (0-100)
 * @param {number} width Width of the progress bar
 * @returns {string} Progress bar string
 */
function createProgressBar(progress, width = 30) {
  const filled = Math.round(width * progress / 100);
  const empty = width - filled;
  return '█'.repeat(filled) + '░'.repeat(empty);
}

/**
 * Clear the current line in terminal
 */
function clearLine() {
  process.stdout.write('\r' + ' '.repeat(process.stdout.columns || 120) + '\r');
}

/**
 * Downloads a file from a URL with redirect handling and progress display
 * @param {string} url The URL to download from
 * @param {string} destinationPath The path to save the file to
 * @param {boolean} showProgress Whether to show download progress (default: true)
 * @returns {Promise<void>} Promise that resolves when download is complete
 */
async function downloadWithRedirects(url, destinationPath, showProgress = true) {
  return new Promise((resolve, reject) => {
    const http = require('http');
    const https = require('https');
    const fs = require('fs');

    let downloadedBytes = 0;
    let totalBytes = 0;
    let startTime = Date.now();
    let lastUpdateTime = 0;
    let progressStarted = false;

    const request = (url) => {
      const isHttps = url.startsWith('https://')
      const requestModule = isHttps ? https : http

      requestModule
        .get(url, (response) => {
          if (response.statusCode == 301 || response.statusCode == 302) {
            request(response.headers.location)
            return
          }
          if (response.statusCode !== 200) {
            reject(new Error(`Download failed: ${response.statusCode} ${response.statusMessage}`))
            return
          }

          // Get total file size from headers
          totalBytes = parseInt(response.headers['content-length'] || '0', 10);

          const file = fs.createWriteStream(destinationPath)

                    // Track download progress
          response.on('data', (chunk) => {
            downloadedBytes += chunk.length;

                        if (showProgress && totalBytes > 0) {
              const currentTime = Date.now();
              // Update progress every 100ms to avoid too frequent updates
              if (currentTime - lastUpdateTime > 100) {
                const progress = (downloadedBytes / totalBytes) * 100;
                const elapsed = (currentTime - startTime) / 1000;
                const speed = downloadedBytes / elapsed;
                const eta = totalBytes > downloadedBytes ? (totalBytes - downloadedBytes) / speed : 0;

                const progressBar = createProgressBar(progress);
                const progressText = `下载进度: ${progressBar} ${progress.toFixed(1)}% ` +
                  `(${formatBytes(downloadedBytes)}/${formatBytes(totalBytes)}) ` +
                  `速度: ${formatBytes(speed)}/s ` +
                  `剩余: ${eta > 0 ? Math.ceil(eta) + 's' : '0s'}`;

                // 首次显示进度时清空屏幕
                if (!progressStarted) {
                  console.log(''); // 添加一个空行
                  progressStarted = true;
                }

                // 清空当前行并输出进度
                clearLine();
                process.stdout.write(progressText);
                lastUpdateTime = currentTime;
              }
            }
          });

          response.pipe(file)

          file.on('finish', () => {
            if (showProgress) {
              // 完全清空进度行并显示完成信息
              clearLine();
              const elapsed = (Date.now() - startTime) / 1000;
              const avgSpeed = downloadedBytes / elapsed;
              console.log(`✅ 下载完成: ${formatBytes(downloadedBytes)} 用时: ${elapsed.toFixed(1)}s 平均速度: ${formatBytes(avgSpeed)}/s`);
            }
            resolve();
          });

          file.on('error', (err) => {
            reject(err);
          });
        })
        .on('error', (err) => {
          reject(err)
        })
    }
    request(url)
  })
}

module.exports = { downloadWithRedirects, formatBytes, createProgressBar, clearLine }