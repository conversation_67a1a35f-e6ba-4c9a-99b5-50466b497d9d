const fs = require('fs')
const path = require('path')
const os = require('os')
const { execSync } = require('child_process')
const tar = require('tar')
const AdmZip = require('adm-zip')
const { downloadWithRedirects } = require('./download')

// Base URL for downloading Node.js binaries
const NODEJS_RELEASE_BASE_URL = 'https://nodejs.org/download/release'
const DEFAULT_NODEJS_VERSION = 'v22.16.0' // LTS version

// Mapping of platform+arch to binary package name
const NODEJS_PACKAGES = {
  'darwin-arm64': 'node-v22.16.0-darwin-arm64.tar.gz',
  'darwin-x64': 'node-v22.16.0-darwin-x64.tar.gz',
  'win32-x64': 'node-v22.16.0-win-x64.zip',
  'win32-x86': 'node-v22.16.0-win-x86.zip'
}

/**
 * Downloads and extracts the Node.js binary for the specified platform and architecture
 * @param {string} platform Platform to download for (e.g., 'darwin', 'win32', 'linux')
 * @param {string} arch Architecture to download for (e.g., 'x64', 'arm64')
 * @param {string} version Version of Node.js to download
 * @param {boolean} isMusl Whether to use MUSL variant for Linux
 */
async function downloadNodejsBinary(platform, arch, version = DEFAULT_NODEJS_VERSION, isMusl = false) {
  // Handle architecture mapping
  let mappedArch = arch
  if (arch === 'ia32') {
    mappedArch = 'x86'
  }

  const platformKey = isMusl ? `${platform}-musl-${mappedArch}` : `${platform}-${mappedArch}`
  const packageName = NODEJS_PACKAGES[platformKey]

  if (!packageName) {
    console.error(`No binary available for ${platformKey}`)
    console.log('Available packages:', Object.keys(NODEJS_PACKAGES))
    return false
  }

  const studioName = process.env.STUDIO_NAME || '.ailinksit';

  // Create output directory structure
  const binDir = path.join(os.homedir(), `.${studioName}`, 'nodejs')
  // Ensure directories exist
  fs.mkdirSync(binDir, { recursive: true })

  // Download URL for the specific binary
  const downloadUrl = `${NODEJS_RELEASE_BASE_URL}/${version}/${packageName}`
  const tempdir = os.tmpdir()
  const tempFilename = path.join(tempdir, packageName)

  try {
    console.log(`Downloading Node.js ${version} for ${platformKey}...`)
    console.log(`URL: ${downloadUrl}`)

    await downloadWithRedirects(downloadUrl, tempFilename)

    console.log(`Extracting ${packageName} to ${binDir}...`)

    let extractedDirName

    if (packageName.endsWith('.zip')) {
      // 使用 adm-zip 处理 zip 文件 (Windows)
      const zip = new AdmZip(tempFilename)
      zip.extractAllTo(tempdir, true)
      extractedDirName = packageName.replace('.zip', '')
    } else if (packageName.endsWith('.tar.xz')) {
      // 处理 tar.xz 文件 (Linux)
      await tar.x({
        file: tempFilename,
        cwd: tempdir,
        J: true // xz compression
      })
      extractedDirName = packageName.replace('.tar.xz', '')
    } else if (packageName.endsWith('.tar.gz')) {
      // 处理 tar.gz 文件 (macOS)
      await tar.x({
        file: tempFilename,
        cwd: tempdir,
        z: true // gzip compression
      })
      extractedDirName = packageName.replace('.tar.gz', '')
    } else {
      throw new Error(`Unsupported package format: ${packageName}`)
    }

    // Move files from extracted directory to bin directory
    const sourceDir = path.join(tempdir, extractedDirName)

    if (!fs.existsSync(sourceDir)) {
      throw new Error(`Extracted directory not found: ${sourceDir}`)
    }

    // Copy all contents from source directory to bin directory
    const files = fs.readdirSync(sourceDir)
    for (const file of files) {
      const sourcePath = path.join(sourceDir, file)
      const destPath = path.join(binDir, file)

      // If it's a directory, copy recursively
      if (fs.statSync(sourcePath).isDirectory()) {
        copyDirectoryRecursive(sourcePath, destPath)
      } else {
        fs.copyFileSync(sourcePath, destPath)
      }

      // Set executable permissions for non-Windows platforms
      if (platform !== 'win32' && (file === 'node' || file === 'npm' || file === 'npx')) {
        try {
          fs.chmodSync(destPath, '755')
        } catch (error) {
          console.warn(`Warning: Failed to set executable permissions for ${file}: ${error.message}`)
        }
      }
    }

    // Clean up
    fs.unlinkSync(tempFilename)
    fs.rmSync(sourceDir, { recursive: true })

    // Create symlinks or add to PATH instructions
    await createNodejsLinks(binDir, platform)

    console.log(`Successfully installed Node.js ${version} for ${platformKey}`)
    console.log(`Installation directory: ${binDir}`)

    return true
  } catch (error) {
    console.error(`Error installing Node.js for ${platformKey}: ${error.message}`)

    if (fs.existsSync(tempFilename)) {
      fs.unlinkSync(tempFilename)
    }

    // Check if binDir is empty and remove it if so
    try {
      const files = fs.readdirSync(binDir)
      if (files.length === 0) {
        fs.rmSync(binDir, { recursive: true })
        console.log(`Removed empty directory: ${binDir}`)
      }
    } catch (cleanupError) {
      console.warn(`Warning: Failed to clean up directory: ${cleanupError.message}`)
    }

    return false
  }
}

/**
 * Recursively copy directory
 * @param {string} src Source directory
 * @param {string} dest Destination directory
 */
function copyDirectoryRecursive(src, dest) {
  fs.mkdirSync(dest, { recursive: true })
  const files = fs.readdirSync(src)

  for (const file of files) {
    const srcPath = path.join(src, file)
    const destPath = path.join(dest, file)

    if (fs.statSync(srcPath).isDirectory()) {
      copyDirectoryRecursive(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}

/**
 * Create symlinks or provide PATH instructions for Node.js
 * @param {string} binDir Directory where Node.js is installed
 * @param {string} platform Current platform
 */
async function createNodejsLinks(binDir, platform) {
  const studioName = process.env.STUDIO_NAME || '.ailinksit'
  const globalBinDir = path.join(os.homedir(), `.${studioName}`, 'bin')

  // Ensure global bin directory exists
  fs.mkdirSync(globalBinDir, { recursive: true })

  const nodeBinPath = path.join(binDir, 'bin', platform === 'win32' ? 'node.exe' : 'node')
  const npmBinPath = path.join(binDir, 'bin', platform === 'win32' ? 'npm.cmd' : 'npm')
  const npxBinPath = path.join(binDir, 'bin', platform === 'win32' ? 'npx.cmd' : 'npx')

  // For Windows, we'll create batch files that call the actual executables
  if (platform === 'win32') {
    const nodeBatchPath = path.join(globalBinDir, 'node.cmd')
    const npmBatchPath = path.join(globalBinDir, 'npm.cmd')
    const npxBatchPath = path.join(globalBinDir, 'npx.cmd')

    fs.writeFileSync(nodeBatchPath, `@echo off\n"${nodeBinPath}" %*\n`)
    fs.writeFileSync(npmBatchPath, `@echo off\n"${npmBinPath}" %*\n`)
    fs.writeFileSync(npxBatchPath, `@echo off\n"${npxBinPath}" %*\n`)

    console.log('\nWindows installation complete!')
    console.log(`Add the following directory to your PATH: ${globalBinDir}`)
  } else {
    // For Unix-like systems, create symlinks
    try {
      const nodeSymlink = path.join(globalBinDir, 'node')
      const npmSymlink = path.join(globalBinDir, 'npm')
      const npxSymlink = path.join(globalBinDir, 'npx')

      // Remove existing symlinks if they exist
      if (fs.existsSync(nodeSymlink)) fs.unlinkSync(nodeSymlink)
      if (fs.existsSync(npmSymlink)) fs.unlinkSync(npmSymlink)
      if (fs.existsSync(npxSymlink)) fs.unlinkSync(npxSymlink)

      // Create new symlinks
      fs.symlinkSync(nodeBinPath, nodeSymlink)
      fs.symlinkSync(npmBinPath, npmSymlink)
      fs.symlinkSync(npxBinPath, npxSymlink)

      console.log('\nUnix installation complete!')
      console.log(`Add the following directory to your PATH: ${globalBinDir}`)
      console.log('Or run the following command:')
      console.log(`export PATH="${globalBinDir}:$PATH"`)
    } catch (error) {
      console.warn(`Warning: Failed to create symlinks: ${error.message}`)
      console.log(`You can manually add ${path.join(binDir, 'bin')} to your PATH`)
    }
  }
}

/**
 * Detects current platform and architecture
 */
function detectPlatformAndArch() {
  const platform = os.platform()
  const arch = os.arch()
  const isMusl = platform === 'linux' && detectIsMusl()

  return { platform, arch, isMusl }
}

/**
 * Attempts to detect if running on MUSL libc
 */
function detectIsMusl() {
  try {
    // Simple check for Alpine Linux which uses MUSL
    const output = execSync('cat /etc/os-release', { encoding: 'utf8' })
    return output.toLowerCase().includes('alpine')
  } catch (error) {
    try {
      // Alternative check using ldd
      const lddOutput = execSync('ldd --version', { encoding: 'utf8' })
      return lddOutput.toLowerCase().includes('musl')
    } catch (lddError) {
      return false
    }
  }
}

/**
 * Check if Node.js is already installed
 */
function checkExistingNodejs() {
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim()
    console.log(`Node.js is already installed: ${version}`)
    return version
  } catch (error) {
    return null
  }
}

/**
 * Main function to install Node.js
 */
async function installNodejs() {
  console.log('Node.js Green Installation Script')
  console.log('=================================')

  // Check if Node.js is already installed
  const existingVersion = checkExistingNodejs()
  if (existingVersion) {
    console.log('Node.js is already available in PATH.')
    console.log('This script will install a portable version in your home directory.')
  }

  const version = DEFAULT_NODEJS_VERSION
  console.log(`Using Node.js version: ${version}`)

  const { platform, arch, isMusl } = detectPlatformAndArch()

  console.log(
    `Installing Node.js ${version} for ${platform}-${arch}${isMusl ? ' (MUSL)' : ''}...`
  )

  const success = await downloadNodejsBinary(platform, arch, version, isMusl)

  if (success) {
    console.log('\n✅ Node.js installation completed successfully!')
    console.log('\nTo verify the installation, restart your terminal and run:')
    console.log('  node --version')
    console.log('  npm --version')
  } else {
    console.log('\n❌ Node.js installation failed!')
    process.exit(1)
  }
}

// Run the installation
if (require.main === module) {
  installNodejs()
    .then(() => {
      console.log('Installation process completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Installation failed:', error)
      process.exit(1)
    })
}

module.exports = { installNodejs, downloadNodejsBinary }
