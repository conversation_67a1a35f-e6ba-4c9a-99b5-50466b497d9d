<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <style>
        canvas {
            background-color: #f0f0f0;
            display: block;
            margin: 0 auto;
            border: 2px solid #333;
        }
    </style>
</head>
<body>
    <canvas id="tetris" width="300" height="600"></canvas>
    <script>
        const canvas = document.getElementById('tetris');
        const context = canvas.getContext('2d');

        // 音效
        const sounds = {
            rotate: new Audio('https://cdn.jsdelivr.net/gh/kitety/demo@master/sound/rotate.mp3'),
            lineClear: new Audio('https://cdn.jsdelivr.net/gh/kitety/demo@master/sound/line_clear.mp3'),
            gameOver: new Audio('https://cdn.jsdelivr.net/gh/kitety/demo@master/sound/game_over.mp3'),
            drop: new Audio('https://cdn.jsdelivr.net/gh/kitety/demo@master/sound/drop.mp3')
        };

        // 缩放画布以适应不同分辨率
        context.scale(30, 30); // 每个方块30x30像素

        // 创建矩阵函数
        function createMatrix(w, h) {
            const matrix = [];
            while (h--) {
                matrix.push(new Array(w).fill(0));
            }
            return matrix;
        }

        // 创建俄罗斯方块形状
        function createPiece(type) {
            if (type === 'T') {
                return [
                    [0, 1, 0],
                    [1, 1, 1],
                    [0, 0, 0],
                ];
            } else if (type === 'O') {
                return [
                    [2, 2],
                    [2, 2],
                ];
            } else if (type === 'L') {
                return [
                    [0, 0, 3],
                    [3, 3, 3],
                    [0, 0, 0],
                ];
            } else if (type === 'J') {
                return [
                    [4, 0, 0],
                    [4, 4, 4],
                    [0, 0, 0],
                ];
            } else if (type === 'I') {
                return [
                    [0, 5, 0, 0],
                    [0, 5, 0, 0],
                    [0, 5, 0, 0],
                    [0, 5, 0, 0],
                ];
            } else if (type === 'S') {
                return [
                    [0, 6, 6],
                    [6, 6, 0],
                    [0, 0, 0],
                ];
            } else if (type === 'Z') {
                return [
                    [7, 7, 0],
                    [0, 7, 7],
                    [0, 0, 0],
                ];
            }
        }

        // 绘制矩阵
        function drawMatrix(matrix, offset) {
            matrix.forEach((row, y) => {
                row.forEach((value, x) => {
                    if (value) {
                        context.fillStyle = colors[value];
                        context.fillRect(x + offset.x, y + offset.y, 1, 1);
                    }
                });
            });
        }

        // 碰撞检测
        function collide(arena, player, offset = {x: 0, y: 0}) {
            const m = player.matrix;
            const o = player.pos;
            for (let y = 0; y < m.length; ++y) {
                for (let x = 0; x < m[y].length; ++x) {
                    if (m[y][x] &&
                       (arena[y + o.y + offset.y] &&
                        arena[y + o.y + offset.y][x + o.x + offset.x]) !== 0) {
                        return true;
                    }
                }
            }
            return false;
        }

        // 合并方块到场地
        function merge(arena, player) {
            player.matrix.forEach((row, y) => {
                row.forEach((value, x) => {
                    if (value) {
                        arena[y + player.pos.y][x + player.pos.x] = value;
                    }
                });
            });
            // 播放下落音效
            playSound('drop');
        }

        // 旋转矩阵
        function rotate(matrix, dir) {
            const rotated = matrix.map((_, i) =>
                matrix.map(row => row[i])
            );
            if (dir > 0) {
                return rotated.map(row => row.reverse());
            } else {
                return rotated.reverse();
            }
        }

        // 清除行
        function arenaSweep() {
            let rowCount = 1;
            outer: for (let y = this.arena.length - 1; y >= 0; --y) {
                for (let x = 0; x < this.arena[y].length; ++x) {
                    if (this.arena[y][x] === 0) {
                        continue outer;
                    }
                }

                const row = this.arena.splice(y, 1)[0].fill(0);
                this.arena.unshift(row);
                ++y;

                this.player.score += rowCount * 10;
                rowCount *= 2;
            }

            // 如果清除了行，播放音效
            if (rowCount > 1) {
                playSound('lineClear');
            }
        }

        // 重置游戏
        function playerReset() {
            const pieces = 'TJLOSZI';
            this.player.matrix = createPiece(pieces[Math.floor(Math.random() * pieces.length)]);
            this.player.pos.y = 0;
            this.player.pos.x = Math.floor(this.arena[0].length / 2) -
                               Math.floor(this.player.matrix[0].length / 2);

            if (collide(this.arena, this.player)) {
                this.arena.forEach(row => row.fill(0));
                this.player.score = 0;
                // 播放游戏结束音效
                playSound('gameOver');
                alert('游戏结束！');
            }
        }

        // 方块下落
        function playerDrop() {
            this.player.pos.y++;
            if (collide(this.arena, this.player)) {
                this.player.pos.y--;
                merge(this.arena, this.player);
                this.arenaSweep();
                this.playerReset();
                this.updateScore();
            }
            this.dropCounter = 0;
        }

        // 方块移动
        function playerMove(dir) {
            this.player.pos.x += dir;
            if (collide(this.arena, this.player)) {
                this.player.pos.x -= dir;
            }
        }

        // 旋转方块
        function playerRotate(dir) {
            const pos = this.player.pos.x;
            let offset = 1;
            this.player.matrix = rotate(this.player.matrix, dir);

            // 调整位置以防止旋转时超出边界
            while (collide(this.arena, this.player)) {
                this.player.pos.x += offset;
                offset = -(offset + (offset > 0 ? 1 : -1));
                if (offset > this.player.matrix[0].length) {
                    this.player.matrix = rotate(this.player.matrix, -dir);
                    this.player.pos.x = pos;
                    return;
                }
            }
            // 播放旋转音效
            playSound('rotate');
        }

        // 更新游戏
        function update(time = 0) {
            const deltaTime = time - this.lastTime;
            this.lastTime = time;

            this.dropCounter += deltaTime;
            if (this.dropCounter > this.dropInterval) {
                this.playerDrop();
            }

            this.draw();
            requestAnimationFrame(update.bind(this));
        }

        // 绘制游戏
        function draw() {
            context.fillStyle = '#f0f0f0';
            context.fillRect(0, 0, canvas.width, canvas.height);

            drawMatrix(this.arena, {x: 0, y: 0});
            drawMatrix(this.player.matrix, this.player.pos);

            context.fillStyle = '#333';
            context.font = '16px Arial';
            context.fillText('得分: ' + this.player.score, 10, 20);
        }

        // 更新得分
        function updateScore() {
            document.getElementById('score').innerText = this.player.score;
        }

        // 初始化游戏
        function init() {
            // 初始化颜色映射
            colors = [
                null,
                '#FF0D72', // T
                '#0DC2FF', // O
                '#0DFF72', // L
                '#F538FF', // J
                '#FF8E0D', // I
                '#FFE138', // S
                '#387AFF'  // Z
            ];

            // 创建游戏场地
            arena = createMatrix(10, 20);

            // 初始化玩家对象
            player = {
                pos: {x: 0, y: 0},
                matrix: null,
                score: 0
            };

            // 重置玩家
            this.playerReset();

            // 设置下落间隔
            dropCounter = 0;
            dropInterval = 1000;

            // 记录上次更新时间
            lastTime = 0;

            // 绑定键盘事件
            document.addEventListener('keydown', event => {
                if (event.key === 'ArrowLeft') {
                    this.playerMove(-1);
                } else if (event.key === 'ArrowRight') {
                    this.playerMove(1);
                } else if (event.key === 'ArrowDown') {
                    this.playerDrop();
                } else if (event.key === 'q') {
                    this.playerRotate(-1);
                } else if (event.key === 'w') {
                    this.playerRotate(1);
                }
            });

            // 启动游戏循环
            update();
        }

        // 启动游戏
        init();

        // 播放音效函数
        function playSound(soundName) {
            if (sounds[soundName]) {
                // 重置音频到开头
                sounds[soundName].currentTime = 0;
                // 播放音效
                sounds[soundName].play().catch(error => {
                    console.log('音效播放失败:', error);
                });
            }
        }
    </script>
</body>
</html>
