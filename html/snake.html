<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇</title>
    <style>
        canvas {
            background-color: #f0f0f0;
            display: block;
            margin: 0 auto;
            border: 2px solid #333;
        }
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        button {
            padding: 8px 16px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>贪吃蛇游戏</h1>
    <canvas id="game" width="400" height="400"></canvas>
    <div class="controls">
        <button onclick="startGame()">开始游戏</button>
        <button onclick="pauseGame()">暂停游戏</button>
    </div>
    <p>得分: <span id="score">0</span></p>
    <p>最高分: <span id="highScore">0</span></p>
    <script>
        const canvas = document.getElementById('game');
        const context = canvas.getContext('2d');
        const scoreDisplay = document.getElementById('score');
        const highScoreDisplay = document.getElementById('highScore');
        
        // 游戏配置
        const config = {
            gridSize: 20,      // 网格大小（像素）
            initialSpeed: 150, // 初始速度（毫秒）
            speedIncrease: 5   // 每次加速的增量
        };
        
        // 游戏状态
        let gameState = {
            snake: [],
            direction: { x: 1, y: 0 },
            food: {},
            score: 0,
            highScore: localStorage.getItem('snakeHighScore') || 0,
            running: false,
            paused: false,
            speed: config.initialSpeed
        };
        
        // 初始化游戏
        function init() {
            // 设置画布缩放
            context.scale(config.gridSize, config.gridSize);
            
            // 初始化最高分
            highScoreDisplay.textContent = gameState.highScore;
            
            // 绑定键盘事件
            document.addEventListener('keydown', handleKeyPress);
            
            // 初始化游戏
            resetGame();
        }
        
        // 重置游戏
        function resetGame() {
            // 初始化蛇（3个方块）
            gameState.snake = [
                { x: 10, y: 10 },
                { x: 9, y: 10 },
                { x: 8, y: 10 }
            ];
            
            // 初始方向向右
            gameState.direction = { x: 1, y: 0 };
            
            // 生成第一个食物
            placeFood();
            
            // 重置分数
            gameState.score = 0;
            updateScore();
            
            // 重置速度
            gameState.speed = config.initialSpeed;
            
            // 重置状态
            gameState.running = true;
            gameState.paused = false;
            
            // 开始游戏循环
            requestAnimationFrame(gameLoop);
        }
        
        // 游戏主循环
        function gameLoop() {
            if (!gameState.running || gameState.paused) {
                return;
            }
            
            // 移动蛇
            moveSnake();
            
            // 检查碰撞
            if (checkCollision()) {
                gameOver();
                return;
            }
            
            // 绘制游戏
            draw();
            
            // 继续循环
            setTimeout(() => {
                requestAnimationFrame(gameLoop);
            }, gameState.speed);
        }
        
        // 移动蛇
        function moveSnake() {
            // 获取蛇头位置
            const head = { 
                x: gameState.snake[0].x + gameState.direction.x,
                y: gameState.snake[0].y + gameState.direction.y
            };
            
            // 添加新蛇头
            gameState.snake.unshift(head);
            
            // 检查是否吃到食物
            if (head.x === gameState.food.x && head.y === gameState.food.y) {
                // 吃到食物，增加分数
                gameState.score++;
                updateScore();
                
                // 加快速度
                if (gameState.speed > 50) {
                    gameState.speed -= config.speedIncrease;
                }
                
                // 生成新食物
                placeFood();
            } else {
                // 没有食物，移除蛇尾
                gameState.snake.pop();
            }
        }
        
        // 放置食物
        function placeFood() {
            while (true) {
                // 在游戏区域内随机生成食物位置
                const x = Math.floor(Math.random() * (canvas.width / config.gridSize));
                const y = Math.floor(Math.random() * (canvas.height / config.gridSize));
                
                // 确保食物不在蛇身上
                if (!gameState.snake.some(segment => segment.x === x && segment.y === y)) {
                    gameState.food = { x, y };
                    break;
                }
            }
        }
        
        // 检查碰撞
        function checkCollision() {
            const head = gameState.snake[0];
            
            // 检查墙壁碰撞
            if (head.x < 0 || 
                head.x >= canvas.width / config.gridSize || 
                head.y < 0 || 
                head.y >= canvas.height / config.gridSize) {
                return true;
            }
            
            // 检查自撞
            for (let i = 1; i < gameState.snake.length; i++) {
                if (head.x === gameState.snake[i].x && head.y === gameState.snake[i].y) {
                    return true;
                }
            }
            
            return false;
        }
        
        // 游戏结束
        function gameOver() {
            gameState.running = false;
            
            // 更新最高分
            if (gameState.score > gameState.highScore) {
                gameState.highScore = gameState.score;
                highScoreDisplay.textContent = gameState.highScore;
                localStorage.setItem('snakeHighScore', gameState.highScore);
            }
            
            // 显示游戏结束消息
            alert(`游戏结束！得分: ${gameState.score}\n点击确定重新开始`);
            
            // 重置游戏
            resetGame();
        }
        
        // 绘制游戏
        function draw() {
            // 清除画布
            context.fillStyle = '#f0f0f0';
            context.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制蛇
            gameState.snake.forEach((segment, index) => {
                context.fillStyle = index === 0 ? '#4CAF50' : '#81C784';
                context.fillRect(segment.x, segment.y, 1, 1);
            });
            
            // 绘制食物
            context.fillStyle = '#FF5252';
            context.fillRect(gameState.food.x, gameState.food.y, 1, 1);
        }
        
        // 更新得分显示
        function updateScore() {
            scoreDisplay.textContent = gameState.score;
        }
        
        // 处理键盘输入
        function handleKeyPress(event) {
            switch (event.key) {
                case 'ArrowUp':
                case 'w':
                    if (gameState.direction.y === 0) {
                        gameState.direction = { x: 0, y: -1 };
                    }
                    break;
                case 'ArrowDown':
                case 's':
                    if (gameState.direction.y === 0) {
                        gameState.direction = { x: 0, y: 1 };
                    }
                    break;
                case 'ArrowLeft':
                case 'a':
                    if (gameState.direction.x === 0) {
                        gameState.direction = { x: -1, y: 0 };
                    }
                    break;
                case 'ArrowRight':
                case 'd':
                    if (gameState.direction.x === 0) {
                        gameState.direction = { x: 1, y: 0 };
                    }
                    break;
                case ' ': // 空格键暂停/继续
                    if (gameState.running) {
                        gameState.paused = !gameState.paused;
                        if (!gameState.paused) {
                            requestAnimationFrame(gameLoop);
                        }
                    }
                    break;
            }
        }
        
        // 开始游戏
        function startGame() {
            if (!gameState.running) {
                resetGame();
            } else if (gameState.paused) {
                gameState.paused = false;
                requestAnimationFrame(gameLoop);
            }
        }
        
        // 暂停游戏
        function pauseGame() {
            if (gameState.running && !gameState.paused) {
                gameState.paused = true;
            }
        }
        
        // 初始化游戏
        init();
    </script>
</body>
</html>