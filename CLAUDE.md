# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**inc-bdp-usb-mcp-web** is a desktop AI assistant application called "领慧助手" (<PERSON> Hui Assistant) built with Electron. It serves as an MCP (Model Context Protocol) client that integrates multiple AI models (OpenAI, Anthropic, DeepSeek) with tool capabilities.

## Architecture

### Core Technologies

- **Frontend**: React 18 + TypeScript + Ant Design 4.24.10
- **Desktop Framework**: Electron 35.1.5
- **Database**: SQLite (better-sqlite3)
- **Build System**: bdpfe-rs (Rspack-based)
- **AI Integration**: OpenAI SDK, Anthropic SDK, MCP SDK

### Project Structure

```
src/                     # React frontend application
├── pages/               # Main application pages
│   ├── chat-studio/     # Core chat interface
│   ├── mcp-servers/     # MCP server management UI
│   ├── model-service/   # AI model configuration
│   └── setting/         # Application settings
├── components/          # Reusable UI components
├── services/           # API service layer
└── context/            # React contexts for state management

clients/                # Backend implementation
├── node/              # Electron main process code
│   ├── chat/          # Chat functionality and AI integration
│   ├── db/            # SQLite database layer
│   ├── mcp-core/      # MCP protocol implementation
│   └── common-api/    # IPC communication layer
└── web/               # Renderer process APIs

external/              # External tools (mset, nodejs, uv, bun)
scripts/               # Build and deployment automation
```

### Key Components

- **Main Process**: `main.js` - Electron app lifecycle and window management
- **Preload Script**: `preload.js` - Secure IPC bridge between main and renderer
- **Chat Engine**: `clients/node/chat/ChatClient.ts` - Core AI chat functionality
- **Database**: SQLite with migration support for session persistence
- **MCP Integration**: Protocol implementation for external tool integration

## Development Commands

### Environment Setup

```bash
npm install                    # Install dependencies
npm run replace-sdk-files      # Replace SDK files with custom versions
```

### Development

```bash
npm run dev                    # Start development server with Electron
npm start                      # Alternative development server (renderer only)
```

### Building

```bash
npm run build                  # Build renderer process
npm run build:main            # Compile TypeScript for main process
npm run clean                  # Clean release directory
```

### Distribution

```bash
# macOS builds
npm run dist:mac-x64-production      # macOS Intel production build
npm run dist:mac-arm64-production    # macOS Apple Silicon production build
npm run dist:mac-x64-sit            # macOS Intel test build

# Windows builds
npm run dist:win-x64-production      # Windows x64 production build
npm run dist:win-x64-sit            # Windows x64 test build
```

### Environment Configuration

The application supports multiple environments through dynamic configuration:

- **Production**: `--env production` - Full production build with auto-updates
- **SIT**: `--env sit` - Test environment with different app ID and endpoints

## Code Organization Patterns

### IPC Communication

- Main process APIs exposed via `window['bdpStudioAPI']` in preload script
- Async/await pattern for all IPC calls from renderer to main process
- Type-safe IPC with TypeScript interfaces

### Database Layer

- SQLite database with migration system in `clients/node/db/`
- Session persistence for chat conversations
- User configuration and settings storage

### AI Model Integration

- Abstracted model provider interface supporting multiple APIs
- Function calling capabilities through MCP protocol
- Streaming response handling for real-time chat

### State Management

- React Context for global application state
- Local component state for UI-specific data
- Persistent storage through main process database

## Development Considerations

### Build Process

- Uses custom `bdpfe-rs` build system based on Rspack
- Dynamic configuration generation for different environments and architectures
- External dependencies bundled in `external/` directory for MCP functionality

### Security

- Context isolation enabled in Electron
- Secure IPC communication through preload script
- No direct access to Node.js APIs from renderer process

### Multi-platform Support

- Cross-platform builds for Windows and macOS (Intel/ARM)
- Platform-specific installers (NSIS for Windows, DMG for macOS)
- Environment-specific configurations and endpoints

### External Tool Integration

- MCP servers run as separate processes
- Support for Python, Node.js, and other runtime environments
- Tool discovery and management through MCP protocol

### Internationalization

- Multi-language support (Chinese, English, Thai)
- i18next integration for text localization
- Scanner script available: `npm run i18next-scanner`

## Important Development Notes

### TypeScript Configuration

The project uses two separate TypeScript configurations:
- `tsconfig.json` - Frontend React application (relaxed type checking)
- `tsconfig.main.json` - Electron main process code (strict type checking)

### Development Workflow

1. Generate environment configuration: `node scripts/generate-env-config.js --env sit --force`
2. Generate builder configuration: `node scripts/generate-builder-config.js --env sit`
3. Replace SDK files: `npm run replace-sdk-files`
4. Build main process: `npm run build:main`
5. Start development: `npm run dev`

## Testing and Quality

### Build Artifacts

- Distribution packages in `release/` directory
- Debug symbols and effective configuration in build output
- Auto-update support through electron-updater

### Path Aliases

- `@/*` maps to `src/*` directory for cleaner imports in React components
