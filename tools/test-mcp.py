#!/usr/bin/env python3
"""
Simple test script for the filesystem MCP server
"""

import json
import subprocess
import sys
import tempfile
import os

def send_mcp_request(process, request):
    """Send a JSON-RPC request to the MCP server and get response"""
    request_json = json.dumps(request) + '\n'
    process.stdin.write(request_json.encode())
    process.stdin.flush()
    
    response_line = process.stdout.readline().decode().strip()
    if response_line:
        return json.loads(response_line)
    return None

def test_filesystem_mcp():
    """Test the filesystem MCP server"""
    # Start the MCP server
    mcp_binary = './target/release/filesystem-mcp'
    
    if not os.path.exists(mcp_binary):
        print(f"Error: MCP binary not found at {mcp_binary}")
        print("Please run 'cargo build --release' first")
        return False
    
    try:
        # Suppress debug output by setting log level to error
        process = subprocess.Popen(
            [mcp_binary, '--log-level', 'error'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd='/Users/<USER>/Documents/source-code/inc-bdp-usb-mcp-web/tools'
        )
        
        print("🚀 Starting filesystem MCP server test...")
        
        # Test 1: Initialize
        print("\n📋 Test 1: Initialize server")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {}
        }
        response = send_mcp_request(process, init_request)
        print(f"Response: {response}")
        
        if response and response.get('result'):
            print("✅ Initialize successful")
        else:
            print("❌ Initialize failed")
            return False
        
        # Test 2: List tools
        print("\n📋 Test 2: List available tools")
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        response = send_mcp_request(process, list_tools_request)
        print(f"Tools response: {json.dumps(response, indent=2)}")
        
        if response and 'result' in response and 'tools' in response['result']:
            tools = response['result']['tools']
            print(f"✅ Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
        else:
            print("❌ List tools failed")
            return False
        
        # Test 3: Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n📋 Test 3: Testing filesystem operations in {temp_dir}")
            
            # Test 3a: List directory
            print("\n📋 Test 3a: List directory")
            list_dir_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "list_directory",
                    "arguments": {
                        "path": temp_dir,
                        "recursive": False
                    }
                }
            }
            response = send_mcp_request(process, list_dir_request)
            if response and 'result' in response:
                print("✅ List directory successful")
                print(f"Directory listing: {response['result']['content'][0]['text'][:200]}...")
            else:
                print("❌ List directory failed")
                print(f"Response: {response}")
            
            # Test 3b: Write file
            print("\n📋 Test 3b: Write file")
            test_file = os.path.join(temp_dir, "test.txt")
            test_content = "Hello, MCP World!\nThis is a test file."
            
            write_file_request = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": "write_file",
                    "arguments": {
                        "path": test_file,
                        "content": test_content
                    }
                }
            }
            response = send_mcp_request(process, write_file_request)
            if response and 'result' in response:
                print("✅ Write file successful")
            else:
                print("❌ Write file failed")
                print(f"Response: {response}")
            
            # Test 3c: Read file
            print("\n📋 Test 3c: Read file")
            read_file_request = {
                "jsonrpc": "2.0",
                "id": 5,
                "method": "tools/call",
                "params": {
                    "name": "read_file",
                    "arguments": {
                        "path": test_file
                    }
                }
            }
            response = send_mcp_request(process, read_file_request)
            if response and 'result' in response:
                print("✅ Read file successful")
                content_response = json.loads(response['result']['content'][0]['text'])
                if content_response.get('content') == test_content:
                    print("✅ File content matches expected")
                else:
                    print("❌ File content mismatch")
                    print(f"Expected: {test_content}")
                    print(f"Got: {content_response.get('content')}")
            else:
                print("❌ Read file failed")
                print(f"Response: {response}")
            
            # Test 3d: Get file info
            print("\n📋 Test 3d: Get file info")
            file_info_request = {
                "jsonrpc": "2.0",
                "id": 6,
                "method": "tools/call",
                "params": {
                    "name": "get_file_info",
                    "arguments": {
                        "path": test_file
                    }
                }
            }
            response = send_mcp_request(process, file_info_request)
            if response and 'result' in response:
                print("✅ Get file info successful")
                info_text = response['result']['content'][0]['text']
                print(f"File info: {info_text[:200]}...")
            else:
                print("❌ Get file info failed")
                print(f"Response: {response}")
        
        print("\n🎉 All tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    finally:
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()

if __name__ == "__main__":
    success = test_filesystem_mcp()
    sys.exit(0 if success else 1)