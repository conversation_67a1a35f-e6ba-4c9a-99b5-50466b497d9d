# Filesystem MCP Server

A Model Context Protocol (MCP) server that provides filesystem operations for AI assistants.

## Features

- **Directory Operations**: List directory contents (with recursive option)
- **File Operations**: Read, write, delete files
- **Directory Management**: Create and delete directories
- **File Information**: Get detailed file/directory metadata
- **Search**: Search for files by name pattern
- **Security**: Path validation and access control

## Available Tools

### `list_directory`
Lists the contents of a directory.

**Parameters:**
- `path` (string): Directory path to list
- `recursive` (boolean, optional): Whether to list recursively (default: false)

### `read_file`
Reads the content of a text file.

**Parameters:**
- `path` (string): File path to read

### `write_file`
Writes content to a file.

**Parameters:**
- `path` (string): File path to write
- `content` (string): Content to write to the file

### `create_directory`
Creates a directory.

**Parameters:**
- `path` (string): Directory path to create
- `recursive` (boolean, optional): Whether to create parent directories (default: false)

### `delete`
Deletes a file or directory.

**Parameters:**
- `path` (string): Path to delete
- `recursive` (boolean, optional): Whether to delete recursively for directories (default: false)

### `get_file_info`
Gets detailed information about a file or directory.

**Parameters:**
- `path` (string): Path to get information about

### `search_files`
Searches for files matching a pattern.

**Parameters:**
- `base_path` (string): Base directory to search in
- `pattern` (string): Pattern to search for (substring match)
- `max_results` (integer, optional): Maximum number of results (default: 100)

### `list_allowed_directories`
Lists the currently configured allowed directories with their status and information.

**Parameters:**
- None

**Returns:**
- `allowed_directories`: Array of directory information objects
- `total_count`: Total number of allowed directories
- `summary`: Summary statistics

## Usage

### Building

```bash
# From the tools directory
cargo build --release

# Or build just this package
cd filesystem-mcp
cargo build --release
```

### Running

**IMPORTANT**: The MCP server requires the `--allowed-directories` parameter to specify which directories can be accessed. This is a security requirement.

```bash
# Single directory
./target/release/filesystem-mcp --allowed-directories /path/to/allowed/directory

# Multiple directories (comma-separated)
./target/release/filesystem-mcp --allowed-directories /path/to/dir1,/path/to/dir2

# With custom log level
./target/release/filesystem-mcp --allowed-directories /tmp --log-level error
```

**Startup Validation:**
- At least one allowed directory must be specified
- All specified directories must exist and be accessible
- The server will refuse to start if these conditions are not met

### Example MCP Communication

Initialize the server:
```json
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {}}
```

List available tools:
```json
{"jsonrpc": "2.0", "id": 2, "method": "tools/list"}
```

Call a tool:
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "list_directory",
    "arguments": {
      "path": "/tmp",
      "recursive": false
    }
  }
}
```

Check allowed directories:
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "list_allowed_directories",
    "arguments": {}
  }
}
```

## Security Features

- **Mandatory Directory Allowlist**: All filesystem operations are restricted to explicitly allowed directories
- **Startup Validation**: Server refuses to start without valid allowed directories
- **Path Canonicalization**: Resolves symlinks and relative paths to prevent bypass attempts
- **Write Operation Restrictions**: Additional security checks for write/delete operations
- **System Directory Protection**: Prevents access to critical system directories even if accidentally allowed
- **Directory Traversal Prevention**: Blocks dangerous path patterns like `../` and absolute system paths

## Development

### Running Tests

```bash
cargo test
```

### Debug Mode

```bash
cargo run -- --debug --log-level debug
```

## Integration with MCP Client

This filesystem MCP server can be integrated into any MCP client application. Here's how to configure it:

### For inc-bdp-usb-mcp-web Application

#### 1. Database Configuration
Add the following MCP server configuration to your application's database:

```sql
INSERT INTO mcp_servers (serverName, serverConfig, serverDesc, serverStatus, serverFromObj, serverLogo, serverTools) 
VALUES (
  'filesystem-mcp',
  '{"serverType": "stdio", "cmd": "./external/rust-tools/filesystem-mcp", "args": ["--log-level", "error", "--allowed-directories", "/Users/<USER>/Documents,/Users/<USER>/Projects"], "env": {}}',
  'Built-in filesystem operations for file and directory management',
  1,
  'built-in',
  '',
  'list_directory,read_file,write_file,create_directory,delete,get_file_info,search_files,list_allowed_directories'
);
```

#### 2. MCP Client Configuration
In your MCP client code, configure the server as follows:

```typescript
// Example configuration for MCPClient
const filesystemMcpConfig = {
  name: "filesystem-client",
  version: "1.0.0",
  cmd: "./external/rust-tools/filesystem-mcp",
  args: [
    "--log-level", "error",
    "--allowed-directories", "/Users/<USER>/Documents,/Users/<USER>/Projects"
  ],
  env: {},
  serverUrl: "", // Not used for stdio
  requestHeaders: {},
  serverType: "stdio" as const,
  mcpName: "filesystem-mcp"
};

const mcpClient = new MCPClient(filesystemMcpConfig);
await mcpClient.init();
```

#### 3. Tool Usage Examples

Once configured, you can use the filesystem tools in your chat interface:

**List directory contents:**
```javascript
const tools = await mcpClient.getTools();
const listTool = tools.find(t => t.name === 'list_directory');

// Use in AI conversation
const response = await callTool('list_directory', {
  path: '/Users/<USER>/Documents',
  recursive: false
});
```

**Read a file:**
```javascript
const response = await callTool('read_file', {
  path: '/path/to/file.txt'
});
```

**Write a file:**
```javascript
const response = await callTool('write_file', {
  path: '/path/to/output.txt',
  content: 'Hello, World!'
});
```

**Search for files:**
```javascript
const response = await callTool('search_files', {
  base_path: '/Users/<USER>/projects',
  pattern: '.ts',
  max_results: 50
});
```

### For Other MCP Clients

#### Claude Desktop Configuration
Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "filesystem-mcp": {
      "command": "/path/to/filesystem-mcp",
      "args": ["--log-level", "error"]
    }
  }
}
```

#### Continue.dev Configuration
Add to your `config.json`:

```json
{
  "contextProviders": [
    {
      "name": "filesystem-mcp",
      "params": {
        "command": "/path/to/filesystem-mcp",
        "args": ["--log-level", "error"]
      }
    }
  ]
}
```

### Security Considerations

When integrating this MCP server:

1. **Path Restrictions**: Consider implementing path restrictions based on your application's security requirements
2. **User Permissions**: Ensure the server runs with appropriate file system permissions
3. **Sandbox Environment**: Consider running in a sandboxed environment for enhanced security
4. **Access Control**: Implement proper access control in your client application

### Deployment

For production deployment:

1. Build the release binary: `cargo build --release`
2. Copy the binary to your application's `external/rust-tools/` directory
3. Update your MCP server configuration to point to the correct binary path
4. Restart your application to load the new MCP server

This MCP server is designed to be integrated into the inc-bdp-usb-mcp-web Electron application as a built-in tool for filesystem operations.