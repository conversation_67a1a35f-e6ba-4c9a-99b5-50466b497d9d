use std::path::{Path, PathBuf};

/// Utility functions for filesystem operations

/// Normalize a path by resolving . and .. components
pub fn normalize_path<P: AsRef<Path>>(path: P) -> PathBuf {
    let path = path.as_ref();
    let mut components = Vec::new();

    for component in path.components() {
        match component {
            std::path::Component::CurDir => {
                // Skip "." components
            }
            std::path::Component::ParentDir => {
                // Pop the last component for ".."
                components.pop();
            }
            _ => {
                components.push(component);
            }
        }
    }

    components.iter().collect()
}

/// Check if a path is safe (doesn't contain dangerous patterns)
pub fn is_safe_path<P: AsRef<Path>>(path: P) -> bool {
    let path = path.as_ref();
    let path_str = path.to_string_lossy();

    // Check for dangerous patterns
    if path_str.contains("..") {
        return false;
    }

    // Check for absolute paths that might be dangerous
    if path.is_absolute() {
        let sensitive_dirs = [
            "/etc",
            "/bin",
            "/sbin",
            "/usr/bin",
            "/usr/sbin",
            "/System",
            "/private",
        ];

        for sensitive in &sensitive_dirs {
            if path_str.starts_with(sensitive) {
                return false;
            }
        }
    }

    true
}

/// Get file extension
pub fn get_file_extension<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref()
        .extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| ext.to_lowercase())
}

/// Check if file is likely to be text-based
pub fn is_text_file<P: AsRef<Path>>(path: P) -> bool {
    let text_extensions = [
        "txt",
        "md",
        "json",
        "js",
        "ts",
        "tsx",
        "jsx",
        "html",
        "htm",
        "css",
        "scss",
        "sass",
        "less",
        "xml",
        "yaml",
        "yml",
        "toml",
        "ini",
        "cfg",
        "conf",
        "log",
        "sql",
        "py",
        "rs",
        "go",
        "java",
        "c",
        "cpp",
        "h",
        "hpp",
        "cs",
        "php",
        "rb",
        "sh",
        "bash",
        "zsh",
        "fish",
        "dockerfile",
        "gitignore",
        "gitattributes",
        "LICENSE",
        "README",
    ];

    if let Some(ext) = get_file_extension(path) {
        text_extensions.contains(&ext.as_str())
    } else {
        false
    }
}

/// Format file size in human-readable format
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_normalize_path() {
        assert_eq!(
            normalize_path("./test/../file.txt"),
            PathBuf::from("file.txt")
        );
        assert_eq!(
            normalize_path("./dir/./file.txt"),
            PathBuf::from("dir/file.txt")
        );
    }

    #[test]
    fn test_is_safe_path() {
        assert!(is_safe_path("./safe/path"));
        assert!(!is_safe_path("../unsafe/path"));
        assert!(!is_safe_path("/etc/passwd"));
    }

    #[test]
    fn test_get_file_extension() {
        assert_eq!(get_file_extension("test.txt"), Some("txt".to_string()));
        assert_eq!(get_file_extension("test.JSON"), Some("json".to_string()));
        assert_eq!(get_file_extension("no_extension"), None);
    }

    #[test]
    fn test_is_text_file() {
        assert!(is_text_file("test.txt"));
        assert!(is_text_file("config.json"));
        assert!(!is_text_file("image.png"));
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }
}
