use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use glob::Pattern;
use regex::{Regex, RegexBuilder};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;

// 默认忽略的目录和文件模式
const DEFAULT_IGNORE_PATTERNS: &[&str] = &[
    // 构建目录
    "node_modules/*",
    "target/*",
    "dist/*",
    "build/*",
    "release/*",
    "out/*",
    "bin/*",
    "obj/*",
    // 依赖管理
    "vendor/*",
    "deps/*",
    "packages/*",
    // 缓存目录
    ".cache/*",
    ".tmp/*",
    "tmp/*",
    "temp/*",
    // 日志文件
    "*.log",
    "logs/*",
    // 操作系统文件
    ".DS_Store",
    "Thumbs.db",
    "desktop.ini",
    // 编辑器文件
    ".vscode/*",
    ".idea/*",
    "*.swp",
    "*.swo",
    "*~",
    // 版本控制
    ".git/*",
    ".svn/*",
    ".hg/*",
    // 编译产物
    "*.o",
    "*.obj",
    "*.exe",
    "*.dll",
    "*.so",
    "*.dylib",
    "*.class",
    "*.pyc",
    "__pycache__/*",
    // 压缩文件
    "*.zip",
    "*.tar.gz",
    "*.7z",
    "*.rar",
];

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub path: String,
    pub name: String,
    pub size: u64,
    pub is_directory: bool,
    pub is_file: bool,
    pub created: Option<DateTime<Utc>>,
    pub modified: Option<DateTime<Utc>>,
    pub permissions: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectoryListing {
    pub path: String,
    pub entries: Vec<FileInfo>,
    pub total_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileContent {
    pub path: String,
    pub content: String,
    pub size: u64,
    pub encoding: String,
    pub total_lines: usize,                 // 文件总行数
    pub displayed_lines: usize,             // 显示的行数
    pub is_truncated: bool,                 // 是否被截断
    pub line_range: Option<(usize, usize)>, // 显示的行范围 (start, end)
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReadFileConfig {
    pub max_lines: Option<usize>,  // 最大行数限制，None 表示读取全部
    pub start_line: Option<usize>, // 开始行号 (1-based)
    pub end_line: Option<usize>,   // 结束行号 (1-based)
}

impl Default for ReadFileConfig {
    fn default() -> Self {
        Self {
            max_lines: Some(200), // 默认读取前 200 行
            start_line: None,
            end_line: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub path: String,
    pub exists: bool,
    pub is_accessible: bool,
    pub canonical_path: Option<String>,
    pub size_info: Option<DirectorySizeInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectorySizeInfo {
    pub total_files: usize,
    pub total_directories: usize,
    pub total_size: u64,
}

// 搜索相关的数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchConfig {
    pub query: String,
    pub directory: String,
    pub recursive: bool,
    pub case_sensitive: bool,
    pub use_regex: bool,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub max_file_size: Option<u64>,
    pub context_lines: Option<usize>,
    pub max_results: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub query: String,
    pub total_matches: usize,
    pub files_searched: usize,
    pub files_with_matches: usize,
    pub matches: Vec<FileMatch>,
    pub search_time_ms: u128,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMatch {
    pub file_path: String,
    pub line_matches: Vec<LineMatch>,
    pub total_matches: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineMatch {
    pub line_number: usize,
    pub content: String,
    pub start_index: usize,
    pub end_index: usize,
    pub context_before: Option<Vec<String>>,
    pub context_after: Option<Vec<String>>,
}

// .gitignore 解析器
#[derive(Debug, Clone)]
pub struct GitIgnoreParser {
    pub patterns: Vec<GitIgnorePattern>,
}

#[derive(Debug, Clone)]
pub struct GitIgnorePattern {
    pub pattern: String,
    pub is_negation: bool,
    pub is_directory_only: bool,
    pub is_absolute: bool,
    pub regex: Option<Regex>,
}

impl GitIgnoreParser {
    pub fn new() -> Self {
        Self {
            patterns: Vec::new(),
        }
    }

    /// 从.gitignore文件加载规则
    pub fn load_from_file<P: AsRef<Path>>(gitignore_path: P) -> Result<Self> {
        let mut parser = Self::new();

        if let Ok(content) = fs::read_to_string(gitignore_path) {
            parser.parse_content(&content)?;
        }

        Ok(parser)
    }

    /// 解析.gitignore文件内容
    pub fn parse_content(&mut self, content: &str) -> Result<()> {
        for line in content.lines() {
            let line = line.trim();

            // 跳过空行和注释
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            self.add_pattern(line)?;
        }

        Ok(())
    }

    /// 添加单个模式
    pub fn add_pattern(&mut self, pattern_str: &str) -> Result<()> {
        let mut pattern = pattern_str.to_string();
        let mut is_negation = false;
        let mut is_directory_only = false;
        let mut is_absolute = false;

        // 处理否定模式
        if pattern.starts_with('!') {
            is_negation = true;
            pattern = pattern[1..].to_string();
        }

        // 处理目录专用模式
        if pattern.ends_with('/') {
            is_directory_only = true;
            pattern = pattern[..pattern.len() - 1].to_string();
        }

        // 处理绝对路径模式
        if pattern.starts_with('/') {
            is_absolute = true;
            pattern = pattern[1..].to_string();
        }

        // 编译为正则表达式
        let regex = self.compile_gitignore_pattern(&pattern)?;

        self.patterns.push(GitIgnorePattern {
            pattern: pattern_str.to_string(),
            is_negation,
            is_directory_only,
            is_absolute,
            regex: Some(regex),
        });

        Ok(())
    }

    /// 将gitignore模式编译为正则表达式
    fn compile_gitignore_pattern(&self, pattern: &str) -> Result<Regex> {
        let mut regex_pattern = String::new();
        let chars: Vec<char> = pattern.chars().collect();
        let mut i = 0;

        while i < chars.len() {
            match chars[i] {
                '*' => {
                    if i + 1 < chars.len() && chars[i + 1] == '*' {
                        // 处理 ** 模式
                        if i + 2 < chars.len() && chars[i + 2] == '/' {
                            regex_pattern.push_str("(?:.*/)?");
                            i += 3;
                        } else {
                            regex_pattern.push_str(".*");
                            i += 2;
                        }
                    } else {
                        regex_pattern.push_str("[^/]*");
                        i += 1;
                    }
                }
                '?' => {
                    regex_pattern.push_str("[^/]");
                    i += 1;
                }
                '[' => {
                    // 处理字符类
                    regex_pattern.push('[');
                    i += 1;
                    while i < chars.len() && chars[i] != ']' {
                        if chars[i] == '\\' {
                            regex_pattern.push('\\');
                            i += 1;
                        }
                        if i < chars.len() {
                            regex_pattern.push(chars[i]);
                            i += 1;
                        }
                    }
                    if i < chars.len() {
                        regex_pattern.push(']');
                        i += 1;
                    }
                }
                '.' | '+' | '^' | '$' | '(' | ')' | '{' | '}' | '|' | '\\' => {
                    regex_pattern.push('\\');
                    regex_pattern.push(chars[i]);
                    i += 1;
                }
                _ => {
                    regex_pattern.push(chars[i]);
                    i += 1;
                }
            }
        }

        let full_pattern = if pattern.contains('/') {
            format!("^{}$", regex_pattern)
        } else {
            format!("(^|/){}(/|$)", regex_pattern)
        };

        Regex::new(&full_pattern)
            .with_context(|| format!("Failed to compile gitignore pattern: {}", pattern))
    }

    /// 检查文件是否应该被忽略
    pub fn is_ignored(&self, file_path: &Path, base_path: &Path) -> bool {
        let relative_path = if let Ok(path) = file_path.strip_prefix(base_path) {
            path.to_string_lossy().replace('\\', "/")
        } else {
            file_path.to_string_lossy().replace('\\', "/")
        };

        let is_dir = file_path.is_dir();
        let mut is_ignored = false;

        for pattern in &self.patterns {
            if let Some(regex) = &pattern.regex {
                let matches = if pattern.is_directory_only {
                    // 对于目录模式，检查文件是否在该目录内
                    if is_dir {
                        // 如果是目录，检查目录本身是否匹配
                        regex.is_match(&relative_path)
                    } else {
                        // 如果是文件，检查文件是否在匹配的目录内
                        regex.is_match(&relative_path) || {
                            // 检查文件的父目录是否匹配模式
                            let parent_components: Vec<&str> = relative_path.split('/').collect();
                            parent_components.len() > 1 && {
                                for i in 1..parent_components.len() {
                                    let parent_path = parent_components[..i].join("/");
                                    if regex.is_match(&parent_path) {
                                        return true;
                                    }
                                }
                                false
                            }
                        }
                    }
                } else if pattern.is_absolute {
                    regex.is_match(&relative_path)
                } else {
                    regex.is_match(&relative_path)
                        || regex
                            .is_match(&file_path.file_name().unwrap_or_default().to_string_lossy())
                };

                if matches {
                    is_ignored = !pattern.is_negation;
                }
            }
        }

        is_ignored
    }
}

pub struct FileSystemOperations {
    allowed_paths: Vec<PathBuf>,
    gitignore_parser: GitIgnoreParser,
}

impl FileSystemOperations {
    pub fn new(allowed_paths: Vec<PathBuf>) -> Self {
        let mut gitignore_parser = GitIgnoreParser::new();

        // 尝试从第一个允许的路径中加载.gitignore文件
        if let Some(first_path) = allowed_paths.first() {
            let gitignore_path = first_path.join(".gitignore");
            if let Ok(parser) = GitIgnoreParser::load_from_file(&gitignore_path) {
                gitignore_parser = parser;
            }
        }

        Self {
            allowed_paths,
            gitignore_parser,
        }
    }

    /// Check if a path is allowed to be accessed
    fn is_path_allowed(&self, path: &Path) -> Result<()> {
        // First, validate that we have allowed paths configured
        if self.allowed_paths.is_empty() {
            return Err(anyhow::anyhow!(
                "No allowed directories configured. Access denied to: {}",
                path.display()
            ));
        }

        // Try to canonicalize the path for exact comparison
        // Use a more efficient approach that doesn't block on non-existent files
        let canonical_path = if path.exists() {
            path.canonicalize()
                .with_context(|| format!("Failed to canonicalize path: {}", path.display()))?
        } else {
            // For non-existent files, check the parent directory
            let parent = path.parent().unwrap_or(path);
            if parent.exists() {
                let canonical_parent = parent.canonicalize().with_context(|| {
                    format!("Failed to canonicalize parent path: {}", parent.display())
                })?;
                canonical_parent.join(path.file_name().unwrap_or_default())
            } else {
                // If parent doesn't exist either, use the path as-is but resolve it
                path.to_path_buf()
            }
        };

        // Check if path is within any allowed directory
        let mut allowed = false;
        for allowed_path in &self.allowed_paths {
            if canonical_path.starts_with(allowed_path) {
                allowed = true;
                break;
            }
        }

        if !allowed {
            return Err(anyhow::anyhow!(
                "Path '{}' is not within any allowed directory. Allowed directories: {:?}",
                canonical_path.display(),
                self.allowed_paths
                    .iter()
                    .map(|p| p.display().to_string())
                    .collect::<Vec<_>>()
            ));
        }

        Ok(())
    }

    /// Check if a path is allowed for write operations (more restrictive)
    fn is_write_allowed(&self, path: &Path) -> Result<()> {
        // First check basic path permission
        if let Some(parent) = path.parent() {
            self.is_path_allowed(parent)?;
        } else {
            return Err(anyhow::anyhow!(
                "Cannot determine parent directory for: {}",
                path.display()
            ));
        }

        // Additional security checks for write operations
        let path_str = path.to_string_lossy();

        // Prevent writing to hidden system files
        if path_str.contains("/.") && !path_str.contains("/.local") {
            return Err(anyhow::anyhow!(
                "Writing to hidden files is not allowed: {}",
                path.display()
            ));
        }

        // Prevent writing to common system directories even if they're in allowed paths
        let dangerous_patterns = [
            "/etc/",
            "/bin/",
            "/sbin/",
            "/usr/bin/",
            "/usr/sbin/",
            "/System/",
            "/Library/System/",
            "/var/root/",
        ];

        for pattern in &dangerous_patterns {
            if path_str.contains(pattern) {
                return Err(anyhow::anyhow!(
                    "Writing to system directory is not allowed: {}",
                    path.display()
                ));
            }
        }

        Ok(())
    }

    /// List directory contents
    pub fn list_directory(&self, path: &str, recursive: bool) -> Result<DirectoryListing> {
        let path = Path::new(path);
        self.is_path_allowed(path)?;

        if !path.exists() {
            return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
        }

        if !path.is_dir() {
            return Err(anyhow::anyhow!(
                "Path is not a directory: {}",
                path.display()
            ));
        }

        let mut entries = Vec::new();

        if recursive {
            for entry in WalkDir::new(path) {
                let entry = entry.with_context(|| "Failed to read directory entry")?;
                let file_info = self.path_to_file_info(entry.path())?;
                entries.push(file_info);
            }
        } else {
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                let file_info = self.path_to_file_info(&entry.path())?;
                entries.push(file_info);
            }
        }

        // Sort entries by name
        entries.sort_by(|a, b| a.name.cmp(&b.name));

        Ok(DirectoryListing {
            path: path.display().to_string(),
            total_count: entries.len(),
            entries,
        })
    }

    /// Read file content with optional line limiting
    pub fn read_file(&self, path: &str, config: Option<ReadFileConfig>) -> Result<FileContent> {
        let path = Path::new(path);
        self.is_path_allowed(path)?;

        if !path.exists() {
            return Err(anyhow::anyhow!("File does not exist: {}", path.display()));
        }

        if !path.is_file() {
            return Err(anyhow::anyhow!("Path is not a file: {}", path.display()));
        }

        let config = config.unwrap_or_default();
        let metadata = fs::metadata(path)?;

        // 读取完整文件内容
        let full_content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read file: {}", path.display()))?;

        let all_lines: Vec<&str> = full_content.lines().collect();
        let total_lines = all_lines.len();

        // 确定要显示的行范围
        let (start_line, end_line, displayed_content) =
            self.determine_line_range(&all_lines, &config)?;

        let displayed_lines = displayed_content.lines().count();
        let is_truncated = displayed_lines < total_lines;
        let line_range = if start_line > 0 || end_line < total_lines {
            Some((start_line + 1, end_line)) // 转换为 1-based
        } else {
            None
        };

        Ok(FileContent {
            path: path.display().to_string(),
            content: displayed_content,
            size: metadata.len(),
            encoding: "utf-8".to_string(),
            total_lines,
            displayed_lines,
            is_truncated,
            line_range,
        })
    }

    /// 确定要显示的行范围
    fn determine_line_range(
        &self,
        all_lines: &[&str],
        config: &ReadFileConfig,
    ) -> Result<(usize, usize, String)> {
        let total_lines = all_lines.len();

        if total_lines == 0 {
            return Ok((0, 0, String::new()));
        }

        let (start_idx, end_idx) =
            if let (Some(start), Some(end)) = (config.start_line, config.end_line) {
                // 指定了开始和结束行
                if start == 0 || end == 0 {
                    return Err(anyhow::anyhow!(
                        "Line numbers must be 1-based (start from 1)"
                    ));
                }
                if start > end {
                    return Err(anyhow::anyhow!(
                        "Start line must be less than or equal to end line"
                    ));
                }
                if start > total_lines {
                    return Err(anyhow::anyhow!(
                        "Start line {} exceeds total lines {}",
                        start,
                        total_lines
                    ));
                }

                let start_idx = start - 1; // 转换为 0-based
                let end_idx = std::cmp::min(end - 1, total_lines - 1); // 转换为 0-based 并确保不超出范围
                (start_idx, end_idx + 1) // end_idx + 1 because range is exclusive
            } else if let Some(start) = config.start_line {
                // 只指定了开始行，根据 max_lines 确定结束行
                if start == 0 {
                    return Err(anyhow::anyhow!(
                        "Line numbers must be 1-based (start from 1)"
                    ));
                }
                if start > total_lines {
                    return Err(anyhow::anyhow!(
                        "Start line {} exceeds total lines {}",
                        start,
                        total_lines
                    ));
                }

                let start_idx = start - 1; // 转换为 0-based
                let end_idx = if let Some(max_lines) = config.max_lines {
                    std::cmp::min(start_idx + max_lines, total_lines)
                } else {
                    total_lines
                };
                (start_idx, end_idx)
            } else {
                // 从文件开头开始
                let end_idx = if let Some(max_lines) = config.max_lines {
                    std::cmp::min(max_lines, total_lines)
                } else {
                    total_lines
                };
                (0, end_idx)
            };

        let selected_lines = &all_lines[start_idx..end_idx];
        let content = selected_lines.join("\n");

        Ok((start_idx, end_idx, content))
    }

    /// Write file content
    pub fn write_file(&self, path: &str, content: &str) -> Result<()> {
        let path = Path::new(path);

        // Use the more restrictive write permission check
        self.is_write_allowed(path)?;

        fs::write(path, content)
            .with_context(|| format!("Failed to write file: {}", path.display()))?;

        Ok(())
    }

    /// Create directory
    pub fn create_directory(&self, path: &str, recursive: bool) -> Result<()> {
        let path = Path::new(path);

        // Use write permission check for directory creation
        self.is_write_allowed(path)?;

        if recursive {
            fs::create_dir_all(path)
        } else {
            fs::create_dir(path)
        }
        .with_context(|| format!("Failed to create directory: {}", path.display()))?;

        Ok(())
    }

    /// Delete file or directory
    pub fn delete(&self, path: &str, recursive: bool) -> Result<()> {
        let path = Path::new(path);

        // Use write permission check for deletion (very restrictive)
        self.is_write_allowed(path)?;

        if !path.exists() {
            return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
        }

        // Additional safety check for deletion
        let path_str = path.to_string_lossy();
        if path_str == "/" || path_str.ends_with("/..") || path_str.contains("/../") {
            return Err(anyhow::anyhow!(
                "Dangerous deletion path detected: {}",
                path.display()
            ));
        }

        if path.is_file() {
            fs::remove_file(path)
                .with_context(|| format!("Failed to delete file: {}", path.display()))?;
        } else if path.is_dir() {
            if recursive {
                fs::remove_dir_all(path)
            } else {
                fs::remove_dir(path)
            }
            .with_context(|| format!("Failed to delete directory: {}", path.display()))?;
        }

        Ok(())
    }

    /// Get file information
    pub fn get_file_info(&self, path: &str) -> Result<FileInfo> {
        let path = Path::new(path);
        self.is_path_allowed(path)?;

        if !path.exists() {
            return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
        }

        self.path_to_file_info(path)
    }

    /// Convert path to FileInfo
    fn path_to_file_info(&self, path: &Path) -> Result<FileInfo> {
        let metadata = fs::metadata(path)?;

        let created = metadata.created().ok().and_then(|time| {
            DateTime::from_timestamp(
                time.duration_since(std::time::UNIX_EPOCH).ok()?.as_secs() as i64,
                0,
            )
        });

        let modified = metadata.modified().ok().and_then(|time| {
            DateTime::from_timestamp(
                time.duration_since(std::time::UNIX_EPOCH).ok()?.as_secs() as i64,
                0,
            )
        });

        Ok(FileInfo {
            path: path.display().to_string(),
            name: path
                .file_name()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
            size: metadata.len(),
            is_directory: metadata.is_dir(),
            is_file: metadata.is_file(),
            created,
            modified,
            permissions: None, // TODO: implement platform-specific permissions
        })
    }

    /// Search for files matching a pattern
    pub fn search_files(
        &self,
        base_path: &str,
        pattern: &str,
        max_results: Option<usize>,
    ) -> Result<Vec<FileInfo>> {
        let path = Path::new(base_path);
        self.is_path_allowed(path)?;

        if !path.exists() {
            return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
        }

        let mut results = Vec::new();
        let max = max_results.unwrap_or(1000);

        // Compile glob pattern
        let glob_pattern =
            Pattern::new(pattern).with_context(|| format!("Invalid glob pattern: {}", pattern))?;

        for entry in WalkDir::new(path) {
            if results.len() >= max {
                break;
            }

            let entry = entry.with_context(|| "Failed to read directory entry")?;
            let file_path = entry.path();
            let file_name = entry.file_name().to_string_lossy();

            // Use glob pattern matching
            if glob_pattern.matches(&file_name) {
                // 检查是否应该忽略该文件
                if self.should_ignore_file(file_path)? {
                    continue;
                }

                let file_info = self.path_to_file_info(file_path)?;
                results.push(file_info);
            }
        }

        Ok(results)
    }

    /// 检查文件是否应该被忽略（用于search_files）
    fn should_ignore_file(&self, file_path: &Path) -> Result<bool> {
        // 检查文件权限
        if self.is_path_allowed(file_path).is_err() {
            return Ok(true);
        }

        let file_name = file_path.file_name().and_then(|n| n.to_str()).unwrap_or("");
        let file_path_str = file_path.to_string_lossy();

        // 检查默认忽略模式
        for default_pattern in DEFAULT_IGNORE_PATTERNS {
            if default_pattern.contains('/') {
                if self.matches_path_pattern(&file_path_str, default_pattern) {
                    return Ok(true);
                }
            } else {
                if self.matches_pattern(file_name, default_pattern) {
                    return Ok(true);
                }
            }
        }

        // 检查.gitignore规则
        if let Some(first_allowed) = self.allowed_paths.first() {
            if self.gitignore_parser.is_ignored(file_path, first_allowed) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// Get information about allowed directories
    pub fn get_allowed_directories(&self) -> Vec<DirectoryInfo> {
        self.allowed_paths
            .iter()
            .map(|path| self.get_directory_info(path))
            .collect()
    }

    /// Get detailed information about a directory
    fn get_directory_info(&self, path: &Path) -> DirectoryInfo {
        let path_str = path.display().to_string();
        let exists = path.exists();
        let is_accessible = exists && path.is_dir();

        let canonical_path = if exists {
            path.canonicalize().map(|p| p.display().to_string()).ok()
        } else {
            None
        };

        let size_info = if is_accessible {
            self.calculate_directory_size(path).ok()
        } else {
            None
        };

        DirectoryInfo {
            path: path_str,
            exists,
            is_accessible,
            canonical_path,
            size_info,
        }
    }

    /// Calculate directory size information
    fn calculate_directory_size(&self, path: &Path) -> Result<DirectorySizeInfo> {
        let mut total_files = 0;
        let mut total_directories = 0;
        let mut total_size = 0;

        // Limit to prevent hanging on very large directories
        let mut count = 0;
        const MAX_ENTRIES: usize = 10000;

        for entry in WalkDir::new(path).max_depth(3) {
            if count >= MAX_ENTRIES {
                break;
            }
            count += 1;

            match entry {
                Ok(entry) => {
                    let metadata = entry.metadata();
                    if let Ok(metadata) = metadata {
                        if metadata.is_file() {
                            total_files += 1;
                            total_size += metadata.len();
                        } else if metadata.is_dir() {
                            total_directories += 1;
                        }
                    }
                }
                Err(_) => {
                    // Skip entries we can't read
                    continue;
                }
            }
        }

        Ok(DirectorySizeInfo {
            total_files,
            total_directories,
            total_size,
        })
    }

    /// Search for content within files
    pub fn search_content(&self, config: SearchConfig) -> Result<SearchResult> {
        let start_time = std::time::Instant::now();

        // 验证搜索目录权限
        let search_path = Path::new(&config.directory);
        self.is_path_allowed(search_path)?;

        // 编译正则表达式（如果需要）
        let pattern = if config.use_regex {
            Some(self.compile_regex(&config.query, config.case_sensitive)?)
        } else {
            None
        };

        // 收集要搜索的文件
        let files_to_search = self.collect_search_files(&config)?;

        // 执行搜索
        let mut all_matches = Vec::new();
        let mut files_searched = 0;
        let mut files_with_matches = 0;

        for file_path in files_to_search {
            files_searched += 1;

            if let Some(file_matches) = self.search_file_content(&file_path, &config, &pattern)? {
                files_with_matches += 1;
                all_matches.push(file_matches);

                // 限制结果数量
                if let Some(max_results) = config.max_results {
                    if files_with_matches >= max_results {
                        break;
                    }
                }
            }
        }

        let total_matches = all_matches.iter().map(|fm| fm.total_matches).sum();

        Ok(SearchResult {
            query: config.query,
            total_matches,
            files_searched,
            files_with_matches,
            matches: all_matches,
            search_time_ms: start_time.elapsed().as_millis(),
        })
    }

    /// 搜索单个文件内容
    fn search_file_content(
        &self,
        file_path: &Path,
        config: &SearchConfig,
        pattern: &Option<Regex>,
    ) -> Result<Option<FileMatch>> {
        // 检查文件大小限制
        if let Some(max_size) = config.max_file_size {
            if file_path.metadata()?.len() > max_size {
                return Ok(None);
            }
        }

        // 读取文件内容
        let content = match std::fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(_) => return Ok(None), // 跳过无法读取的文件
        };

        let lines: Vec<&str> = content.lines().collect();
        let mut line_matches = Vec::new();

        for (line_num, line) in lines.iter().enumerate() {
            if let Some(match_info) = self.search_line(line, line_num + 1, config, pattern)? {
                // 添加上下文
                let context_before = if let Some(context_lines) = config.context_lines {
                    self.get_context_lines(&lines, line_num, context_lines, true)
                } else {
                    None
                };

                let context_after = if let Some(context_lines) = config.context_lines {
                    self.get_context_lines(&lines, line_num, context_lines, false)
                } else {
                    None
                };

                line_matches.push(LineMatch {
                    line_number: line_num + 1,
                    content: line.to_string(),
                    start_index: match_info.0,
                    end_index: match_info.1,
                    context_before,
                    context_after,
                });
            }
        }

        if line_matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(FileMatch {
                file_path: file_path.to_string_lossy().to_string(),
                total_matches: line_matches.len(),
                line_matches,
            }))
        }
    }

    /// 搜索单行内容
    fn search_line(
        &self,
        line: &str,
        _line_number: usize,
        config: &SearchConfig,
        pattern: &Option<Regex>,
    ) -> Result<Option<(usize, usize)>> {
        if let Some(regex) = pattern {
            // 使用正则表达式搜索
            if let Some(mat) = regex.find(line) {
                Ok(Some((mat.start(), mat.end())))
            } else {
                Ok(None)
            }
        } else {
            // 字符串字面量搜索
            let search_line = if config.case_sensitive {
                line.to_string()
            } else {
                line.to_lowercase()
            };

            let search_query = if config.case_sensitive {
                config.query.clone()
            } else {
                config.query.to_lowercase()
            };

            if let Some(pos) = search_line.find(&search_query) {
                Ok(Some((pos, pos + search_query.len())))
            } else {
                Ok(None)
            }
        }
    }

    /// 收集要搜索的文件
    fn collect_search_files(&self, config: &SearchConfig) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let search_path = Path::new(&config.directory);

        if config.recursive {
            for entry in WalkDir::new(search_path)
                .follow_links(false)
                .into_iter()
                .filter_map(|e| e.ok())
            {
                let path = entry.path();
                if path.is_file() && self.should_search_file(path, config)? {
                    files.push(path.to_path_buf());
                }
            }
        } else {
            for entry in std::fs::read_dir(search_path)? {
                let entry = entry?;
                let path = entry.path();
                if path.is_file() && self.should_search_file(&path, config)? {
                    files.push(path);
                }
            }
        }

        Ok(files)
    }

    /// 判断是否应该搜索该文件
    fn should_search_file(&self, file_path: &Path, config: &SearchConfig) -> Result<bool> {
        // 检查文件权限
        if self.is_path_allowed(file_path).is_err() {
            return Ok(false);
        }

        let file_name = file_path.file_name().and_then(|n| n.to_str()).unwrap_or("");
        let file_path_str = file_path.to_string_lossy();

        // 检查默认忽略模式
        for default_pattern in DEFAULT_IGNORE_PATTERNS {
            if default_pattern.contains('/') {
                if self.matches_path_pattern(&file_path_str, default_pattern) {
                    return Ok(false);
                }
            } else {
                if self.matches_pattern(file_name, default_pattern) {
                    return Ok(false);
                }
            }
        }

        // 检查.gitignore规则
        if let Some(first_allowed) = self.allowed_paths.first() {
            if self.gitignore_parser.is_ignored(file_path, first_allowed) {
                return Ok(false);
            }
        }

        // 检查用户提供的排除模式（同时检查文件名和路径）
        for exclude_pattern in &config.exclude_patterns {
            // 如果模式包含路径分隔符，则匹配完整路径
            if exclude_pattern.contains('/') {
                if self.matches_path_pattern(&file_path_str, exclude_pattern) {
                    return Ok(false);
                }
            } else {
                // 否则只匹配文件名
                if self.matches_pattern(file_name, exclude_pattern) {
                    return Ok(false);
                }
            }
        }

        // 检查包含模式
        if !config.include_patterns.is_empty() {
            let mut matches = false;
            for include_pattern in &config.include_patterns {
                if self.matches_pattern(file_name, include_pattern) {
                    matches = true;
                    break;
                }
            }
            if !matches {
                return Ok(false);
            }
        }

        // 检查文件大小
        if let Some(max_size) = config.max_file_size {
            if file_path.metadata()?.len() > max_size {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// 简单的通配符匹配
    fn matches_pattern(&self, text: &str, pattern: &str) -> bool {
        // 简单实现，支持 * 和 ? 通配符
        let regex_pattern = pattern
            .replace(".", r"\.")
            .replace("*", ".*")
            .replace("?", ".");

        if let Ok(regex) = Regex::new(&format!("^{}$", regex_pattern)) {
            regex.is_match(text)
        } else {
            false
        }
    }

    /// 路径模式匹配（支持路径分隔符）
    fn matches_path_pattern(&self, path: &str, pattern: &str) -> bool {
        // 将路径和模式标准化（统一使用 / 分隔符）
        let normalized_path = path.replace('\\', "/");
        let normalized_pattern = pattern.replace('\\', "/");

        // 检查是否为目录排除模式（以 / 结尾）
        if normalized_pattern.ends_with('/') {
            let dir_pattern = &normalized_pattern[..normalized_pattern.len() - 1];
            return normalized_path.contains(&format!("/{}/", dir_pattern))
                || normalized_path.contains(&format!("/{}", dir_pattern));
        }

        // 检查是否包含目录模式（如 node_modules/*）
        if normalized_pattern.contains('/') {
            let regex_pattern = normalized_pattern
                .replace(".", r"\.")
                .replace("*", ".*")
                .replace("?", ".");

            if let Ok(regex) = Regex::new(&regex_pattern) {
                return regex.is_match(&normalized_path);
            }
        }

        false
    }

    /// 获取上下文行
    fn get_context_lines(
        &self,
        lines: &[&str],
        current_line: usize,
        context_lines: usize,
        before: bool,
    ) -> Option<Vec<String>> {
        let mut context = Vec::new();

        if before {
            let start = if current_line > context_lines {
                current_line - context_lines
            } else {
                0
            };

            for i in start..current_line {
                if let Some(line) = lines.get(i) {
                    context.push(line.to_string());
                }
            }
        } else {
            let end = std::cmp::min(current_line + context_lines + 1, lines.len());
            for i in (current_line + 1)..end {
                if let Some(line) = lines.get(i) {
                    context.push(line.to_string());
                }
            }
        }

        if context.is_empty() {
            None
        } else {
            Some(context)
        }
    }

    /// 编译正则表达式
    fn compile_regex(&self, pattern: &str, case_sensitive: bool) -> Result<Regex> {
        let mut regex_builder = RegexBuilder::new(pattern);
        regex_builder.case_insensitive(!case_sensitive);

        regex_builder
            .build()
            .with_context(|| format!("Invalid regex pattern: {}", pattern))
    }
}
