use anyhow::{Context, Result};
use clap::<PERSON>rse<PERSON>;
use std::io::{self, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Write};
use std::path::{Path, PathBuf};
use tracing::{debug, error, info, warn};

mod filesystem;
mod mcp;

use filesystem::FileSystemOperations;
use mcp::{McpRequest, McpResponse, McpServer};

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Enable debug logging
    #[arg(short, long)]
    debug: bool,

    /// Set the log level
    #[arg(short, long, default_value = "info")]
    log_level: String,

    /// Allowed directories for filesystem operations (comma-separated)
    #[arg(
        long,
        value_delimiter = ',',
        help = "Comma-separated list of allowed directories"
    )]
    allowed_directories: Vec<String>,
}

/// Validate and convert allowed directories to PathBuf
fn validate_allowed_directories(dirs: &[String]) -> Result<Vec<PathBuf>> {
    if dirs.is_empty() {
        return Err(anyhow::anyhow!(
            "No allowed directories specified. Use --allowed-directories to specify at least one directory."
        ));
    }

    let mut validated_paths = Vec::new();

    for dir in dirs {
        let path = Path::new(dir);

        // Check if directory exists
        if !path.exists() {
            return Err(anyhow::anyhow!("Directory does not exist: '{}'", dir));
        }

        // Check if it's actually a directory
        if !path.is_dir() {
            return Err(anyhow::anyhow!("Path is not a directory: '{}'", dir));
        }

        // Canonicalize the path to resolve symlinks and relative paths
        let canonical_path = path
            .canonicalize()
            .with_context(|| format!("Failed to canonicalize directory: '{}'", dir))?;

        info!("Validated allowed directory: {}", canonical_path.display());
        validated_paths.push(canonical_path);
    }

    // Check for duplicate or overlapping paths
    for (i, path1) in validated_paths.iter().enumerate() {
        for (j, path2) in validated_paths.iter().enumerate() {
            if i != j && (path1.starts_with(path2) || path2.starts_with(path1)) {
                warn!(
                    "Directory '{}' overlaps with '{}' - this may cause unexpected behavior",
                    path1.display(),
                    path2.display()
                );
            }
        }
    }

    Ok(validated_paths)
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Initialize tracing
    let level = match args.log_level.as_str() {
        "trace" => tracing::Level::TRACE,
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::fmt().with_max_level(level).init();

    info!("Starting filesystem MCP server");

    // Validate allowed directories
    let allowed_paths = validate_allowed_directories(&args.allowed_directories)?;
    info!(
        "Filesystem access restricted to {} director{}",
        allowed_paths.len(),
        if allowed_paths.len() == 1 { "y" } else { "ies" }
    );

    let mut server = McpServer::new();
    let mut fs_ops = FileSystemOperations::new(allowed_paths);

    // Read from stdin and write to stdout for MCP communication
    let stdin = io::stdin();
    let mut reader = BufReader::new(stdin);
    let mut line = String::new();

    loop {
        line.clear();
        match reader.read_line(&mut line) {
            Ok(0) => {
                debug!("Received EOF, shutting down");
                break;
            }
            Ok(_) => {
                let line = line.trim();
                if line.is_empty() {
                    continue;
                }

                debug!("Received: {}", line);

                match serde_json::from_str::<McpRequest>(line) {
                    Ok(request) => {
                        let response = server.handle_request(request, &mut fs_ops).await;
                        let response_json = serde_json::to_string(&response)?;
                        println!("{}", response_json);
                        io::stdout().flush()?;
                        debug!("Sent: {}", response_json);
                    }
                    Err(e) => {
                        error!("Failed to parse request: {}", e);
                        let error_response =
                            McpResponse::error(None, "parse_error".to_string(), e.to_string());
                        let response_json = serde_json::to_string(&error_response)?;
                        println!("{}", response_json);
                        io::stdout().flush()?;
                    }
                }
            }
            Err(e) => {
                error!("Failed to read from stdin: {}", e);
                break;
            }
        }
    }

    info!("Filesystem MCP server shutting down");
    Ok(())
}
