use anyhow::Result;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use tracing::{debug, error};

use crate::filesystem::FileSystemOperations;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct McpRequest {
    pub jsonrpc: String,
    pub id: Option<Value>,
    pub method: String,
    pub params: Option<Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum McpResponse {
    Success {
        jsonrpc: String,
        id: Option<Value>,
        result: Value,
    },
    Error {
        jsonrpc: String,
        id: Option<Value>,
        error: McpError,
    },
}

impl McpResponse {
    pub fn success(id: Option<Value>, result: Value) -> Self {
        Self::Success {
            jsonrpc: "2.0".to_string(),
            id,
            result,
        }
    }

    pub fn error(id: Option<Value>, code: String, message: String) -> Self {
        Self::Error {
            jsonrpc: "2.0".to_string(),
            id,
            error: McpError {
                code,
                message,
                data: None,
            },
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct McpError {
    pub code: String,
    pub message: String,
    pub data: Option<Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tool {
    pub name: String,
    pub description: String,
    pub input_schema: Value,
}

pub struct McpServer {
    pub name: String,
    pub version: String,
    pub tools: Vec<Tool>,
}

impl McpServer {
    pub fn new() -> Self {
        let tools = vec![
            Tool {
                name: "list_directory".to_string(),
                description: "List contents of a directory".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to list"
                        },
                        "recursive": {
                            "type": "boolean",
                            "description": "Whether to list recursively",
                            "default": false
                        }
                    },
                    "required": ["path"]
                }),
            },
            Tool {
                name: "read_file".to_string(),
                description: "Read content of a file with optional line limiting".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "File path to read"
                        },
                        "max_lines": {
                            "type": "integer",
                            "description": "Maximum number of lines to read (default: 200, null for all lines)"
                        },
                        "start_line": {
                            "type": "integer",
                            "description": "Starting line number (1-based, optional)"
                        },
                        "end_line": {
                            "type": "integer",
                            "description": "Ending line number (1-based, optional)"
                        }
                    },
                    "required": ["path"]
                }),
            },
            Tool {
                name: "write_file".to_string(),
                description: "Write content to a file".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "File path to write"
                        },
                        "content": {
                            "type": "string",
                            "description": "Content to write to the file"
                        }
                    },
                    "required": ["path", "content"]
                }),
            },
            Tool {
                name: "create_directory".to_string(),
                description: "Create a directory".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to create"
                        },
                        "recursive": {
                            "type": "boolean",
                            "description": "Whether to create parent directories",
                            "default": false
                        }
                    },
                    "required": ["path"]
                }),
            },
            Tool {
                name: "delete".to_string(),
                description: "Delete a file or directory".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Path to delete"
                        },
                        "recursive": {
                            "type": "boolean",
                            "description": "Whether to delete recursively (for directories)",
                            "default": false
                        }
                    },
                    "required": ["path"]
                }),
            },
            Tool {
                name: "get_file_info".to_string(),
                description: "Get information about a file or directory".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Path to get information about"
                        }
                    },
                    "required": ["path"]
                }),
            },
            Tool {
                name: "search_files".to_string(),
                description: "Search for files matching a pattern".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "base_path": {
                            "type": "string",
                            "description": "Base directory to search in"
                        },
                        "pattern": {
                            "type": "string",
                            "description": "Pattern to search for"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "Maximum number of results to return",
                            "default": 100
                        }
                    },
                    "required": ["base_path", "pattern"]
                }),
            },
            Tool {
                name: "list_allowed_directories".to_string(),
                description: "List currently allowed directories with their status and information"
                    .to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {},
                    "additionalProperties": false
                }),
            },
            Tool {
                name: "search_content".to_string(),
                description: "Search for content within files using text or regex patterns"
                    .to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query (text or regex pattern)"
                        },
                        "directory": {
                            "type": "string",
                            "description": "Directory to search in"
                        },
                        "recursive": {
                            "type": "boolean",
                            "description": "Search recursively in subdirectories",
                            "default": true
                        },
                        "case_sensitive": {
                            "type": "boolean",
                            "description": "Case sensitive search",
                            "default": false
                        },
                        "use_regex": {
                            "type": "boolean",
                            "description": "Treat query as regular expression",
                            "default": false
                        },
                        "include_patterns": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "File patterns to include (e.g., *.rs, *.txt)"
                        },
                        "exclude_patterns": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "File patterns to exclude (e.g., *.log, node_modules)"
                        },
                        "max_file_size": {
                            "type": "integer",
                            "description": "Maximum file size to search (in bytes)"
                        },
                        "context_lines": {
                            "type": "integer",
                            "description": "Number of context lines to show before/after matches"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "Maximum number of files with matches to return"
                        }
                    },
                    "required": ["query", "directory"]
                }),
            },
        ];

        Self {
            name: "filesystem-mcp".to_string(),
            version: "0.1.0".to_string(),
            tools,
        }
    }

    pub async fn handle_request(
        &mut self,
        request: McpRequest,
        fs_ops: &mut FileSystemOperations,
    ) -> McpResponse {
        debug!("Handling request: {}", request.method);

        match request.method.as_str() {
            "initialize" => self.handle_initialize(request.id),
            "tools/list" => self.handle_list_tools(request.id),
            "tools/call" => {
                self.handle_tool_call(request.id, request.params, fs_ops)
                    .await
            }
            _ => McpResponse::error(
                request.id,
                "method_not_found".to_string(),
                format!("Method not found: {}", request.method),
            ),
        }
    }

    fn handle_initialize(&self, id: Option<Value>) -> McpResponse {
        let result = json!({
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": {
                "name": self.name,
                "version": self.version
            }
        });

        McpResponse::success(id, result)
    }

    fn handle_list_tools(&self, id: Option<Value>) -> McpResponse {
        let tools_json: Vec<Value> = self
            .tools
            .iter()
            .map(|tool| {
                json!({
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.input_schema
                })
            })
            .collect();

        let result = json!({
            "tools": tools_json
        });

        McpResponse::success(id, result)
    }

    async fn handle_tool_call(
        &self,
        id: Option<Value>,
        params: Option<Value>,
        fs_ops: &mut FileSystemOperations,
    ) -> McpResponse {
        let params = match params {
            Some(p) => p,
            None => {
                return McpResponse::error(
                    id,
                    "invalid_params".to_string(),
                    "Missing parameters".to_string(),
                );
            }
        };

        let tool_name = match params.get("name") {
            Some(Value::String(name)) => name,
            _ => {
                return McpResponse::error(
                    id,
                    "invalid_params".to_string(),
                    "Missing or invalid tool name".to_string(),
                );
            }
        };

        let arguments = params.get("arguments").cloned().unwrap_or(json!({}));

        let response = match self.execute_tool(tool_name, arguments, fs_ops).await {
            Ok(result) => {
                // 成功情况：返回工具结果
                json!({
                    "content": [
                        {
                            "type": "text",
                            "text": serde_json::to_string_pretty(&result).unwrap_or_else(|_| result.to_string())
                        }
                    ]
                })
            }
            Err(e) => {
                // 错误情况：按照 MCP 协议，错误信息应该放在成功响应的 data 中
                error!("Tool execution failed: {}", e);
                json!({
                    "content": [
                        {
                            "type": "text",
                            "text": json!({
                                "error": {
                                    "code": "tool_error",
                                    "message": e.to_string()
                                }
                            }).to_string()
                        }
                    ],
                    "isError": true
                })
            }
        };

        McpResponse::success(id, response)
    }

    async fn execute_tool(
        &self,
        tool_name: &str,
        arguments: Value,
        fs_ops: &mut FileSystemOperations,
    ) -> Result<Value> {
        match tool_name {
            "list_directory" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;
                let recursive = arguments["recursive"].as_bool().unwrap_or(false);

                let listing = fs_ops.list_directory(path, recursive)?;
                Ok(serde_json::to_value(listing)?)
            }
            "read_file" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;

                // 构建读取配置
                let config = if arguments.get("max_lines").is_some()
                    || arguments.get("start_line").is_some()
                    || arguments.get("end_line").is_some()
                {
                    let max_lines = arguments
                        .get("max_lines")
                        .and_then(|v| v.as_u64())
                        .map(|n| n as usize);
                    let start_line = arguments
                        .get("start_line")
                        .and_then(|v| v.as_u64())
                        .map(|n| n as usize);
                    let end_line = arguments
                        .get("end_line")
                        .and_then(|v| v.as_u64())
                        .map(|n| n as usize);

                    Some(crate::filesystem::ReadFileConfig {
                        max_lines,
                        start_line,
                        end_line,
                    })
                } else {
                    None // 使用默认配置
                };

                let content = fs_ops.read_file(path, config)?;
                Ok(serde_json::to_value(content)?)
            }
            "write_file" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;
                let content = arguments["content"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing content parameter"))?;

                fs_ops.write_file(path, content)?;
                Ok(json!({"success": true, "message": format!("File written: {}", path)}))
            }
            "create_directory" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;
                let recursive = arguments["recursive"].as_bool().unwrap_or(false);

                fs_ops.create_directory(path, recursive)?;
                Ok(json!({"success": true, "message": format!("Directory created: {}", path)}))
            }
            "delete" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;
                let recursive = arguments["recursive"].as_bool().unwrap_or(false);

                fs_ops.delete(path, recursive)?;
                Ok(json!({"success": true, "message": format!("Deleted: {}", path)}))
            }
            "get_file_info" => {
                let path = arguments["path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing path parameter"))?;

                let info = fs_ops.get_file_info(path)?;
                Ok(serde_json::to_value(info)?)
            }
            "search_files" => {
                let base_path = arguments["base_path"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing base_path parameter"))?;
                let pattern = arguments["pattern"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing pattern parameter"))?;
                let max_results = arguments["max_results"].as_u64().map(|n| n as usize);

                let results = fs_ops.search_files(base_path, pattern, max_results)?;
                Ok(json!({
                    "results": results,
                    "count": results.len()
                }))
            }
            "list_allowed_directories" => {
                let allowed_dirs = fs_ops.get_allowed_directories();
                Ok(json!({
                    "allowed_directories": allowed_dirs,
                    "total_count": allowed_dirs.len(),
                    "summary": {
                        "accessible_count": allowed_dirs.iter().filter(|d| d.is_accessible).count(),
                        "total_directories": allowed_dirs.len()
                    }
                }))
            }
            "search_content" => {
                let query = arguments["query"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing query parameter"))?;
                let directory = arguments["directory"]
                    .as_str()
                    .ok_or_else(|| anyhow::anyhow!("Missing directory parameter"))?;

                let recursive = arguments["recursive"].as_bool().unwrap_or(true);
                let case_sensitive = arguments["case_sensitive"].as_bool().unwrap_or(false);
                let use_regex = arguments["use_regex"].as_bool().unwrap_or(false);

                let include_patterns = arguments["include_patterns"]
                    .as_array()
                    .map(|arr| {
                        arr.iter()
                            .filter_map(|v| v.as_str().map(|s| s.to_string()))
                            .collect()
                    })
                    .unwrap_or_default();

                let exclude_patterns = arguments["exclude_patterns"]
                    .as_array()
                    .map(|arr| {
                        arr.iter()
                            .filter_map(|v| v.as_str().map(|s| s.to_string()))
                            .collect()
                    })
                    .unwrap_or_default();

                let max_file_size = arguments["max_file_size"].as_u64();
                let context_lines = arguments["context_lines"].as_u64().map(|n| n as usize);
                let max_results = arguments["max_results"].as_u64().map(|n| n as usize);

                let config = crate::filesystem::SearchConfig {
                    query: query.to_string(),
                    directory: directory.to_string(),
                    recursive,
                    case_sensitive,
                    use_regex,
                    include_patterns,
                    exclude_patterns,
                    max_file_size,
                    context_lines,
                    max_results,
                };

                let result = fs_ops.search_content(config)?;
                Ok(serde_json::to_value(result)?)
            }
            _ => Err(anyhow::anyhow!("Unknown tool: {}", tool_name)),
        }
    }
}
