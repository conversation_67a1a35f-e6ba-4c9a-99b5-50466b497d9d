[package]
name = "filesystem-mcp"
version = "0.1.0"
edition = "2021"
description = "MCP server for filesystem operations"
authors = ["Claude Code"]

[[bin]]
name = "filesystem-mcp"
path = "src/main.rs"

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
clap = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

# Additional dependencies for filesystem operations
walkdir = "2.3"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
regex = "1.10"
glob = "0.3"

[dev-dependencies]
tempfile = "3.8"
