# Example Tool

这是一个内置 MCP 工具的示例模板，展示了如何创建新的内置工具。

## 功能

该工具提供以下功能：

1. **echo**: 回显输入的消息
2. **get_info**: 获取工具信息

## 使用方法

### 构建

```bash
# 从 tools 目录构建
cargo build --release

# 或者构建特定包
cd example-tool
cargo build --release
```

### 运行

```bash
# 基本用法
./target/release/example-tool

# 带自定义消息
./target/release/example-tool --message "Custom default message"

# 启用调试日志
./target/release/example-tool --debug --log-level debug
```

### MCP 通信示例

初始化服务器：

```json
{ "jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {} }
```

列出可用工具：

```json
{ "jsonrpc": "2.0", "id": 2, "method": "tools/list" }
```

调用 echo 工具：

```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "echo",
    "arguments": {
      "message": "Hello, World!"
    }
  }
}
```

调用 get_info 工具：

```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "get_info",
    "arguments": {}
  }
}
```

## 开发指南

### 创建新的内置工具

1. 在 `tools/` 目录下创建新的包目录
2. 复制此示例工具的结构
3. 修改 `Cargo.toml` 中的包名和描述
4. 更新 `src/tool_handler.rs` 中的工具逻辑
5. 更新 `src/mcp.rs` 中的工具列表
6. 更新 `tools/Cargo.toml` 的 workspace members
7. 更新 `clients/node/db/migrate/migrateProviderDB.ts` 中的 BUILTIN_TOOLS 数组

### 代码结构

- `src/main.rs`: 程序入口点，处理命令行参数和主循环
- `src/mcp.rs`: MCP 协议处理，包括请求/响应解析和路由
- `src/tool_handler.rs`: 具体的工具功能实现

### 最佳实践

1. **错误处理**: 使用 `anyhow::Result` 进行错误处理
2. **日志记录**: 使用 `tracing` 库记录调试信息
3. **参数验证**: 在工具函数中验证输入参数
4. **文档**: 为每个工具提供清晰的描述和输入 schema
5. **测试**: 编写单元测试验证工具功能

## 集成到应用

1. 构建工具: `cargo build --release`
2. 复制到 `external/` 目录
3. 更新数据库迁移脚本以注册新工具
4. 重启应用以加载新工具

## 安全考虑

1. 输入验证：始终验证用户输入
2. 权限控制：限制工具的访问权限
3. 资源限制：避免无限制的资源使用
4. 错误信息：不要在错误信息中泄露敏感信息
