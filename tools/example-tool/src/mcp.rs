use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use tracing::debug;

use crate::tool_handler::Too<PERSON><PERSON><PERSON><PERSON>;

#[derive(Debug, Deserialize)]
pub struct McpRequest {
    pub jsonrpc: String,
    pub id: Option<Value>,
    pub method: String,
    pub params: Option<Value>,
}

#[derive(Debug, Serialize)]
pub struct McpResponse {
    pub jsonrpc: String,
    pub id: Option<Value>,
    pub result: Option<Value>,
    pub error: Option<McpError>,
}

#[derive(Debug, Serialize)]
pub struct McpError {
    pub code: String,
    pub message: String,
}

impl McpResponse {
    pub fn success(id: Option<Value>, result: Value) -> Self {
        Self {
            jsonrpc: "2.0".to_string(),
            id,
            result: Some(result),
            error: None,
        }
    }

    pub fn error(id: Option<Value>, code: String, message: String) -> Self {
        Self {
            jsonrpc: "2.0".to_string(),
            id,
            result: None,
            error: Some(McpError { code, message }),
        }
    }
}

pub struct McpServer {
    pub name: String,
    pub version: String,
}

impl McpServer {
    pub fn new(name: String, version: String) -> Self {
        Self { name, version }
    }

    pub async fn handle_request(
        &mut self,
        request: McpRequest,
        tool_handler: &mut ToolHandler,
    ) -> McpResponse {
        debug!("Handling request: {}", request.method);

        match request.method.as_str() {
            "initialize" => self.handle_initialize(request.id),
            "tools/list" => self.handle_list_tools(request.id),
            "tools/call" => {
                self.handle_tool_call(request.id, request.params, tool_handler)
                    .await
            }
            _ => McpResponse::error(
                request.id,
                "method_not_found".to_string(),
                format!("Method not found: {}", request.method),
            ),
        }
    }

    fn handle_initialize(&self, id: Option<Value>) -> McpResponse {
        let result = json!({
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": {
                "name": self.name,
                "version": self.version
            }
        });

        McpResponse::success(id, result)
    }

    fn handle_list_tools(&self, id: Option<Value>) -> McpResponse {
        let tools = vec![
            json!({
                "name": "echo",
                "description": "Echo back the input message",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "Message to echo back"
                        }
                    },
                    "required": ["message"]
                }
            }),
            json!({
                "name": "get_info",
                "description": "Get tool information",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }),
        ];

        let result = json!({
            "tools": tools
        });

        McpResponse::success(id, result)
    }

    async fn handle_tool_call(
        &self,
        id: Option<Value>,
        params: Option<Value>,
        tool_handler: &mut ToolHandler,
    ) -> McpResponse {
        let params = match params {
            Some(p) => p,
            None => {
                return McpResponse::error(
                    id,
                    "invalid_params".to_string(),
                    "Missing parameters".to_string(),
                );
            }
        };

        let tool_name = match params.get("name") {
            Some(name) => name.as_str().unwrap_or(""),
            None => {
                return McpResponse::error(
                    id,
                    "invalid_params".to_string(),
                    "Missing tool name".to_string(),
                );
            }
        };

        let arguments = params.get("arguments").cloned().unwrap_or(json!({}));

        let result = tool_handler.execute_tool(tool_name, arguments).await;

        match result {
            Ok(output) => McpResponse::success(id, json!({ "content": [{ "type": "text", "text": output }] })),
            Err(e) => McpResponse::error(
                id,
                "tool_error".to_string(),
                format!("Tool execution failed: {}", e),
            ),
        }
    }
}
