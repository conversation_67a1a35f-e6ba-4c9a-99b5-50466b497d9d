use anyhow::Result;
use clap::Parser;
use serde_json::{json, Value};
use std::io::{self, <PERSON><PERSON><PERSON><PERSON>, BufReader, Write};
use tracing::{debug, error, info, warn};
use tracing_subscriber;

mod mcp;
mod tool_handler;

use mcp::{McpRequest, McpResponse, McpServer};
use tool_handler::ToolHandler;

#[derive(Parser)]
#[command(name = "example-tool")]
#[command(about = "An example built-in MCP tool")]
struct Args {
    #[arg(long, default_value = "info")]
    log_level: String,

    #[arg(long)]
    debug: bool,

    // 在这里添加工具特定的参数
    #[arg(long, default_value = "Hello from example tool!")]
    message: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // 初始化日志
    let log_level = if args.debug {
        "debug"
    } else {
        &args.log_level
    };

    let level = match log_level {
        "error" => tracing::Level::ERROR,
        "warn" => tracing::Level::WARN,
        "info" => tracing::Level::INFO,
        "debug" => tracing::Level::DEBUG,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::fmt()
        .with_max_level(level)
        .init();

    info!("Starting example-tool MCP server");

    // 创建MCP服务器实例
    let mut server = McpServer::new("example-tool".to_string(), "0.1.0".to_string());

    // 创建工具处理器
    let mut tool_handler = ToolHandler::new(args.message);

    // 从stdin读取请求
    let stdin = io::stdin();
    let reader = BufReader::new(stdin.lock());

    for line in reader.lines() {
        let line = line?;
        debug!("Received: {}", line);

        // 解析MCP请求
        let request: McpRequest = match serde_json::from_str(&line) {
            Ok(req) => req,
            Err(e) => {
                error!("Failed to parse request: {}", e);
                continue;
            }
        };

        // 处理请求
        let response = server.handle_request(request, &mut tool_handler).await;

        // 发送响应
        let response_json = serde_json::to_string(&response)?;
        println!("{}", response_json);
        io::stdout().flush()?;
    }

    Ok(())
}
