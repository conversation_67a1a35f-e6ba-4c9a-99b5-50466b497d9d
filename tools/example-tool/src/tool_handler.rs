use anyhow::Result;
use serde_json::Value;
use tracing::debug;

pub struct ToolHandler {
    default_message: String,
}

impl ToolHandler {
    pub fn new(default_message: String) -> Self {
        Self { default_message }
    }

    pub async fn execute_tool(&mut self, tool_name: &str, arguments: Value) -> Result<String> {
        debug!("Executing tool: {} with arguments: {}", tool_name, arguments);

        match tool_name {
            "echo" => self.handle_echo(arguments),
            "get_info" => self.handle_get_info(arguments),
            _ => Err(anyhow::anyhow!("Unknown tool: {}", tool_name)),
        }
    }

    fn handle_echo(&self, arguments: Value) -> Result<String> {
        let message = arguments
            .get("message")
            .and_then(|v| v.as_str())
            .unwrap_or(&self.default_message);

        Ok(format!("Echo: {}", message))
    }

    fn handle_get_info(&self, _arguments: Value) -> Result<String> {
        let info = format!(
            "Example Tool Information:\n\
            - Name: example-tool\n\
            - Version: 0.1.0\n\
            - Description: A template for creating new built-in MCP tools\n\
            - Available tools:\n\
              * echo: Echo back a message\n\
              * get_info: Get tool information\n\
            - Default message: {}"
        , self.default_message);

        Ok(info)
    }
}
