# 内置工具构建指南

本目录包含了 MCP Web 应用的内置工具源代码。这些工具使用 Rust 编写，通过 MCP 协议与主应用通信。

## 目录结构

```
tools/
├── Cargo.toml              # Rust 工作区配置
├── filesystem-mcp/         # 文件系统操作工具
├── example-tool/           # 示例工具模板
└── README.md              # 本文件
```

## 构建工具

### 前提条件

1. **安装 Rust 工具链**

   ```bash
   # 安装 Rust
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

   # 或者在 Windows 上从 https://rustup.rs/ 下载安装程序
   ```

2. **验证安装**
   ```bash
   cargo --version
   rustc --version
   ```

### 构建所有工具

从项目根目录运行：

```bash
# 构建所有内置工具
node scripts/build-builtin-tools.js
```

该脚本会：

- 检查 Rust 工具链是否安装
- 构建所有工具的 release 版本
- 将可执行文件复制到 `external/` 目录
- 根据操作系统设置正确的文件权限

### 跨平台构建

#### Windows 平台

在 Windows 上构建时，生成的可执行文件会自动添加 `.exe` 后缀：

```bash
# 构建后生成的文件
external/filesystem-mcp.exe
external/example-tool.exe
```

#### Unix 平台 (macOS/Linux)

在 Unix 系统上构建时，生成的是无后缀的可执行文件：

```bash
# 构建后生成的文件
external/filesystem-mcp
external/example-tool
```

#### 交叉编译

如果需要为其他平台构建，可以添加相应的目标：

```bash
# 添加 Windows 目标（从 Unix 构建）
rustup target add x86_64-pc-windows-gnu

# 构建 Windows 版本
cargo build --release --target x86_64-pc-windows-gnu

# 添加 macOS 目标（从 Linux 构建）
rustup target add x86_64-apple-darwin

# 构建 macOS 版本
cargo build --release --target x86_64-apple-darwin
```

## 内置工具配置

内置工具的配置在 `clients/node/db/migrate/migrateProviderDB.ts` 中定义，会根据操作系统自动选择正确的可执行文件路径：

```typescript
// 根据操作系统获取可执行文件路径
function getExecutablePath(toolName: string): string {
  const externalDir = path.join(process.cwd(), 'external');
  return path.join(externalDir, isWin ? `${toolName}.exe` : toolName);
}
```

## 工具列表

### 1. filesystem-mcp

**功能**: 提供文件系统操作功能

**工具**:

- `list_directory` - 列出目录内容
- `read_file` - 读取文件内容 (支持行数限制和范围读取)
- `write_file` - 写入文件内容
- `create_directory` - 创建目录
- `delete` - 删除文件或目录
- `get_file_info` - 获取文件信息
- `search_files` - 搜索文件
- `list_allowed_directories` - 列出允许访问的目录
- `search_content` - 搜索文件内容 (类似 grep 功能)

**参数**:

- `--log-level`: 日志级别 (默认: error)
- `--allowed-directories`: 允许访问的目录路径

#### read_file 工具详细说明

`read_file` 工具用于读取文件内容，支持行数限制和范围读取，避免一次性读取大文件造成的性能问题。

**主要特性**:

- 默认读取前 200 行
- 支持指定最大行数限制
- 支持指定行范围读取
- 支持读取全部内容
- 提供文件元数据信息（总行数、是否截断等）

**参数**:

- `path` (必需): 文件路径
- `max_lines` (可选): 最大行数限制 (默认: 200, null 表示读取全部)
- `start_line` (可选): 开始行号 (1-based)
- `end_line` (可选): 结束行号 (1-based)

**使用示例**:

1. **默认读取前 200 行**:

```json
{
  "tool": "read_file",
  "arguments": {
    "path": "/path/to/file.txt"
  }
}
```

2. **读取前 50 行**:

```json
{
  "tool": "read_file",
  "arguments": {
    "path": "/path/to/file.txt",
    "max_lines": 50
  }
}
```

3. **读取全部内容**:

```json
{
  "tool": "read_file",
  "arguments": {
    "path": "/path/to/file.txt",
    "max_lines": null
  }
}
```

4. **读取指定行范围**:

```json
{
  "tool": "read_file",
  "arguments": {
    "path": "/path/to/file.txt",
    "start_line": 10,
    "end_line": 20
  }
}
```

5. **从指定行开始读取指定数量的行**:

```json
{
  "tool": "read_file",
  "arguments": {
    "path": "/path/to/file.txt",
    "start_line": 100,
    "max_lines": 10
  }
}
```

**返回结果**:

```json
{
  "path": "/path/to/file.txt",
  "content": "file content...",
  "size": 1024,
  "encoding": "utf-8",
  "total_lines": 500,
  "displayed_lines": 200,
  "is_truncated": true,
  "line_range": [1, 200]
}
```

**字段说明**:

- `content`: 文件内容
- `size`: 文件大小（字节）
- `encoding`: 编码格式
- `total_lines`: 文件总行数
- `displayed_lines`: 显示的行数
- `is_truncated`: 是否被截断
- `line_range`: 显示的行范围（1-based），null 表示完整文件

#### search_content 工具详细说明

`search_content` 工具提供了强大的文件内容搜索功能，类似于 Unix 的 `grep` 命令。

**主要特性**:

- 字符串字面量搜索和正则表达式搜索
- 大小写敏感/不敏感搜索
- 递归目录搜索
- 文件类型过滤 (include/exclude patterns)
- 文件大小限制
- 上下文行数显示
- 结果数量限制
- 权限安全检查

**参数**:

- `query` (必需): 搜索查询 (文本或正则表达式)
- `directory` (必需): 搜索目录
- `recursive` (可选): 递归搜索子目录 (默认: true)
- `case_sensitive` (可选): 区分大小写 (默认: false)
- `use_regex` (可选): 使用正则表达式 (默认: false)
- `include_patterns` (可选): 包含的文件模式 (如 ["*.js", "*.ts"])
- `exclude_patterns` (可选): 排除的文件模式 (如 ["*.log", "node_modules"])
- `max_file_size` (可选): 最大文件大小 (字节)
- `context_lines` (可选): 显示上下文行数
- `max_results` (可选): 最大结果数量

**使用示例**:

1. **基本文本搜索**:

```json
{
  "tool": "search_content",
  "arguments": {
    "query": "hello world",
    "directory": "/path/to/search",
    "case_sensitive": false
  }
}
```

2. **正则表达式搜索**:

```json
{
  "tool": "search_content",
  "arguments": {
    "query": "function\\s+\\w+",
    "directory": "/path/to/search",
    "use_regex": true,
    "include_patterns": ["*.js", "*.ts"]
  }
}
```

3. **带上下文的搜索**:

```json
{
  "tool": "search_content",
  "arguments": {
    "query": "TODO",
    "directory": "/path/to/search",
    "context_lines": 2,
    "exclude_patterns": ["*.log", "node_modules"]
  }
}
```

**返回结果**:

```json
{
  "query": "hello world",
  "total_matches": 3,
  "files_searched": 10,
  "files_with_matches": 3,
  "search_time_ms": 5,
  "matches": [
    {
      "file_path": "/path/to/file.js",
      "total_matches": 1,
      "line_matches": [
        {
          "line_number": 15,
          "content": "console.log('hello world');",
          "start_index": 13,
          "end_index": 24,
          "context_before": ["// Start of function", "function test() {"],
          "context_after": ["  return true;", "}"]
        }
      ]
    }
  ]
}
```

### 2. example-tool

**功能**: 示例工具，展示内置工具的基本结构

**工具**:

- `echo` - 回显消息
- `get_info` - 获取工具信息

**参数**:

- `--log-level`: 日志级别 (默认: error)
- `--message`: 自定义消息内容

## 添加新工具

1. **创建新的 Rust 项目**

   ```bash
   cd tools
   cargo new my-new-tool --bin
   ```

2. **更新 Cargo.toml**

   ```toml
   [workspace]
   members = [
       "filesystem-mcp",
       "example-tool",
       "my-new-tool",  # 添加新工具
   ]
   ```

3. **实现 MCP 协议**

   - 参考 `example-tool` 的实现
   - 使用 `mcp.rs` 模块处理 MCP 消息
   - 在 `tool_handler.rs` 中实现具体的工具逻辑

4. **添加到数据库配置**

   在 `clients/node/db/migrate/migrateProviderDB.ts` 中添加新工具的配置：

   ```typescript
   {
     serverName: 'my-new-tool',
     serverConfig: JSON.stringify({
       serverType: 'stdio',
       cmd: getExecutablePath('my-new-tool'),
       args: ['--log-level', 'error'],
       env: {}
     }),
     serverDesc: 'Description of my new tool',
     serverStatus: 0,
     serverFromObj: '',
     serverLogo: '',
     serverTools: 'tool1,tool2,tool3', // 逗号分隔的工具列表
     // 对于 filesystem-mcp 工具，完整的工具列表为：
     // 'list_directory,read_file,write_file,create_directory,delete,get_file_info,search_files,list_allowed_directories,search_content'
     isBuiltin: 1
   }
   ```

5. **构建和测试**
   ```bash
   node scripts/build-builtin-tools.js
   ```

## 故障排除

### 构建失败

1. **检查 Rust 安装**

   ```bash
   rustc --version
   cargo --version
   ```

2. **清理构建缓存**

   ```bash
   cd tools
   cargo clean
   ```

3. **更新依赖**
   ```bash
   cargo update
   ```

### 可执行文件权限问题

在 Unix 系统上，如果工具无法执行：

```bash
# 添加执行权限
chmod +x external/tool-name
```

### Windows 路径问题

在 Windows 上，确保路径使用正确的分隔符：

```javascript
// 使用 path.join() 而不是手动拼接路径
const toolPath = path.join(externalDir, isWin ? 'tool.exe' : 'tool');
```

## 性能测试结果

### search_content 工具性能

基于实际测试，`search_content` 工具的性能表现如下：

**测试环境**:

- 搜索 4-5 个小文件 (< 1KB)
- 包含 JavaScript、Python、文本文件
- 在 macOS 系统上测试

**测试结果**:

- **基本文本搜索**: 2-4ms
- **正则表达式搜索**: 3-5ms
- **带上下文的搜索**: 4-6ms
- **文件过滤搜索**: 2-3ms

**性能优化建议**:

1. 使用 `include_patterns` 限制搜索的文件类型
2. 设置合理的 `max_file_size` 限制
3. 使用 `max_results` 限制返回结果数量
4. 避免过于复杂的正则表达式
5. 对于大型项目，考虑分批搜索

### read_file 工具性能

基于实际测试，`read_file` 工具的性能表现如下：

**测试环境**:

- 测试文件：20 行文本文件 (679 字节)
- 在 macOS 系统上测试

**测试结果**:

- **默认读取 (200 行限制)**: < 1ms
- **限制读取 (5 行)**: < 1ms
- **指定行范围读取**: < 1ms
- **读取全部内容**: < 1ms

**性能优化建议**:

1. 对于大文件，使用 `max_lines` 限制读取行数
2. 使用 `start_line` 和 `end_line` 精确定位需要的内容
3. 避免频繁读取大文件的全部内容
4. 对于超大文件 (>10MB)，建议分段读取

## 注意事项

1. **安全性**: 内置工具会在数据库初始化时自动添加，不应该被用户手动删除
2. **性能**: 工具使用 release 模式构建，确保最佳性能
3. **日志**: 默认使用 error 级别日志，减少输出噪音
4. **路径**: 使用绝对路径避免相对路径问题
5. **权限**: 确保工具有适当的文件系统访问权限
6. **搜索安全**: `search_content` 工具严格遵守 `--allowed-directories` 限制，防止未授权访问
7. **正则表达式**: 使用正则表达式搜索时，避免复杂的表达式导致性能问题或安全风险
8. **文件大小限制**: 建议设置合理的 `max_file_size` 限制，避免处理过大的文件影响性能
9. **搜索结果限制**: 使用 `max_results` 参数限制搜索结果数量，避免返回过多数据
10. **文件读取优化**: `read_file` 工具默认只读取前 200 行，避免大文件造成的性能问题
11. **行号范围**: 使用 `read_file` 时，行号是 1-based（从 1 开始），请确保指定的行号在文件范围内
12. **内存使用**: 对于大文件，建议使用行数限制而不是读取全部内容

## 开发建议

1. **使用模板**: 基于 `example-tool` 创建新工具
2. **错误处理**: 实现完善的错误处理和日志记录
3. **参数验证**: 验证所有输入参数的有效性
4. **测试**: 编写单元测试和集成测试
5. **文档**: 为每个工具编写详细的使用文档
6. **性能优化**: 对于搜索功能，考虑使用并行处理和缓存机制
7. **安全检查**: 对用户输入进行严格验证，特别是正则表达式和文件路径
8. **用户体验**: 提供清晰的错误信息和进度反馈，特别是长时间运行的搜索操作
9. **文件读取策略**: 对于大文件，优先使用行数限制而不是一次性读取全部内容
10. **行号验证**: 在处理行号参数时，确保进行边界检查和合理性验证
11. **内存管理**: 对于超大文件，考虑实现流式读取而不是一次性加载到内存
