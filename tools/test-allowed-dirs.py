#!/usr/bin/env python3
"""
Test script for the allowed-directories functionality
"""

import json
import subprocess
import sys
import tempfile
import os

def send_mcp_request(process, request):
    """Send a JSON-RPC request to the MCP server and get response"""
    request_json = json.dumps(request) + '\n'
    process.stdin.write(request_json.encode())
    process.stdin.flush()
    
    response_line = process.stdout.readline().decode().strip()
    if response_line:
        return json.loads(response_line)
    return None

def test_startup_validation():
    """Test startup validation with different scenarios"""
    mcp_binary = './target/release/filesystem-mcp'
    
    print("🧪 Testing startup validation...")
    
    # Test 1: No allowed directories (should fail)
    print("\n📋 Test 1: Startup without allowed directories (should fail)")
    try:
        result = subprocess.run(
            [mcp_binary, '--log-level', 'error'],
            input='{"jsonrpc": "2.0", "id": 1, "method": "initialize"}',
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode != 0:
            print("✅ Correctly failed startup without allowed directories")
            print(f"Error: {result.stderr.strip()}")
        else:
            print("❌ Should have failed but didn't")
    except subprocess.TimeoutExpired:
        print("❌ Process hung - this shouldn't happen")
    
    # Test 2: Non-existent directory (should fail)
    print("\n📋 Test 2: Startup with non-existent directory (should fail)")
    try:
        result = subprocess.run(
            [mcp_binary, '--log-level', 'error', '--allowed-directories', '/this/does/not/exist'],
            input='{"jsonrpc": "2.0", "id": 1, "method": "initialize"}',
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode != 0:
            print("✅ Correctly failed with non-existent directory")
            print(f"Error: {result.stderr.strip()}")
        else:
            print("❌ Should have failed but didn't")
    except subprocess.TimeoutExpired:
        print("❌ Process hung - this shouldn't happen")
    
    # Test 3: Valid directory (should succeed)
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"\n📋 Test 3: Startup with valid directory (should succeed): {temp_dir}")
        try:
            process = subprocess.Popen(
                [mcp_binary, '--log-level', 'error', '--allowed-directories', temp_dir],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd='/Users/<USER>/Documents/source-code/inc-bdp-usb-mcp-web/tools'
            )
            
            # Test initialization
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {}
            }
            response = send_mcp_request(process, init_request)
            if response and response.get('result'):
                print("✅ Successfully started with valid directory")
            else:
                print("❌ Failed to initialize with valid directory")
                print(f"Response: {response}")
            
            # Test the new tool
            print("\n📋 Test 4: Testing list_allowed_directories tool")
            list_dirs_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "list_allowed_directories",
                    "arguments": {}
                }
            }
            response = send_mcp_request(process, list_dirs_request)
            if response and 'result' in response:
                print("✅ list_allowed_directories tool works")
                result_content = json.loads(response['result']['content'][0]['text'])
                print(f"Allowed directories: {result_content['total_count']}")
                print(f"Directory info: {result_content['allowed_directories'][0]['path']}")
            else:
                print("❌ list_allowed_directories tool failed")
                print(f"Response: {response}")
            
            # Test access restriction
            print("\n📋 Test 5: Testing access restriction")
            # Try to list a directory outside allowed paths
            list_root_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "list_directory",
                    "arguments": {
                        "path": "/",
                        "recursive": False
                    }
                }
            }
            response = send_mcp_request(process, list_root_request)
            if response and 'error' in response:
                print("✅ Correctly blocked access to restricted directory")
                print(f"Error message: {response['error']['message']}")
            elif response and 'result' in response:
                print("❌ Should have blocked access to root directory")
                print(f"Response: {response}")
            else:
                print("❌ Unexpected response format")
                print(f"Response: {response}")
            
            # Test allowed directory access
            print("\n📋 Test 6: Testing allowed directory access")
            list_allowed_request = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": "list_directory",
                    "arguments": {
                        "path": temp_dir,
                        "recursive": False
                    }
                }
            }
            response = send_mcp_request(process, list_allowed_request)
            if response and 'result' in response:
                print("✅ Successfully accessed allowed directory")
            else:
                print("❌ Failed to access allowed directory")
                print(f"Response: {response}")
            
            process.terminate()
            process.wait(timeout=5)
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()

def test_multiple_directories():
    """Test with multiple allowed directories"""
    print("\n🧪 Testing multiple allowed directories...")
    
    mcp_binary = './target/release/filesystem-mcp'
    
    with tempfile.TemporaryDirectory() as temp_dir1:
        with tempfile.TemporaryDirectory() as temp_dir2:
            print(f"Testing with directories: {temp_dir1}, {temp_dir2}")
            
            try:
                process = subprocess.Popen(
                    [mcp_binary, '--log-level', 'error', '--allowed-directories', f'{temp_dir1},{temp_dir2}'],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd='/Users/<USER>/Documents/source-code/inc-bdp-usb-mcp-web/tools'
                )
                
                # Initialize
                init_request = {"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {}}
                response = send_mcp_request(process, init_request)
                
                if response and response.get('result'):
                    print("✅ Successfully started with multiple directories")
                    
                    # Test list_allowed_directories
                    list_dirs_request = {
                        "jsonrpc": "2.0",
                        "id": 2,
                        "method": "tools/call",
                        "params": {"name": "list_allowed_directories", "arguments": {}}
                    }
                    response = send_mcp_request(process, list_dirs_request)
                    if response and 'result' in response:
                        result_content = json.loads(response['result']['content'][0]['text'])
                        if result_content['total_count'] == 2:
                            print("✅ Both directories are listed in allowed directories")
                        else:
                            print(f"❌ Expected 2 directories, got {result_content['total_count']}")
                    
                process.terminate()
                process.wait(timeout=5)
                
            except Exception as e:
                print(f"❌ Error: {e}")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    process.kill()

if __name__ == "__main__":
    test_startup_validation()
    test_multiple_directories()
    print("\n🎉 Testing completed!")