// SharedWorker 定时器
let timerId: NodeJS.Timeout | null = null;
const connectedPorts: MessagePort[] = [];

// 启动定时器
function startTimer() {
  if (timerId) {
    clearInterval(timerId);
  }

  timerId = setInterval(() => {
    const message = {
      type: 'TIMER_TICK',
      timestamp: Date.now(),
      message: '定时器触发'
    };

    // 向所有连接的端口发送消息
    connectedPorts.forEach(port => {
      try {
        port.postMessage(message);
      } catch (error) {
        console.error('发送消息失败:', error);
      }
    });
  }, 5000); // 10秒
}

// 停止定时器
function stopTimer() {
  if (timerId) {
    clearInterval(timerId);
    timerId = null;
  }
}

// 向所有连接的端口广播消息
function broadcastMessage(message: any) {
  connectedPorts.forEach(port => {
    try {
      port.postMessage(message);
    } catch (error) {
      console.error('广播消息失败:', error);
    }
  });
}

// 处理新连接
self.addEventListener('connect', (event) => {
  const port = (event as MessageEvent).ports[0];
  connectedPorts.push(port);

  console.log('新的连接建立，当前连接数:', connectedPorts.length);

  // 发送就绪消息
  port.postMessage({
    type: 'WORKER_READY',
    timestamp: Date.now(),
    message: 'SharedWorker 已准备就绪'
  });

  startTimer();

  // 监听来自主线程的消息
  port.onmessage = (event) => {
    const { type, data } = event.data;

    switch (type) {
      case 'START_TIMER':
        startTimer();
        broadcastMessage({
          type: 'TIMER_STARTED',
          timestamp: Date.now(),
          message: '定时器已启动'
        });
        break;

      case 'STOP_TIMER':
        stopTimer();
        broadcastMessage({
          type: 'TIMER_STOPPED',
          timestamp: Date.now(),
          message: '定时器已停止'
        });
        break;

      default:
        console.warn('Unknown message type:', type);
    }
  };

  // 处理错误
  port.onmessageerror = (error) => {
    console.error('消息错误:', error);
  };

  // 启动定时器
  port.start();
});

// 初始化时启动定时器

