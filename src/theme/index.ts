const less = require('less');
const dark = require('antd/dist/dark-theme');
// const aliyun = require('@ant-design/aliyun-theme');
const sfTheme = require('../../config/theme');
const themeVars = require('./vars.json');

const defaultTheme = {};
const darkTheme = {};

Object.keys(dark).forEach((key) => {
  darkTheme[`@${key}`] = dark[key];
});

themeVars.forEach((group) => {
  group.children.forEach((item) => {
    let { value } = item;
    if (item.type === 'number') {
      value += item.unit;
    }
    defaultTheme[item.name] = value;
  });
});
const themes = {
  default: defaultTheme,
  dark: darkTheme,
  // aliyun,
  sfTheme,
};
const themeVariables = themeVars.reduce((pre, cur) => {
  const childThemeVar = cur.children.reduce((cpre, ccur) => cpre.concat(ccur.name), []);
  return pre.concat(childThemeVar);
}, []);
const changeLess = ({ theme, onSuccess, onError }) => {
  const getThemeVars = (themeName) => {
    const vars = {};
    // const cacheTheme = JSON.parse(localStorage.getItem(themeName));
    const theme = {
      ...themes[themeName],
      // ...cacheTheme,
    };

    themeVars.forEach((group) => {
      group.children.forEach((item) => {
        let { value } = item;
        if (item.name in theme) {
          // 在theme的值结尾都带单位
          value = theme[item.name];
        } else if (item.type === 'number') {
          value = `${value}${item.unit}`;
        }

        vars[item.name] = {
          ...item,
          value,
        };
      });
    });

    return vars;
  };
  const extractTheme = (vars) => {
    const theme = {};
    Object.keys(vars).forEach((key) => {
      theme[key] = vars[key].value;
    });

    return theme;
  };
  if (!less || !less.modifyVars) {
    return;
  }
  const vars = getThemeVars(theme);
  less
    .modifyVars(extractTheme(vars))
    .then((res) => {
      // console.log(res);
      onSuccess?.();
    })
    .catch((err) => {
      console.error(err.message);
      onError?.();
    });
};

module.exports.themeVars = themeVariables;
module.exports.themes = themes;
module.exports.changeLess = changeLess;
