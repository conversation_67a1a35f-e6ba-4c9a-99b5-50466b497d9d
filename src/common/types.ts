export interface Provider {
  id: number;
  provider: string;
  name: string;
  apiBaseUrl: string;
  defaultBaseModelUrl: string;
  defaultApiKey: string;
  status: number;
  currentAPIKey: string;
  createdAt: string;
}

export interface Model {
  id: number;
  provider: string;
  model: string;
  createdAt: string;
}

export interface Server {
  id: number;
  serverName: string;
  serverConfig: string;
  serverDesc: string;
  serverStatus: 0 | 1;
  serverLogo: string;
  serverFromObj: string;
  serverTools: string;
  isBuiltin: number;
  loading?: boolean;
}

export interface ServerConfig {
  serverType: 'stdio' | 'sse' | 'streamable_http';
}

export interface StdioServerConfig extends ServerConfig {
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

export interface SseServerConfig extends ServerConfig {
  serverUrl: string;
  requestHeaders?: Record<string, string>;
}

export interface StreamableHttpServerConfig extends ServerConfig {
  serverUrl: string;
  requestHeaders?: Record<string, string>;
}
