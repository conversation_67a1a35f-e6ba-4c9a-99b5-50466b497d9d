import { validateSimplifiedMcpData, convertSimplifiedMcpToDb, sanitizeDbMcpData } from './mcpValidator';

/**
 * 支持UTF-8的base64解码函数
 * @param str base64编码的字符串
 * @returns 解码后的UTF-8字符串
 */
const base64DecodeUTF8 = (str: string): string => {
  try {
    // 先用atob解码base64
    const binaryString = atob(str);
    // 将二进制字符串转换为字节数组
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    // 使用TextDecoder将字节数组解码为UTF-8字符串
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(bytes);
  } catch (error) {
    // 如果UTF-8解码失败，回退到普通的atob
    console.warn('UTF-8解码失败，回退到atob:', error);
    return atob(str);
  }
};

/**
 * 解析MCP协议URL并返回可直接保存的数据
 * @param url MCP协议URL
 * @returns 解析结果，包含可直接用于保存的数据库格式数据
 */
export const parseMcpUrl = (url: string) => {
  try {
    const urlObj = new URL(url);

    // 检查是否是MCP协议
    if (urlObj.hostname !== 'mcp') {
      throw new Error('不是有效的MCP协议URL');
    }

    const encodedData = urlObj.searchParams.get('data');
    if (!encodedData) {
      throw new Error('URL中缺少data参数');
    }

    // 第一次base64解码（支持UTF-8）
    const firstDecoded = base64DecodeUTF8(encodedData);
    console.log('第一次解码结果:', firstDecoded);

    // 第二次base64解码（支持UTF-8）
    const secondDecoded = base64DecodeUTF8(firstDecoded);
    console.log('第二次解码结果:', secondDecoded);

    // 解析JSON
    let rawMcpData = JSON.parse(secondDecoded);
    console.log('解析的MCP数据:', rawMcpData);

    // 验证简化MCP数据
    const validationResult = validateSimplifiedMcpData(rawMcpData);
    if (!validationResult.valid) {
      throw new Error(`MCP数据验证失败: ${validationResult.errors.join(', ')}`);
    }

    // 输出警告信息（如果有）
    if (validationResult.warnings.length > 0) {
      console.warn('MCP数据验证警告:', validationResult.warnings.join(', '));
    }

    if (rawMcpData.mcpServers) {
      rawMcpData = rawMcpData.mcpServers
    }
    // 转换为数据库格式
    const dbData = convertSimplifiedMcpToDb(rawMcpData);

    // 清理和标准化数据
    const sanitizedData = sanitizeDbMcpData(dbData);

    console.log('转换后的数据库格式数据:', sanitizedData);

    return {
      success: true,
      data: sanitizedData
    };

  } catch (error) {
    console.error('解析MCP URL失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
};
