/**
 * MCP协议字段验证模块 - 前端版本
 * 用于验证从协议URL解析的MCP数据是否符合规范
 */

/**
 * MCP服务器类型枚举
 */
export const MCP_SERVER_TYPES = {
  STDIO: 'stdio',
  SSE: 'sse',
  STREAMABLE_HTTP: 'streamable_http'
} as const;

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 简化MCP协议数据接口 - 一次只添加一个服务器
 */
export interface SimplifiedMcpData {
  [serverName: string]: {
    command: string;
    args?: string[];
    env?: Record<string, string>;
    serverLogo?: string;
    serverDesc?: string;
    mcpId?: string;
    source?: string;
  } | {
    url: string;
    type?: 'sse' | 'streamable_http';
    headers?: Record<string, string>;
    serverLogo?: string;
    serverDesc?: string;
    mcpId?: string;
    source?: string;
  };
}

/**
 * 数据库MCP表数据接口
 */
export interface DbMcpData {
  serverName: string;
  serverDesc?: string;
  serverConfig: string;
  serverLogo?: string;
  serverFromObj: string;
  isBuiltin: number;
  serverTools?: string;
}

/**
 * 将简化MCP协议数据转换为数据库格式
 * @param simplifiedData - 简化MCP协议数据
 * @returns 数据库格式的MCP数据
 */
export function convertSimplifiedMcpToDb(simplifiedData: SimplifiedMcpData): DbMcpData {
  // 获取服务器名称和配置（应该只有一个）
  const serverEntries = Object.entries(simplifiedData);
  if (serverEntries.length !== 1) {
    throw new Error(`简化格式应该只包含一个服务器，但收到了 ${serverEntries.length} 个`);
  }

  const [serverName, serverConfig] = serverEntries[0];

  // 确定服务器类型和配置
  let dbServerConfig: any;

  if ('command' in serverConfig) {
    // stdio类型
    dbServerConfig = {
      serverType: 'stdio',
      cmd: serverConfig.command,
      args: serverConfig.args || [],
      env: serverConfig.env || {},
      mcpId: serverConfig.mcpId || {},
      source: serverConfig.source || undefined,
      // serverDesc: serverConfig.serverDesc,
      // serverLogo: serverConfig.serverLogo,
    };
  } else if ('url' in serverConfig) {
    // 根据URL判断是SSE还是HTTP类型
    const serverType = serverConfig.type ? serverConfig.type : serverConfig.url.includes('/sse') ? 'sse' : 'streamable_http';
    dbServerConfig = {
      serverType,
      serverUrl: serverConfig.url,
      requestHeaders: serverConfig.headers || {},
      mcpId: serverConfig.mcpId || {},
      source: serverConfig.source || undefined
      // serverDesc: serverConfig.serverDesc,
      // serverLogo: serverConfig.serverLogo,
    };
  } else {
    throw new Error(`无法识别的服务器配置类型: ${serverName}`);
  }

  // 构建数据库格式的数据
  const dbData: DbMcpData = {
    serverName: serverName,
    serverDesc: serverConfig.serverDesc || `通过协议添加的MCP:${serverName}`,
    serverConfig: JSON.stringify(dbServerConfig),
    serverLogo: serverConfig.serverLogo || '',
    serverFromObj: '',
    isBuiltin: 0,
    serverTools: ''
  };

  return dbData;
}

/**
 * 验证简化MCP协议数据
 * @param data - 要验证的简化MCP数据
 * @returns 验证结果
 */
export function validateSimplifiedMcpData(data: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 基本类型检查
  if (!data || typeof data !== 'object') {
    errors.push('MCP数据必须是一个对象');
    return { valid: false, errors, warnings };
  }

  if (data.mcpServers) {
    data = data.mcpServers;
  }

  // 验证应该只有一个服务器
  const serverKeys = Object.keys(data);
  if (serverKeys.length === 0) {
    errors.push('MCP数据不能为空');
    return { valid: false, errors, warnings };
  }

  if (serverKeys.length > 1) {
    errors.push(`简化格式应该只包含一个服务器，但收到了 ${serverKeys.length} 个`);
    return { valid: false, errors, warnings };
  }

  // 验证服务器配置
  const [serverKey, serverConfig] = Object.entries(data)[0];

  if (!serverConfig || typeof serverConfig !== 'object') {
    errors.push(`服务器 ${serverKey} 的配置必须是对象`);
    return { valid: false, errors, warnings };
  }

  const config = serverConfig as any;

  if ('command' in config) {
    // stdio类型验证
    if (!config.command || typeof config.command !== 'string') {
      errors.push(`服务器 ${serverKey} 的command字段必须是非空字符串`);
    }

    if (config.args && !Array.isArray(config.args)) {
      errors.push(`服务器 ${serverKey} 的args字段必须是数组`);
    }

    if (config.env && typeof config.env !== 'object') {
      errors.push(`服务器 ${serverKey} 的env字段必须是对象`);
    }
  } else if ('url' in config) {
    // URL类型验证
    if (!config.url || typeof config.url !== 'string') {
      errors.push(`服务器 ${serverKey} 的url字段必须是非空字符串`);
    } else {
      try {
        new URL(config.url);
      } catch (error) {
        errors.push(`服务器 ${serverKey} 的url字段不是有效的URL`);
      }
    }

    if (config.headers && typeof config.headers !== 'object') {
      errors.push(`服务器 ${serverKey} 的headers字段必须是对象`);
    }
  } else {
    errors.push(`服务器 ${serverKey} 必须包含command字段或url字段`);
  }

  // 验证服务器名称
  if (!/^[a-zA-Z0-9\-_\u4e00-\u9fa5\s]+$/.test(serverKey)) {
    warnings.push('服务器名称包含特殊字符，可能导致兼容性问题');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证数据库格式的MCP数据
 * @param data - 要验证的数据库格式MCP数据
 * @returns 验证结果
 */
export function validateDbMcpData(data: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 基本类型检查
  if (!data || typeof data !== 'object') {
    errors.push('MCP数据必须是一个对象');
    return { valid: false, errors, warnings };
  }

  // 验证必需字段
  const requiredFields = ['serverName', 'serverConfig'];
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`缺少必需字段: ${field}`);
    }
  }

  // 验证serverName
  if (data.serverName) {
    if (typeof data.serverName !== 'string') {
      errors.push('serverName必须是字符串');
    } else if (data.serverName.length === 0) {
      errors.push('serverName不能为空');
    } else if (data.serverName.length > 100) {
      warnings.push('serverName过长，建议不超过100个字符');
    }

    // 检查服务器名称是否包含非法字符
    if (!/^[a-zA-Z0-9\-_\u4e00-\u9fa5\s]+$/.test(data.serverName)) {
      warnings.push('serverName包含特殊字符，可能导致兼容性问题');
    }
  }

  // 验证serverConfig
  if (data.serverConfig) {
    let config: any;
    try {
      // 如果是字符串，尝试解析为JSON
      if (typeof data.serverConfig === 'string') {
        config = JSON.parse(data.serverConfig);
      } else if (typeof data.serverConfig === 'object') {
        config = data.serverConfig;
      } else {
        errors.push('serverConfig必须是JSON字符串或对象');
      }

      if (config) {
        const configValidation = validateServerConfig(config);
        errors.push(...configValidation.errors);
        warnings.push(...configValidation.warnings);
      }
    } catch (error) {
      errors.push(`serverConfig不是有效的JSON: ${(error as Error).message}`);
    }
  }

  // 验证可选字段
  if (data.serverDesc && typeof data.serverDesc !== 'string') {
    warnings.push('serverDesc应该是字符串');
  }

  if (data.serverLogo && typeof data.serverLogo !== 'string') {
    warnings.push('serverLogo应该是字符串');
  }

  if (data.serverTools) {
    if (typeof data.serverTools !== 'string') {
      warnings.push('serverTools应该是字符串');
    } else {
      // 验证工具列表格式
      const tools = data.serverTools.split(',').filter((tool: string) => tool.trim());
      if (tools.length === 0) {
        warnings.push('serverTools为空');
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证服务器配置
 * @param config - 服务器配置对象
 * @returns 验证结果
 */
function validateServerConfig(config: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config || typeof config !== 'object') {
    errors.push('服务器配置必须是对象');
    return { errors, warnings, valid: false };
  }

  // 验证serverType
  if (config.serverType) {
    if (!Object.values(MCP_SERVER_TYPES).includes(config.serverType)) {
      errors.push(`不支持的服务器类型: ${config.serverType}`);
    }
  } else {
    warnings.push('未指定serverType，将使用默认值');
  }

  // 根据服务器类型验证特定字段
  switch (config.serverType) {
    case MCP_SERVER_TYPES.STDIO:
      validateStdioConfig(config, errors, warnings);
      break;
    case MCP_SERVER_TYPES.SSE:
      validateSseConfig(config, errors, warnings);
      break;
    case MCP_SERVER_TYPES.STREAMABLE_HTTP:
      validateHttpConfig(config, errors, warnings);
      break;
  }

  // 验证环境变量
  if (config.env && typeof config.env !== 'object') {
    errors.push('env必须是对象');
  }

  return { errors, warnings, valid: errors.length === 0 };
}

/**
 * 验证stdio类型配置
 */
function validateStdioConfig(config: any, errors: string[], warnings: string[]): void {
  if (!config.cmd) {
    errors.push('stdio类型服务器必须指定cmd字段');
  } else if (typeof config.cmd !== 'string') {
    errors.push('cmd必须是字符串');
  }

  if (config.args && !Array.isArray(config.args)) {
    errors.push('args必须是数组');
  }
}

/**
 * 验证SSE类型配置
 */
function validateSseConfig(config: any, errors: string[], warnings: string[]): void {
  if (!config.serverUrl) {
    errors.push('SSE类型服务器必须指定serverUrl字段');
  } else if (typeof config.serverUrl !== 'string') {
    errors.push('serverUrl必须是字符串');
  } else {
    try {
      new URL(config.serverUrl);
    } catch (error) {
      errors.push(`serverUrl不是有效的URL: ${(error as Error).message}`);
    }
  }

  if (config.requestHeaders && typeof config.requestHeaders !== 'object') {
    errors.push('requestHeaders必须是对象');
  }
}

/**
 * 验证HTTP类型配置
 */
function validateHttpConfig(config: any, errors: string[], warnings: string[]): void {
  if (!config.serverUrl) {
    errors.push('HTTP类型服务器必须指定serverUrl字段');
  } else if (typeof config.serverUrl !== 'string') {
    errors.push('serverUrl必须是字符串');
  } else {
    try {
      new URL(config.serverUrl);
    } catch (error) {
      errors.push(`serverUrl不是有效的URL: ${(error as Error).message}`);
    }
  }

  if (config.requestHeaders && typeof config.requestHeaders !== 'object') {
    errors.push('requestHeaders必须是对象');
  }
}

/**
 * 清理和标准化数据库格式的MCP数据
 * @param data - 原始MCP数据
 * @returns 清理后的数据
 */
export function sanitizeDbMcpData(data: any): DbMcpData {
  const sanitized: DbMcpData = {
    serverName: '',
    serverDesc: '通过协议添加的MCP服务器',
    serverConfig: '',
    serverLogo: '',
    serverFromObj: '',
    isBuiltin: 0,
    serverTools: ''
  };

  // 清理serverName
  if (data.serverName) {
    sanitized.serverName = String(data.serverName).trim();
  }

  // 清理serverDesc
  if (data.serverDesc) {
    sanitized.serverDesc = String(data.serverDesc).trim();
  }

  // 处理serverConfig
  if (data.serverConfig) {
    if (typeof data.serverConfig === 'string') {
      sanitized.serverConfig = data.serverConfig;
    } else {
      sanitized.serverConfig = JSON.stringify(data.serverConfig);
    }
  }

  // 清理其他字段
  if (data.serverLogo) {
    sanitized.serverLogo = String(data.serverLogo).trim();
  }

  if (data.serverTools) {
    sanitized.serverTools = String(data.serverTools).trim();
  }

  // 保存原始数据
  sanitized.serverFromObj = sanitized.serverConfig

  return sanitized;
}

/**
 * 生成MCP数据摘要（用于日志和调试）
 * @param data - MCP数据
 * @returns 数据摘要
 */
export function generateMcpDataSummary(data: any): string {
  let serverName = 'Unknown';
  let serverType = 'Unknown';
  let hasConfig = false;

  try {
    if (data.serverConfig) {
      // 数据库格式
      serverName = data.serverName || 'Unknown';
      hasConfig = true;
      const config = typeof data.serverConfig === 'string'
        ? JSON.parse(data.serverConfig)
        : data.serverConfig;

      if (config && config.serverType) {
        serverType = config.serverType;
      }
    } else {
      // 简化格式 - 直接是 { serverName: config } 的形式
      const serverKeys = Object.keys(data);
      if (serverKeys.length > 0) {
        serverName = serverKeys[0];
        hasConfig = true;
        const serverConfig = data[serverName];
        if ('command' in serverConfig) {
          serverType = 'stdio';
        } else if ('url' in serverConfig) {
          serverType = 'sse/http';
        }
      }
    }
  } catch (error) {
    // 忽略解析错误
  }

  return `MCP服务器: ${serverName} (类型: ${serverType}, 配置: ${hasConfig ? '有' : '无'})`;
}
