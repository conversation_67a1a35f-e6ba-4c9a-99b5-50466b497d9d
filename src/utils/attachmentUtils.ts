import { AttachmentInfo } from '@/services/types';

/**
 * 解析消息中的附件信息
 * @param attachmentsJson JSON字符串格式的附件信息
 * @returns 解析后的附件数组
 */
export const parseMessageAttachments = (attachmentsJson?: string): AttachmentInfo[] => {
  if (!attachmentsJson || attachmentsJson.trim() === '') {
    return [];
  }

  try {
    const parsed = JSON.parse(attachmentsJson);

    if (Array.isArray(parsed)) {
      return parsed.filter(item =>
        item &&
        typeof item === 'object' &&
        item.id &&
        item.originalName &&
        item.fileName
      );
    }
    return [];
  } catch (error) {
    console.warn('解析消息附件信息失败:', error, { attachmentsJson });
    return [];
  }
};

/**
 * 检查消息是否包含附件
 * @param message 消息对象
 * @returns 是否包含附件
 */
export const hasAttachments = (message: { attachments?: string }): boolean => {
  const attachments = parseMessageAttachments(message.attachments);
  return attachments.length > 0;
};

/**
 * 获取消息的附件数量
 * @param message 消息对象
 * @returns 附件数量
 */
export const getAttachmentCount = (message: { attachments?: string }): number => {
  const attachments = parseMessageAttachments(message.attachments);
  return attachments.length;
};

/**
 * 按文件类型分组附件
 * @param attachments 附件数组
 * @returns 按类型分组的附件
 */
export const groupAttachmentsByType = (attachments: AttachmentInfo[]) => {
  const groups = {
    images: [] as AttachmentInfo[],
    documents: [] as AttachmentInfo[],
    others: [] as AttachmentInfo[]
  };

  attachments.forEach(attachment => {
    const ext = attachment.extension.toLowerCase();
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(ext)) {
      groups.images.push(attachment);
    } else if (['.doc', '.docx', '.xls', '.xlsx', '.pdf', '.txt', '.md'].includes(ext)) {
      groups.documents.push(attachment);
    } else {
      groups.others.push(attachment);
    }
  });

  return groups;
};

/**
 * 获取附件的总大小
 * @param attachments 附件数组
 * @returns 总大小（字节）
 */
export const getTotalAttachmentSize = (attachments: AttachmentInfo[]): number => {
  return attachments.reduce((total, attachment) => total + (attachment.size || 0), 0);
};
