/**
 * 判断元素是否可见的工具函数
 */

/**
 * 基础可见性检查 - 检查元素是否在DOM中且样式上可见
 * @param element - 要检查的DOM元素
 * @returns 是否可见
 */
export function isElementVisible(element: HTMLElement | null): boolean {
  if (!element) return false;

  const style = window.getComputedStyle(element);

  // 检查display、visibility、opacity
  if (style.display === 'none') return false;
  if (style.visibility === 'hidden') return false;
  if (parseFloat(style.opacity) === 0) return false;

  // 检查元素尺寸
  const rect = element.getBoundingClientRect();
  if (rect.width === 0 && rect.height === 0) return false;

  return true;
}

/**
 * 视口可见性检查 - 检查元素是否在当前视口范围内可见
 * @param element - 要检查的DOM元素
 * @param threshold - 可见比例阈值 (0-1)，默认0表示只要有一点在视口内就算可见
 * @returns 是否在视口内可见
 */
export function isElementInViewport(element: HTMLElement | null, threshold: number = 0): boolean {
  if (!isElementVisible(element)) return false;

  const rect = element!.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;

  // 计算元素与视口的交集
  const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
  const visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0);

  if (visibleHeight <= 0 || visibleWidth <= 0) return false;

  // 计算可见比例
  const visibleArea = visibleHeight * visibleWidth;
  const totalArea = rect.height * rect.width;
  const visibleRatio = totalArea > 0 ? visibleArea / totalArea : 0;

  return visibleRatio >= threshold;
}

/**
 * 完整可见性检查 - 综合检查元素是否完全可见
 * @param element - 要检查的DOM元素
 * @returns 是否完全可见
 */
export function isElementFullyVisible(element: HTMLElement | null): boolean {
  return isElementInViewport(element, 1); // 要求100%在视口内
}

/**
 * 通过选择器判断容器是否可见
 * @param selector - CSS选择器
 * @param checkViewport - 是否同时检查视口可见性
 * @returns 是否可见
 */
export function isContainerVisible(selector: string, checkViewport: boolean = false): boolean {
  const element = document.querySelector(selector) as HTMLElement;

  if (checkViewport) {
    return isElementInViewport(element);
  }

  return isElementVisible(element);
}

/**
 * 检查特定类名的容器是否可见
 * @param className - 类名
 * @param checkViewport - 是否同时检查视口可见性
 * @returns 是否可见
 */
export function isContainerVisibleByClass(className: string, checkViewport: boolean = false): boolean {
  return isContainerVisible(`.${className}`, checkViewport);
}

/**
 * 观察元素可见性变化（使用 Intersection Observer）
 * @param element - 要观察的元素
 * @param callback - 可见性变化时的回调函数
 * @param threshold - 可见比例阈值数组
 * @returns 清理函数，用于停止观察
 */
export function observeElementVisibility(
  element: HTMLElement,
  callback: (isVisible: boolean, entry: IntersectionObserverEntry) => void,
  threshold: number[] = [0, 0.25, 0.5, 0.75, 1]
): () => void {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const isVisible = entry.isIntersecting && entry.intersectionRatio > 0;
        callback(isVisible, entry);
      });
    },
    {
      threshold,
      rootMargin: '0px'
    }
  );

  observer.observe(element);

  // 返回清理函数
  return () => {
    observer.unobserve(element);
    observer.disconnect();
  };
}
