// 渲染进程日志模块

// 保存原始console方法的引用
const originalConsole: {
  log: typeof console.log;
  info: typeof console.info;
  warn: typeof console.warn;
  error: typeof console.error;
  debug: typeof console.debug;
} = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
};

// 检查API是否存在
const hasLoggerAPI = (): boolean => {
  return typeof window !== 'undefined' &&
    typeof window.bdpStudioAPI !== 'undefined' &&
    window.bdpStudioAPI !== null &&
    typeof window.bdpStudioAPI.logger !== 'undefined';
};

// 如果我们在渲染进程中且API可用
if (hasLoggerAPI()) {
  // 重写console方法
  // console.log = (...args: any[]): void => {
  //   // 调用原始方法保持控制台输出
  //   originalConsole.log(...args);
  //   // 安全地调用日志API
  //   try {
  //     window.bdpStudioAPI.logger.log(...args);
  //   } catch (e) {
  //     // 忽略可能的错误
  //   }
  // };

  console.info = (...args: any[]): void => {
    originalConsole.info(...args);
    try {
      window.bdpStudioAPI.logger.info(...args);
    } catch (e) {
      // 忽略可能的错误
    }
  };

  console.warn = (...args: any[]): void => {
    originalConsole.warn(...args);
    try {
      window.bdpStudioAPI.logger.warn(...args);
    } catch (e) {
      // 忽略可能的错误
    }
  };

  console.error = (...args: any[]): void => {
    originalConsole.error(...args);
    try {
      window.bdpStudioAPI.logger.error(...args);
    } catch (e) {
      // 忽略可能的错误
    }
  };

  console.debug = (...args: any[]): void => {
    originalConsole.debug(...args);
    try {
      window.bdpStudioAPI.logger.debug(...args);
    } catch (e) {
      // 忽略可能的错误
    }
  };
}

// 导出日志接口
interface Logger {
  log: typeof console.log;
  info: typeof console.info;
  warn: typeof console.warn;
  error: typeof console.error;
  debug: typeof console.debug;
}

const logger: Logger = {
  // 导出方法以便直接使用
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
};

export default logger;
