import React from 'react';
import ReactD<PERSON><PERSON>lient, { Root } from 'react-dom/client';
import QRCodeLogin from './qrcode-login/index';

let root: Root;

export function renderLogin() {
  const rootElement = document.querySelector('#root');
  if (!rootElement) throw new Error('Root element not found');
  root = ReactDOMClient.createRoot(rootElement);
  root.render(<QRCodeLogin />);
}

export function unmountLogin() {
  root.unmount();
}