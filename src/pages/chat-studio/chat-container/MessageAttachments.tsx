import React, { useState } from 'react';
import { AttachmentInfo } from '@/services/types';
import { useMemoizedFn } from 'ahooks';
import {
  FileOutlined,
  FileImageOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import Icon from '@/components/MCPIcon';
import AttachmentPreviewModal from './AttachmentPreviewModal';

interface MessageAttachmentsProps {
  attachments: AttachmentInfo[];
  className?: string;
}

const MessageAttachments: React.FC<MessageAttachmentsProps> = ({
  attachments,
  className = ''
}) => {
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [previewAttachment, setPreviewAttachment] = useState<AttachmentInfo | null>(null);
  const [collapsed, setCollapsed] = useState<boolean>(true);

  const getFileIcon = useMemoizedFn((extension: string) => {
    const ext = extension.toLowerCase();
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(ext)) {
      return <FileImageOutlined className="message-attachments__list-icon--image" />;
    } else if (['.xls', '.xlsx', '.xlsm', '.xlsb'].includes(ext)) {
      return <Icon type="mcp-web-excel" className="message-attachments__list-icon--excel" />;
    } else if (['.doc', '.docx'].includes(ext)) {
      return <Icon type="mcp-web-word" className="message-attachments__list-icon--word" />;
    } else if (ext === '.pdf') {
      return <Icon type="mcp-web-pdf" className="message-attachments__list-icon--pdf" />;
    } else if (['.txt', '.md'].includes(ext)) {
      return <FileTextOutlined className="message-attachments__list-icon--text" />;
    }
    return <FileOutlined className="message-attachments__list-icon--default" />;
  });

  const formatFileSize = useMemoizedFn((bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  });

  const truncateFileName = useMemoizedFn((name: string, maxLength: number = 35): string => {
    if (name.length <= maxLength) return name;
    const extensionIndex = name.lastIndexOf('.');
    if (extensionIndex === -1) {
      return name.substring(0, maxLength - 3) + '...';
    }
    const extension = name.substring(extensionIndex);
    const baseName = name.substring(0, extensionIndex);
    const availableLength = maxLength - extension.length - 3;
    if (availableLength > 0) {
      return baseName.substring(0, availableLength) + '...' + extension;
    }
    return '...' + extension;
  });

  const handlePreviewAttachment = useMemoizedFn((attachment: AttachmentInfo) => {
    setPreviewAttachment(attachment);
    setPreviewVisible(true);
  });

  const handleClosePreview = useMemoizedFn(() => {
    setPreviewVisible(false);
    setPreviewAttachment(null);
  });

  const toggleCollapsed = useMemoizedFn(() => {
    setCollapsed(!collapsed);
  });

  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`message-attachments-list ${collapsed ? 'collapsed' : ''}`}>
        <div className="message-attachments-list__header">
          <span className="message-attachments-list__count">{attachments.length}个本地文件</span>
          <div className="message-attachments-list__toggle" onClick={toggleCollapsed}>
            <span className={collapsed ? 'collapsed' : 'expanded'}>
              <Icon type="mcp-web-mcp-tool-arrow-up-grey" />
            </span>
          </div>
        </div>
        {!collapsed && (
          <div className="message-attachments-list__content">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="message-attachments-list__item"
                onClick={() => handlePreviewAttachment(attachment)}
                title={`${attachment.originalName} (${formatFileSize(attachment.size)})`}
              >
                <div className="message-attachments-list__item-icon">
                  {getFileIcon(attachment.extension)}
                </div>
                <div className="message-attachments-list__item-info">
                  <div className="message-attachments-list__item-name">
                    {truncateFileName(attachment.originalName)}
                  </div>
                  <div className="message-attachments-list__item-size">
                    {formatFileSize(attachment.size)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <AttachmentPreviewModal
        visible={previewVisible}
        attachment={previewAttachment}
        onClose={handleClosePreview}
      />
    </>
  );
};

export default MessageAttachments;
