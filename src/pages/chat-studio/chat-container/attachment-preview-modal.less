.attachment-preview-modal {
  &__title {
    display: flex;
    align-items: center;
    gap: 8px;

    &--filename {
      // 主要文件名样式使用默认
    }

    &--size {
      font-size: 12px;
      color: #8c8c8c;
      font-weight: normal;
    }
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  &__image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 70vh;
    overflow: auto;
  }

  &__image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__file-preview {
    padding: 20px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px dashed #d9d9d9;

    &--icon {
      font-size: 48px;
      color: #8c8c8c;
      margin-bottom: 16px;
    }

    &--filename {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #1b1c1f;
    }

    &--info {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 16px;
    }

    &--description {
      font-size: 14px;
      color: #8c8c8c;
    }
  }

  &__error {
    padding: 40px;
    text-align: center;
    color: #8c8c8c;

    &--icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    &--message {
      // 使用默认样式
    }
  }
}