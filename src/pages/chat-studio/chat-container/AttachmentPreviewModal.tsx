import React, { useEffect, useState } from 'react';
import { Modal, Spin, message } from '@bdpfe/components';
import { api } from '@/services/api';
import './attachment-preview-modal.less';

interface AttachmentInfo {
  id: string;
  originalName: string;
  fileName: string;
  path: string;
  size: number;
  extension: string;
  uploadTime: string;
}

interface AttachmentPreviewModalProps {
  visible: boolean;
  attachment: AttachmentInfo | null;
  onClose: () => void;
}

const AttachmentPreviewModal: React.FC<AttachmentPreviewModalProps> = ({
  visible,
  attachment,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [fileData, setFileData] = useState<{
    base64Data: string;
    mimeType: string;
  } | null>(null);

  // 判断是否为图片文件
  const isImageFile = (extension: string): boolean => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.includes(extension.toLowerCase());
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 加载文件内容
  useEffect(() => {
    if (visible && attachment) {
      setLoading(true);
      setFileData(null);

      api.readAttachment(attachment.fileName)
        .then((result) => {
          if (result.success) {
            setFileData({
              base64Data: result.data.base64Data,
              mimeType: result.data.mimeType
            });
          } else {
            message.error(result.message || '文件读取失败');
          }
        })
        .catch((error) => {
          console.error('读取文件失败:', error);
          message.error('文件读取失败');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, attachment]);

  // 清理数据
  const handleClose = () => {
    setFileData(null);
    onClose();
  };

  if (!attachment) return null;

  const isImage = isImageFile(attachment.extension);

  return (
    <Modal
      title={
        <div className="attachment-preview-modal__title">
          <span className="attachment-preview-modal__title--filename">{attachment.originalName}</span>
          <span className="attachment-preview-modal__title--size">
            ({formatFileSize(attachment.size)})
          </span>
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={isImage ? 800 : 600}
      centered
      destroyOnClose
    >
      {loading && (
        <div className="attachment-preview-modal__loading">
          <Spin size="large" />
        </div>
      )}

      {!loading && fileData && isImage && (
        <div className="attachment-preview-modal__image-container">
          <img
            src={`data:${fileData.mimeType};base64,${fileData.base64Data}`}
            alt={attachment.originalName}
            className="attachment-preview-modal__image"
          />
        </div>
      )}

      {!loading && fileData && !isImage && (
        <div className="attachment-preview-modal__file-preview">
          <div className="attachment-preview-modal__file-preview--icon">
            📄
          </div>
          <div className="attachment-preview-modal__file-preview--filename">
            {attachment.originalName}
          </div>
          <div className="attachment-preview-modal__file-preview--info">
            文件类型: {attachment.extension.toUpperCase().replace('.', '')}
            &nbsp;|&nbsp;
            大小: {formatFileSize(attachment.size)}
          </div>
          <div className="attachment-preview-modal__file-preview--description">
            此文件类型不支持预览，可以通过其他应用程序打开
          </div>
        </div>
      )}

      {!loading && !fileData && (
        <div className="attachment-preview-modal__error">
          <div className="attachment-preview-modal__error--icon">❌</div>
          <div className="attachment-preview-modal__error--message">文件读取失败</div>
        </div>
      )}
    </Modal>
  );
};

export default AttachmentPreviewModal;
