import React from 'react';
import { Tooltip } from '@bdpfe/components';
import { FileOutlined, FileImageOutlined, FilePdfOutlined, FileWordOutlined, FileExcelOutlined, CloseOutlined } from '@ant-design/icons';
import Icon from '@/components/MCPIcon';
import './attachment-tags.less';

interface AttachmentInfo {
  id: string;
  originalName: string;
  fileName: string;
  path: string;
  size: number;
  extension: string;
  uploadTime: string;
}

interface AttachmentTagsProps {
  attachments: AttachmentInfo[];
  onRemove: (fileId: string) => void;
  onPreview?: (attachment: AttachmentInfo) => void;
}

const AttachmentTags: React.FC<AttachmentTagsProps> = ({ attachments, onRemove, onPreview }) => {
  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 缩略文件名
  const truncateFileName = (fileName: string, maxLength: number = 25): string => {
    if (fileName.length <= maxLength) return fileName;

    const extension = fileName.includes('.') ? fileName.split('.').pop() : '';
    const nameWithoutExt = fileName.includes('.') ? fileName.substring(0, fileName.lastIndexOf('.')) : fileName;

    if (extension) {
      const availableLength = maxLength - extension.length - 1; // -1 for the dot
      if (availableLength <= 3) {
        return `...${extension}`;
      }
      return `${nameWithoutExt.substring(0, availableLength - 3)}...${extension}`;
    } else {
      return `${nameWithoutExt.substring(0, maxLength - 3)}...`;
    }
  };

  // 根据文件扩展名返回图标
  const getFileIcon = (extension: string) => {
    const ext = extension.toLowerCase();
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(ext)) {
      return <FileImageOutlined className="attachment-card__icon--image" />;
    } else if (ext === '.pdf') {
      return <Icon type="mcp-web-pdf" style={{fontSize: 24}} className="attachment-card__icon--pdf" />;
    } else if (['.doc', '.docx'].includes(ext)) {
      return <Icon type="mcp-web-word" style={{fontSize: 24}} className="attachment-card__icon--word" />;
    } else if (['.xls', '.xlsx'].includes(ext)) {
      return <Icon type="mcp-web-excel" style={{fontSize: 24}} className="attachment-card__icon--excel" />;
    } else {
      return <FileOutlined className="attachment-card__icon--default" />;
    }
  };

  if (attachments.length === 0) {
    return null;
  }

  return (
    <div className={`attachment-cards ${attachments.length <= 2 ? 'attachment-cards--few' : ''}`}>
      {attachments.map((attachment) => (
        <div
          key={attachment.id}
          className={`attachment-card ${onPreview ? 'attachment-card--clickable' : ''}`}
          onClick={() => onPreview && onPreview(attachment)}
        >
          <div className="attachment-card__close" onClick={(e) => {
            e.stopPropagation();
            onRemove(attachment.id);
          }}>
            <CloseOutlined />
          </div>
          <div className="attachment-card__content">
            <div className="attachment-card__icon-container">
              {getFileIcon(attachment.extension)}
            </div>
            <div className="attachment-card__info">
              <Tooltip title={attachment.originalName}>
                <div className="attachment-card__filename">
                  {truncateFileName(attachment.originalName)}
                </div>
              </Tooltip>
              <div className="attachment-card__size">
                {formatFileSize(attachment.size)}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AttachmentTags;
