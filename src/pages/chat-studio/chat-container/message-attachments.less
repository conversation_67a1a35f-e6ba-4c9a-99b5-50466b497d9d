// 消息附件列表样式
.message-attachments-list {
  background: #fff;
  border-radius: 12px;
  padding: 0;
  border: 0.5px solid #C1C5CF;
  max-width: 300px;

  &.collapsed {
    border-bottom: none;
    border-radius: 12px;

    .message-attachments-list__header {
      border-bottom: none;
      border-radius: 12px;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px;

    background: #FFF;
    border-bottom: 0.5px solid #C1C5CF;
    border-radius: 12px 12px 0 0;

  }

  &__count {
    font-size: 12px;
    color: #797C8E;
    ;
    font-weight: 500;
  }

  &__toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;

    span {
      font-size: 12px;
      color: #797C8E;
      font-weight: bold;
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(0deg);
      }

      &.collapsed {
        transform: rotate(180deg);
      }
    }

    &:hover {
      span {
        color: #797C8E;
      }
    }
  }

  &__content {
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
    transition: opacity 0.2s ease, max-height 0.2s ease;
    background: #F6F7FA;
    border-radius: 0px 0px 12px 12px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 2px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.25);
      }
    }

    &::-webkit-scrollbar-thumb:active {
      background: rgba(0, 0, 0, 0.35);
    }
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 8px 0px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }

    &:not(:last-child) {
      border-bottom: none;
    }
  }

  &__item-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;

    .anticon {
      font-size: 16px;
    }
  }

  &__item-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__item-name {
    font-size: 12px;
    color: #333;
    font-weight: 500;
    line-height: 1.2;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__item-size {
    font-size: 11px;
    color: #999;
    line-height: 1;
  }

  // 文件类型图标颜色
  &__list-icon {
    &--image {
      color: #52c41a;
    }

    &--pdf {
      color: #ff4d4f;
    }

    &--word {
      color: #1890ff;
    }

    &--excel {
      color: #52c41a;
    }

    &--text {
      color: #722ed1;
    }

    &--default {
      color: #8c8c8c;
    }
  }
}

// 用户消息中的附件列表样式
.user-message-attachments {
  // margin-top: 8px;
  display: flex;

  .user-message-attachments__block {
    flex: 1;
  }

  .message-attachments-list {
    background: #fff;
    border: 1px solid #E8E9EB;
    width: 300px;
    flex: none;
  }
}

// 旧版本兼容样式（如果还有使用的话）