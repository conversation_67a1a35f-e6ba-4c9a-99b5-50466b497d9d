// 拖拽遮罩层样式
.chat-container__drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);

  .chat-container__drag-content {
    text-align: center;
    padding: 40px;
    position: relative;

    // 文件图标和彩色方块动画容器
    .drag-icon-container {
      position: relative;
      width: 120px;
      height: 120px;
      margin: 0 auto 24px;

      // 中心文件图标
      .center-file-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 48px;
        height: 48px;
        background: #4285f4;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        &::before {
          content: '📄';
          font-size: 24px;
          color: white;
        }
      }

      // 彩色方块
      .floating-square {
        position: absolute;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        animation: float 2s ease-in-out infinite;

        &.square-1 {
          background: #34a853;
          top: 20px;
          left: 30px;
          animation-delay: 0s;
        }

        &.square-2 {
          background: #ea4335;
          top: 40px;
          right: 20px;
          animation-delay: 0.5s;
        }

        &.square-3 {
          background: #fbbc04;
          bottom: 30px;
          left: 20px;
          animation-delay: 1s;
        }

        &.square-4 {
          background: #4285f4;
          bottom: 20px;
          right: 30px;
          animation-delay: 1.5s;
        }
      }

      // 向下箭头
      .down-arrow {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 12px solid transparent;
        border-right: 12px solid transparent;
        border-top: 16px solid #4285f4;
        opacity: 0.7;
      }
    }

    // 主标题
    .drag-title {
      font-size: 20px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    // 信息框
    .drag-info-box {
      background: rgba(66, 133, 244, 0.1);
      border: 1px solid #4285f4;
      border-radius: 6px;
      padding: 12px 16px;
      max-width: 300px;
      margin: 0 auto;

      .info-line {
        font-size: 13px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 拖拽时的动画效果
.chat-container__drag-overlay {
  animation: dragOverlayFadeIn 0.3s ease-out;
}

@keyframes dragOverlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 彩色方块浮动动画
@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }

  50% {
    transform: translateY(-8px) rotate(5deg);
    opacity: 1;
  }
}

// 旋转动画（用于上传状态）
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 上传状态的特殊样式
.chat-container__drag-overlay.uploading {
  .floating-square {
    animation: spin 1s linear infinite;
  }

  .center-file-icon {
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// 确保聊天容器具有相对定位
.chat-studio__chat_content {
  position: relative;

  // 拖拽时的边框效果
  &.drag-active {
    border: 2px dashed #1890ff;
    border-radius: 8px;
    transition: border-color 0.2s ease;
  }
}

// 防止拖拽时选中文本
.chat-container__drag-overlay * {
  user-select: none;
  pointer-events: none;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-container__drag-content {
    padding: 24px 16px;
    max-width: 300px;

    .drag-file-types {
      font-size: 10px;

      .file-type-group {
        margin-bottom: 2px;
      }
    }
  }
}