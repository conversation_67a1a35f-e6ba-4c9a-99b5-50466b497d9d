import React, { useState, useEffect, useRef, useContext } from 'react';
import { Message, Session, ToolCall } from '@/services/types';
import { Modal, Divider, Tooltip, message as AntdMessage } from '@bdpfe/components';
import { api } from '@/services/api';
import Icon from '@/components/MCPIcon';
import Markdown from '../Markdown';
import { AgentMessageRenderer } from '@/components/AgentMessageRenderer';
import { ChatInput, ChatInputRef } from './ChatInput';
import MessageAttachments from './MessageAttachments';
import { MessageEventParams } from '../types';
import { useMemoizedFn } from 'ahooks';
import { SessionContext } from '@/context/SessionContext';
import { parseMessageAttachments } from '@/utils/attachmentUtils';
import './message-attachments.less';
import './drag-overlay.less';

export const ChatContainer: React.FC<{ style?: React.CSSProperties, session: Session, sensorsFunc: (event: string, properties: any) => void, updateSessionName: (sessionId: number, name: string) => void }> = ({ style, session, sensorsFunc, updateSessionName }) => {
  const [messageList, setMessageList] = useState<Message[]>([]);
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const messageListRef = useRef<Message[]>([]);
  const [sendLoading, setSendLoading] = useState<boolean>(false);
  // const { activePage  } = useContext(PageContext);
  const show = style?.display === 'flex';
  const [inited, setInited] = useState<boolean>(false);

  const [noMessages, setNoMessages] = useState<boolean>(false);

  const noMessagesRef= useRef<boolean>(noMessages);
  const { currentSession } = useContext(SessionContext);

  // 拖拽相关状态
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [dragCounter, setDragCounter] = useState<number>(0);
  const [dragUploading, setDragUploading] = useState<boolean>(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const chatInputRef = useRef<ChatInputRef>(null);
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);


  // 处理拖拽的文件列表
  const handleDropFiles = useMemoizedFn(async (files: FileList) => {
    if (dragUploading) {
      AntdMessage.warning({
        content: '文件正在上传中，请稍后再试',
        duration: 3
      });
      return;
    }

    // 检查当前会话是否为活跃会话（与粘贴上传保持一致）
    if (session?.id !== currentSession?.id) {
      AntdMessage.warning({
        content: '请在当前活跃的会话中上传文件',
        duration: 3
      });
      return;
    }

    const fileArray = Array.from(files);

    // 检查是否有文件
    if (fileArray.length === 0) {
      AntdMessage.warning({
        content: '没有检测到有效的文件',
        duration: 3
      });
      return;
    }

    setDragUploading(true);

    try {
      // 直接调用ChatInput的addFiles方法
      await chatInputRef.current?.addFiles(fileArray);
    } catch (error) {
      console.error('拖拽文件上传错误:', error);
      AntdMessage.error({
        content: '文件上传过程中发生错误，请重试',
        duration: 4
      });
    } finally {
      setDragUploading(false);
    }
  });



  const handleDeleteMessage = (id: number) => {
    sensorsFunc('AI_PLAT_delete_message', { session: JSON.stringify(session), message: `${id}` });
    if (sendLoading) {
      return;
    }
    Modal.confirm({
      title: '删除消息',
      onOk: () => {
        api.deleteMessage(id).then(res => {
          if (res) {
            setMessageList(messageList.filter(item => item.id !== id));
          }
        });
      }
    });
  }

  const handleDeleteAllMessages = useMemoizedFn(async () => {
    sensorsFunc('AI_PLAT_delete_all_messages', { session: JSON.stringify(session) });
    if (sendLoading) {
      return;
    }
    Modal.confirm({
      title: '确认删除全部消息',
      onOk: async () => {
        api.deleteAllMessages(session?.id).then(res => {
          // console.log(res);
          if (res) {
            setMessageList([]);
          }
        })
      }
    })

  });

  const getRoleAvatar = (role: string, provider: string, model: string, avatar: string) => {
    if (role === 'user') {
      return <></>;
    }
    if (role === 'assistant' && provider) {
      return <div className='chat-container__message_list--avatar'>
        {avatar && <img src={avatar} />}
        <div className='chat-container__message_list--avatar-name'>{model} | {provider}</div>
      </div>;
    }
    return <></>;
  }

  useEffect(() => {
    if (session?.id) {
      if (inited) {
        return;
      }
      if (!show && !inited) {
        return;
      }
      api.getMessageList(session?.id).then(res => {
        if (res.length === 0) {
          setNoMessages(true);
        }
        setMessageList(res.map(item => {
          let content: string = item.message;
          // console.log('res', res);
          if (item.role === 'assistant') {
            try {
              const messageList = JSON.parse(item.message || '[]') as {
              role: string;
              content: string;
              tool_calls: ToolCall[];
            }[];
              content = messageList.map(item => {
                if (item.role === 'assistant') {
                  let currentContent = item.content;
                  if (item.tool_calls) {
                    item.tool_calls.forEach(toolCall => {
                      currentContent += `<blockquote data-type="tool_call">${JSON.stringify(toolCall)}</blockquote>`;
                    });
                  }
                  return currentContent;
                }
                if (item.role === 'tool') {
                  return `<blockquote data-type="tool_call_result">${item.content}</blockquote>`;
                }
                if (item.role === 'user') {
                  return null;
                }
                return item.content;
              }).filter(Boolean).join('\n').trim();
            } catch (error) {
              console.error('解析消息内容失败:', error, { message: item.message });
            }

          }
          return {
            id: item.id,
            message: content,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            sessionId: Number(item.sessionId),
            role: item.role,
            toolCalls: [],
            provider: item.provider,
            model: item.model,
            avatar: item.avatar,
            attachments: item.attachments,
          }
        }));
        setTimeout(() => {
          messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
        }, 100);
        setInited(true);
      });
    }
  }, [session, inited, show]);


  useEffect(() => {
    if (show && messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [show]);



  useEffect(() => {
    if (!inited) {
      return;
    }
    let currentMessageId: number = null;
    let currentToolCallId: string = null;
    // let currentSessionId: number = null;
    const handleMessage = (event: any, data: MessageEventParams) => {
      // console.log('event', event);
      // console.log('data', data);
      if (data.data.sessionId !== session.id) {
        return;
      }
      if (data.type === 'message_will_start') {
        setSendLoading(true);
      }

      // Agent模式消息处理
      if (data.type === 'agent_plan_will_start' || data.type === 'agent_plan_start') {
        // Agent规划开始，设置加载状态
        setSendLoading(true);
      }
      if (data.type === 'user_message_created') {
        // 处理用户消息创建事件，立即更新消息列表显示附件
        const userMessage = data.data.message;
        const existingIndex = messageListRef.current.findIndex(item => item.id === userMessage.id);

        if (existingIndex !== -1) {
          // 更新现有消息
          messageListRef.current[existingIndex] = {
            ...messageListRef.current[existingIndex],
            ...userMessage,
            attachments: userMessage.attachments // 确保附件信息被更新
          };
        } else {
          // 添加新消息
          messageListRef.current.push({
            id: userMessage.id,
            message: userMessage.message,
            createdAt: userMessage.createdAt,
            updatedAt: userMessage.updatedAt,
            sessionId: userMessage.sessionId,
            role: userMessage.role,
            provider: userMessage.provider,
            model: userMessage.model,
            avatar: userMessage.avatar,
            attachments: userMessage.attachments
          });
        }

        setMessageList([...messageListRef.current]);

        // 滚动到底部
        setTimeout(() => {
          messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
        }, 100);
      }
      if (data.type === 'message_start') {
        const updateQuestionMessageIndex = messageListRef.current.findIndex(item => item.id === data.data.parentMsg?.id);
        const updateAnswerMessageIndex = messageListRef.current.findIndex(item => item.id === data.data.message?.id);
        if (updateQuestionMessageIndex !== -1) {
          messageListRef.current[updateQuestionMessageIndex] = {
            ...messageListRef.current[updateQuestionMessageIndex],
            ...data.data.parentMsg
          }
        } else {
          messageListRef.current.push({
            id: data.data.parentMsg.id,
            message: data.data.parentMsg.message,
            createdAt: data.data.parentMsg.createdAt,
            updatedAt: data.data.parentMsg.updatedAt,
            sessionId: data.data.parentMsg.sessionId,
            role: data.data.parentMsg.role,
            provider: data.data.parentMsg.provider,
            model: data.data.parentMsg.model,
            avatar: data.data.parentMsg.avatar,
          });
        }
        if (updateAnswerMessageIndex !== -1) {
          messageListRef.current[updateAnswerMessageIndex] = {
            ...messageListRef.current[updateAnswerMessageIndex],
            ...data.data.message
          }
        } else {
          messageListRef.current.push({
            id: data.data.message.id,
            message: data.data.message.message,
            createdAt: data.data.message.createdAt,
            updatedAt: data.data.message.updatedAt,
            sessionId: data.data.message.sessionId,
            role: data.data.message.role,
            provider: data.data.message.provider,
            model: data.data.message.model,
            avatar: data.data.message.avatar,
          });
        }
        setMessageList([...messageListRef.current]);
        setTimeout(() => {
          // 消息开始滚动到底部，但仅针对最后一条消息，中间消息重新发送不予考虑
          if(updateAnswerMessageIndex === -1) {
            messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
          }

          // messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
        });
        currentMessageId = data.data.message?.id;
        // currentSessionId = data.data.message?.sessionId;
      }

      if (data.type === 'message_delta_content_send' || data.type === 'message_delta_reasoning_send' || data.type === 'tool_call_start' || data.type === 'tool_call_end' || data.type === 'message_error' ||
          data.type === 'agent_plan_will_start' || data.type === 'agent_plan_update' || data.type === 'agent_step_start' || data.type === 'agent_step_complete' || data.type === 'agent_progress_update') {
        const index = messageListRef.current.findIndex(item => item.id === currentMessageId);
        const currentMessage = messageListRef.current[index];
        if (index !== -1) {
          currentMessage.message += data.data.content;
          setMessageList([...messageListRef.current]);
        }
        setTimeout(() => {
          if (messageContainerRef.current.scrollTop + messageContainerRef.current.clientHeight >= messageContainerRef.current.scrollHeight - 60) {

            if(index === messageListRef.current.length - 1) {
              messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
            }
          }
        });

      }
      if (data.type === 'tool_call_pending') {
        if (currentToolCallId !== data.data.toolCallId) {
          // console.log('currentToolCallId', currentToolCallId, data.data.toolCall);
          currentToolCallId = data.data.toolCallId;
          const index = messageListRef.current.findIndex(item => item.id === currentMessageId);
          const currentMessage = messageListRef.current[index];
          currentMessage.message += `<blockquote data-type="tool_call">${JSON.stringify(data.data.toolCall)}</blockquote>`;
          setMessageList([...messageListRef.current]);
        }
      }
      if (data.type === 'tool_call_end') {
        currentToolCallId = null;
      }
      if (data.type === 'message_end') {
        if (noMessagesRef.current) {
          api.summarizeMessageTitle(session.id, session.provider, session.model).then(res => {
            if (typeof res === 'string') {
              updateSessionName(session.id, res);
            }
          });
        }
        setSendLoading(false);
      }
    };
    const handleInterruptStream = (event: any, data: any) => {
      // console.log('event', event);
      // console.log('data', data);
      if (data.data.sessionId !== session.id) {
        return;
      }
      setSendLoading(false);
    }
    const handleClearContext = (event: any, data: any) => {
      // console.log('event', event);
      // console.log('data', data);
      if (data.data.sessionId !== session.id) {
        return;
      }
      const currentClearMessage = data.data.message;
      const currentClearMessageIndex = messageListRef.current.findIndex(item => item.id === currentClearMessage.id);
      if (currentClearMessageIndex === -1) {
        setMessageList([...messageListRef.current, currentClearMessage]);
        setTimeout(() => {
          messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
        }, 100);
      }
    }
    const key = session.id.toString();
    api.onMessage(key, handleMessage);
    api.onInterruptStream(key, handleInterruptStream);
    api.onClearContext(key, handleClearContext);
    return () => {
      api.removeMessageListener(key);
      api.removeClearContextListener(key);
      api.removeInterruptStreamListener(key);
    };
  }, [session.id, inited]);

  useEffect(() => {
    messageListRef.current = messageList;
  }, [messageList, session.id]);

  useEffect(() => {
    noMessagesRef.current = noMessages;
  }, [noMessages]);

  // 拖拽事件监听器
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;

    // 添加拖拽事件监听器
    container.addEventListener('dragenter', handleDragEnter as any);
    container.addEventListener('dragover', handleDragOver as any);
    container.addEventListener('dragleave', handleDragLeave as any);
    container.addEventListener('drop', handleDrop as any);

    return () => {
      // 清理事件监听器
      container.removeEventListener('dragenter', handleDragEnter as any);
      container.removeEventListener('dragover', handleDragOver as any);
      container.removeEventListener('dragleave', handleDragLeave as any);
      container.removeEventListener('drop', handleDrop as any);

      // 清理超时
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
        dragTimeoutRef.current = null;
      }
    };
  }, [session?.id]);

  const getSelectedServerIds = useMemoizedFn(() => {
    try {
      return JSON.parse(localStorage.getItem(`selectedMcps_${session?.id}`) || '[]');
    } catch (error) {
      return [];
    }
  });

  // 拖拽事件处理函数
  const handleDragEnter = useMemoizedFn((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 如果正在上传，不处理新的拖拽
    if (dragUploading) {
      return;
    }

    // 清除之前的延迟隐藏
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
      dragTimeoutRef.current = null;
    }

    setDragCounter(prev => prev + 1);

    // 检查是否包含文件
    if (e.dataTransfer?.types.includes('Files')) {
      setIsDragOver(true);
    }
  });

  const handleDragOver = useMemoizedFn((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 如果正在上传，显示禁止拖拽效果
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = dragUploading ? 'none' : 'copy';
    }
  });

  const handleDragLeave = useMemoizedFn((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCounter = prev - 1;
      if (newCounter === 0) {
        // 添加延迟以防止快速进出导致的闪烁
        if (dragTimeoutRef.current) {
          clearTimeout(dragTimeoutRef.current);
        }
        dragTimeoutRef.current = setTimeout(() => {
          setIsDragOver(false);
        }, 100);
      }
      return newCounter;
    });
  });

  const handleDrop = useMemoizedFn(async (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(0);
    setIsDragOver(false);

    // 如果正在上传，忽略新的拖拽
    if (dragUploading) {
      AntdMessage.warning({
        content: '文件正在上传中，请等待当前上传完成',
        duration: 3
      });
      return;
    }

    const files = e.dataTransfer?.files;
    if (!files || files.length === 0) {
      AntdMessage.warning({
        content: '没有检测到有效的文件',
        duration: 3
      });
      return;
    }

    // 处理拖拽的文件
    await handleDropFiles(files);
  });

  const handleRefreshMessage = (message: string, answerMessageId?: number, questionMessageId?: number) => {
    sensorsFunc('AI_PLAT_refresh_message', { session: JSON.stringify(session), message: message, answerMessageId: `${answerMessageId}`, questionMessageId: `${questionMessageId}` })
    if (sendLoading) {
      return;
    }
    // let attachmentsJSONStr = messageListRef.current.find(item => item.id === questionMessageId)?.attachments;
    // let attachments: AttachmentInfo[] | undefined = undefined;
    // console.log('attachmentsJSONStr', attachmentsJSONStr);
    // if(attachmentsJSONStr) {
    //   try {
    //     attachments = JSON.parse(attachmentsJSONStr);
    //   } catch (error) {
    //     console.error('解析消息附件信息失败:', error, { attachmentsJSONStr });
    //   }
    // }
    api.chat(message, session.id, getSelectedServerIds().join(','), answerMessageId, questionMessageId);
  }

  return (
    <div className='chat-studio__chat_content' style={style} ref={chatContainerRef}>
      {/* 拖拽遮罩层 */}
      {(isDragOver || dragUploading) && (
        <div className={`chat-container__drag-overlay ${dragUploading ? 'uploading' : ''}`}>
          <div className='chat-container__drag-content'>
            {/* 文件图标和彩色方块动画容器 */}
            <div className="drag-icon-container">
              {/* 彩色方块 */}
              <div className="floating-square square-1"></div>
              <div className="floating-square square-2"></div>
              <div className="floating-square square-3"></div>
              <div className="floating-square square-4"></div>

              {/* 中心文件图标 */}
              <div className="center-file-icon"></div>

              {/* 向下箭头 */}
              <div className="down-arrow"></div>
            </div>

            {/* 主标题 */}
            <div className="drag-title">
              {dragUploading ? '正在上传文件...' : '在此处拖放文件'}
            </div>

            {/* 信息框 */}
            {dragUploading ? (
              <div className="drag-info-box">
                <div className="info-line">请稍候，文件正在处理中</div>
              </div>
            ) : (
              <div className="drag-info-box">
                <div className="info-line">支持文件数量：10个</div>
                <div className="info-line">单个文件大小：不超过10M</div>
                <div className="info-line">文件类型：png、jpeg、gif、pdf、word、excel</div>
              </div>
            )}
          </div>
        </div>
      )}
      <div ref={messageContainerRef} className='chat-container__message_list'>
        {messageList.map((item, index) => {
          const { role, message } = item;

          if (role === 'user') {
            const nextMessage = messageList[index + 1];
            const attachments = parseMessageAttachments(item.attachments);

            return (
              <div key={item.id} className='chat-container__message_list--item'>
                {getRoleAvatar(item.role, item.provider, null, null)}
                <div className="chat-container__message_list--content-wrapper user">
                  <div className="chat-container__message_list--content user">
                    {message}
                  </div>
                </div>
                {
                  attachments.length > 0 && (
                    <div className='user-message-attachments'>
                    <div className='user-message-attachments__block' />
                    <MessageAttachments
                      attachments={attachments}
                      className="user"
                    />
                    </div>
                  )
                }
                <div className='user-message-action'>
                  <div style={{ width: 20, height: 20 }} />
                  <Tooltip title='复制'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-copy-grey' style={{ fontSize: 16 }} onClick={() => {
                        sensorsFunc('AI_PLAT_copy_message', { session: JSON.stringify(session), message })
                        window['bdpStudioAPI'].copyClipboard(message);
                        AntdMessage.success('复制成功');
                      }} />
                    </span>
                  </Tooltip>
                  {/* {nextMessage?.role === 'assstant' && <Tooltip title='重新生成'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-refresh-grey' style={{ fontSize: 16 }} onClick={() => handleRefreshMessage(message, undefined, item.id)} />
                    </span>
                  </Tooltip>} */}
                  <Tooltip title='删除'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-delete-grey' style={{ fontSize: 16 }} onClick={() => handleDeleteMessage(item.id)} />
                    </span>
                  </Tooltip>

                </div>
              </div>
            );
          }
          if (role === 'assistant') {
            const lastMessage = index - 1 >= 0 ? messageList[index - 1] : null;
            // const isAgentMessage = item.message.includes('## 📋 任务规划');

            return (
              <div key={`${item.id}`} className='chat-container__message_list--item'>
                {getRoleAvatar(item.role, item.provider, item.model, item.avatar)}
                <div className="chat-container__message_list--content-wrapper assistant">
                  <div className="chat-container__message_list--content assistant">
                    {(
                      <Markdown content={item.message} />
                    )}
                  </div>
                </div>
                {sendLoading ? <div style={{ height: 20 }} /> : <div className='assistant-message-action'>
                  <Tooltip title='复制'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-copy-grey' style={{ fontSize: 16 }} onClick={() => {
                        sensorsFunc('AI_PLAT_copy_message', { session: JSON.stringify(session), message: item.message })
                        window['bdpStudioAPI'].copyClipboard(item.message);
                        AntdMessage.success('复制成功');
                      }} />
                    </span>
                  </Tooltip>
                  {lastMessage?.role === 'user' && <Tooltip title='重新生成'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-refresh-grey' style={{ fontSize: 16 }} onClick={() => handleRefreshMessage(lastMessage.message, item.id)} />
                    </span>
                  </Tooltip>}
                  <Tooltip title='删除'>
                    <span className='chat-container__message_list--action-icon'>
                      <Icon type='mcp-web-delete-grey' style={{ fontSize: 16 }} onClick={() => handleDeleteMessage(item.id)} />
                    </span>
                  </Tooltip>
                </div>}
              </div>
            );
          }
          if (role === 'clear-context') {
            return (
              <div key={item.id}  className='chat-container__message_list--item clear-context'>
                <Divider style={{ color: '#8f8f8f', fontSize: '12px', fontWeight: 'normal' }} dashed>清除上下文</Divider>
              </div>
            )
          }
        })}
      </div>
      <ChatInput
        ref={chatInputRef}
        session={session}
        sendLoading={sendLoading}
        handleDeleteAllMessages={handleDeleteAllMessages}
      />
    </div>
  );
};

export default React.memo(ChatContainer);
