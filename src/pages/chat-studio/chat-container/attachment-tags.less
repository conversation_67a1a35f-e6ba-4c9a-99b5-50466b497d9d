.attachment-cards {
  margin-bottom: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px 0;

  // 自定义滚动条样式 - 兼容WebKit内核（Chrome, Safari, Edge）
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  &::-webkit-scrollbar-thumb:active {
    background: rgba(0, 0, 0, 0.4);
  }

}

.attachment-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.2s ease;
  height: 56px;
  z-index: 0;

  &--clickable {
    cursor: pointer;

    &:hover {
      // background-color: #e6f7ff;
      // border-color: #3355FF;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &__close {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    opacity: 0.6;
    transition: all 0.2s ease;
    z-index: 1;

    &:hover {
      opacity: 1;
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
    }

    .anticon {
      font-size: 10px;
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 100%;
  }

  &__icon-container {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    .anticon {
      font-size: 24px;
    }
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
  }

  &__filename {
    font-size: 12px;
    font-weight: 500;
    color: #262626;
    line-height: 1.2;
    word-break: break-word;
  }

  &__size {
    font-size: 11px;
    color: #8c8c8c;
    line-height: 1;
  }

  &__icon {
    &--image {
      color: #52c41a;
    }

    &--pdf {
      color: #ff4d4f;
    }

    &--word {
      color: #1890ff;
    }

    &--excel {
      color: #52c41a;
    }

    &--default {
      color: #8c8c8c;
    }
  }
}

// 当只有1-2个附件时的样式优化
.attachment-cards {
  /* 中性色/内容区底色 */
  background: #F6F7FA;
  margin: -12px;
  padding: 12px;
  border-radius: 12px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;

  &.attachment-cards--few {
    display: flex;
    flex-wrap: wrap;

    .attachment-card {
      // flex: 0 0 160px;
    }
  }
}

// 导入操作系统特定样式