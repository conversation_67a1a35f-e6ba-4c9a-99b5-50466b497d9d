import { Message, ToolCall, AttachmentInfo } from "@/services/types";

export type MessageEventParams = {
  type: 'message_start'
  | 'message_delta_content_send'
  | 'message_delta_reasoning_send'
  | 'message_end'
  | 'tool_call_start'
  | 'tool_call_end'
  | 'message_will_start'
  | 'message_error'
  | 'tool_call_pending'
  | 'user_message_created'
  | 'agent_plan_will_start'
  | 'agent_plan_start'
  | 'agent_plan_update'
  | 'agent_step_start'
  | 'agent_step_complete'
  | 'agent_progress_update'
  data: {
    message?: Message;
    content?: string;
    sessionId: string | number;
    parentMsg?: Message;
    toolCallId?: string;
    toolCall?: ToolCall;
    attachments?: AttachmentInfo[];
    // Agent相关字段
    stepId?: string;
    progress?: number;
    totalSteps?: number;
    currentStep?: number;
    mcpName?: string;
    mcpId?: string;
  }
}
