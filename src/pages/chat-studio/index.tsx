import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { api } from '@/services/api';
import { Select, Button, Tooltip, InputNumber, Input, HighlightText, EllipsisTooltip, Tag } from '@bdpfe/components';
import { Slider } from 'antd';
import { SessionContext } from '@/context/SessionContext';
import { ChatContainer } from './chat-container';
import { Session, Model } from '@/services/types';
import { SessionList } from './SessionList';
import { SessionSetting } from './SessionSetting';
import classnames from 'classnames';
import { PageContext } from '@/context/pageContext';
import { Drawer } from '@bdpfe/components';
import { SearchInput } from '@/components/SearchInput';
import CheckedBlueIcon from '@/assets/image/checked-blue.svg';
import Icon from '@/components/MCPIcon';
import sessionEmpty from '@/assets/image/session-empty.svg';
import './index.less';

type PickFunction<T extends noop> = (
  this: ThisParameterType<T>,
  ...args: Parameters<T>
) => ReturnType<T>;

type noop = (this: any, ...args: any[]) => any;

function useMemoizedFn<T extends noop>(fn: T) {
  const fnRef = useRef<T>(fn);

  fnRef.current = useMemo<T>(() => fn, [fn]);

  const memoizedFn = useRef<PickFunction<T>>();
  if (!memoizedFn.current) {
    memoizedFn.current = function (this, ...args) {
      return fnRef.current.apply(this, args);
    };
  }

  return memoizedFn.current as PickFunction<T>;
}

const Option = Select.Option;
const ChatStudio: React.FC = () => {
  const { activePage, sensorsFunc } = useContext(PageContext);
  const [sessionList, setSessionList] = useState<Session[]>([]);
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [modelList, setModelList] = useState<Model[]>([]);
  const [chatSettingVisible, setChatSettingVisible] = useState(false);
  const [modelDrawerVisible, setModelDrawerVisible] = useState(false);
  const [searchModelValue, setSearchModelValue] = useState<string>('');
  const [modelSettingDrawerVisible, setModelSettingDrawerVisible] = useState(false);

  const isWindows = window.bdpStudioAPI.isWindows();

  const handleSelectModel = async (value: string) => {
    const provider = modelList.find(item => item.model === value)?.provider;
    sensorsFunc('AI_PLAT_session_model_select', { session: JSON.stringify(currentSession), model: value, provider });
    setCurrentSession({
      ...currentSession,
      model: value,
      provider,
    });
    setSessionList(sessionList.map(item => {
      if (item.id === currentSession?.id) {
        return {
          ...item,
          model: value,
          provider: modelList.find(item => item.model === value)?.provider,
        };
      }
      return item;
    }));
    if (currentSession) {
      await api.updateSession(currentSession.id, {
        model: value,
        provider: modelList.find(item => item.model === value)?.provider,
      });
    }
  }

  useEffect(() => {
    api.getEnabledModelList().then(res => {
      const functionCallList = res.filter(item => item.modelTag?.includes('FunctionCall'));
      const otherList = res.filter(item => !item.modelTag?.includes('FunctionCall'));
      setModelList([...functionCallList, ...otherList]);
      // console.log(res);
    });

  }, [activePage]);

  useEffect(() => {
    api.getSessionList().then(res => {
      setSessionList(res);
      if (res && res.length > 0) {
        setCurrentSession(res[0]);
      }
    });
  }, []);

  const handleSave = (values: {
    temperature?: number;
    top_p?: number;
    max_tokens?: number;
    top_k?: number;
    system_prompt?: string;
    repetition_penalty?: number;
    seed?: number;
  }) => {
    if (!currentSession) return;
    setCurrentSession({
      ...currentSession,
      ...values,
    });
    api.updateSession(currentSession.id, {
      ...values,
    }).then(res => {
      setCurrentSession(res);
      setSessionList(sessionList.map(item => item.id === currentSession.id ? res : item));
    });
  };

  const handleCreateSession = useMemoizedFn(() => {
    sensorsFunc('AI_PLAT_create_session');
    api.createSession('01405000').then(res => {
      setSessionList([res, ...sessionList]);
      setCurrentSession(res);
    });
  });

  const updateSessionName = useMemoizedFn((sessionId: number, name: string) => {
    setSessionList(sessionList.map(item => item.id === sessionId ? { ...item, name } : item));
  });

  const getModelTag = (model: Model) => {
    const colorMap = {
      'FunctionCall': 'blue',
      '推理模型': 'yellow',
      '多模态': 'green',
      '超长上下文': 'purple',
    };
    return (

      (model.modelTag || '').split(',').filter(Boolean).map((item: string) => {
        return <Tag key={item} color={colorMap[item]} style={{ marginLeft: 'auto', marginRight: 8 }}>{item}</Tag>
      })
    )
  }

  return (
    <SessionContext.Provider value={{
      currentSession,
      setCurrentSession,
      sessionList,
      setSessionList,
      chatSettingVisible,
      setChatSettingVisible,
      handleCreateSession,
    }}>
      <div className='chat-studio'>
        <SessionList />
        <div className='chat-studio__chat_container'>
          <div className={`chat-studio__chat_config ${isWindows ? '' : 'drag'}`}>
            <div className='chat-studio__chat_config--model_selector'>
              <div className='chat-studio__chat_config--model_selector--name' onClick={() => setModelDrawerVisible(true)}>
                <EllipsisTooltip style={{ width: '100%' }}>{currentSession?.model}</EllipsisTooltip>
                <Icon type='mcp-web-arrow-down-grey' />
              </div>
              <div className='chat-studio__chat_config--separator' />
              <Icon type='mcp-web-model-setting' style={{ cursor: 'pointer', fontSize: 16 }} onClick={() => { sensorsFunc('AI_PLAT_session_model_setting', { session: JSON.stringify(currentSession) }); setModelSettingDrawerVisible(true); }} />
            </div>
            {!chatSettingVisible && <div className='chat-studio__chat_config--mcp_tool' onClick={() => { sensorsFunc('AI_PLAT_mcp_tools_config', { session: JSON.stringify(currentSession) }); setChatSettingVisible(!chatSettingVisible) }}>
              <Icon type='mcp-web-mcp-config-grey' style={{ fontSize: 16 }} />
              <span>MCP工具配置</span>
            </div>}
          </div>
          {
            sessionList.map(item => {
              return (
                <ChatContainer updateSessionName={updateSessionName} sensorsFunc={sensorsFunc} style={item.id === currentSession?.id ? { display: 'flex' } : { display: 'none' }} key={item.id} session={item} />
              )
            })
          }
          {
            sessionList.length === 0 && <div className='chat-studio__chat_container--empty'>
              <img src={sessionEmpty} alt='empty-chat' />
              <Button type='primary' onClick={handleCreateSession}><Icon type='mcp-web-create-session-grey' />新建会话</Button>
            </div>

          }
        </div>
        {currentSession && <SessionSetting />}
      </div>
      {modelDrawerVisible && <Drawer width={480} className='chat-studio__model-setting-drawer' closeIcon={<Icon type='mcp-web-arrow-left-icon-grey' />} title='选择模型' open maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0)' }} maskClosable={true} onClose={() => setModelDrawerVisible(false)}>
        <SearchInput placeholder='搜索模型' value={searchModelValue} onChange={setSearchModelValue} />
        <div className='chat-studio__model_list'>
          {
            modelList.filter(item => item.model.includes(searchModelValue)).map(item => {
              const isActive = item.model === currentSession?.model;
              return (
                <div key={item.id} className={classnames('chat-studio__model_list--item', {
                  'chat-studio__model_list--item--active': isActive,
                })}
                  onClick={() => handleSelectModel(item.model)}
                >
                  <div className='chat-studio__model_list--item--content'>
                    {item.logo && <img src={item.logo} alt={item.logo} style={{ width: 24, height: 24, borderRadius: 4 }} />}
                    <HighlightText text={item.model} keywords={searchModelValue} />
                    <div className='chat-studio__model_list--item--tag'>{getModelTag(item)}</div>
                  </div>

                  {isActive && <img src={CheckedBlueIcon} alt='checked-blue' />}
                </div>
              )
            })
          }
        </div>
      </Drawer>}
      {modelSettingDrawerVisible && <Drawer
        title='模型设置'
        closeIcon={<Icon type='mcp-web-arrow-left-icon-grey' />}
        maskClosable={true}
        maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}
        open
        onClose={() => setModelSettingDrawerVisible(false)}
        className='chat-studio__model-setting-drawer'
      >
        <div className='chat-studio__model-setting'>
          <div className='chat-studio__model-setting_title'>
            系统提示词
            <Tooltip title='为会话提供高层指导'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Input.TextArea value={currentSession?.system_prompt} onChange={e => handleSave({ system_prompt: e.target.value })} />
          </div>
          <div className='chat-studio__model-setting_title'>
            温度
            <Tooltip title='用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              max={2}
              min={0}
              step={0.1}
              value={currentSession?.temperature || 0.7}
              style={{ flex: 1 }}
              onChange={value => handleSave({ temperature: value })}
            />
            <InputNumber
              min={0}
              step={0.1}

              value={currentSession?.temperature || 0.7}
              onChange={value => handleSave({ temperature: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>

          <div className='chat-studio__model-setting_title'>
            最大Tokens
            <Tooltip title='用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              min={0}
              max={8192}
              step={1}
              style={{ flex: 1 }}
              value={currentSession?.max_tokens || 4096}
              onChange={value => handleSave({ max_tokens: value })}
            />
            <InputNumber
              min={0}
              step={1}
              value={currentSession?.max_tokens || 4096}
              onChange={value => handleSave({ max_tokens: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>
          <div className='chat-studio__model-setting_title'>
            Top-P
            <Tooltip title='生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              min={0}
              max={1}
              step={0.01}
              style={{ flex: 1 }}
              value={currentSession?.top_p || 0.8}
              onChange={value => handleSave({ top_p: value })}
            />
            <InputNumber
              min={0}
              max={1}
              step={0.01}
              value={currentSession?.top_p || 0.8}
              onChange={value => handleSave({ top_p: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>
          <div className='chat-studio__model-setting_title'>
            取样数量
            <Tooltip title='生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              min={0}
              max={100}
              step={1}
              style={{ flex: 1 }}
              value={currentSession?.top_k || 50}
              onChange={value => handleSave({ top_k: value })}
            />
            <InputNumber
              min={0}
              max={100}
              step={1}
              value={currentSession?.top_k || 50}
              onChange={value => handleSave({ top_k: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>
          <div className='chat-studio__model-setting_title'>
            随机种子
            <Tooltip title='生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。'>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              min={0}
              max={8192}
              step={1}
              style={{ flex: 1 }}
              value={currentSession?.seed || 1234}
              onChange={value => handleSave({ seed: value })}
            />
            <InputNumber
              min={0}
              max={8192}
              step={1}
              value={currentSession?.seed || 1234}
              onChange={value => handleSave({ seed: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>
          <div className='chat-studio__model-setting_title'>
            重复惩罚
            <Tooltip title='用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。 '>
              <Icon type='mcp-web-question-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-studio__model-setting_content'>
            <Slider
              min={-2}
              max={2}
              step={0.1}
              style={{ flex: 1 }}
              value={currentSession?.repetition_penalty || 1}
              onChange={value => handleSave({ repetition_penalty: value })}
            />
            <InputNumber
              min={-2}
              max={2}
              step={0.1}
              value={currentSession?.repetition_penalty || 1}
              onChange={value => handleSave({ repetition_penalty: isNaN(Number(value)) ? undefined : Number(value) })}
            />
          </div>
        </div>
      </Drawer>}
    </SessionContext.Provider>
  );
};

export default ChatStudio;
