import React, { CSSProperties, useContext, useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { Button, Collapse, Drawer, message } from '@bdpfe/components';
import RehypeHighlight from 'rehype-highlight';
import RemarkGfm from 'remark-gfm';
import RehypeRaw from 'rehype-raw';
import CheckedGreen from '@/assets/image/checked-green.svg';
import ErrorRed from '@/assets/image/error-red.svg';
import ExcutingBlue from '@/assets/image/excuting-blue.svg';
import Icon from '@/components/MCPIcon';
import Mermaid from './Mermaid';
import ImagePreview from './ImagePreview';
import CodeBlock from './CodeBlock';
import EChartRenderer from './EChartRenderer';
import 'highlight.js/styles/atom-one-light.min.css';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { PageContext } from '@/context';

const parseBlockquotes = (text: string) => {
  // 定义状态
  const STATE = {
    NORMAL: 'NORMAL',                                      // 正常文本
    IN_TAG: 'IN_TAG',                                      // 在标签内
    IN_BLOCKQUOTE_REASONING: 'IN_BLOCKQUOTE_REASONING',    // 在推理内容blockquote中
    IN_BLOCKQUOTE_TOOL_CALL: 'IN_BLOCKQUOTE_TOOL_CALL',    // 在工具调用blockquote中
    IN_BLOCKQUOTE_TOOL_RESULT: 'IN_BLOCKQUOTE_TOOL_RESULT', // 在工具结果blockquote中
    IN_BLOCKQUOTE_TOOL_START: 'IN_BLOCKQUOTE_TOOL_START', // 在工具调用开始blockquote中
    IN_BLOCKQUOTE_UNKNOWN: 'IN_BLOCKQUOTE_UNKNOWN',        // 在未知类型blockquote中
    IN_CODE_BLOCK: 'IN_CODE_BLOCK',                        // 在代码块内
    IN_MERMAID: 'IN_MERMAID',                              // 在 Mermaid 代码块内
    IN_IMAGE: 'IN_IMAGE',                                 // 在图片内
    IN_HTML: 'IN_HTML',                                  // 在HTML内
    IN_ECHART: 'IN_ECHART'                                // 在EChart内 echart 的标记为 ```echart\n开头 结束为 ```
  };

  // 图片检测逻辑说明：
  // ✅ ![alt](url) - 完整的图片标记，会被识别
  // ✅ ![alt](url "title") - 带标题的图片标记，会被识别
  // ✅ ![](url) - 无alt文本的图片标记，会被识别
  // ❌ ![alt]( - 不完整的图片标记，当作普通文本
  // ❌ ![alt]() - 空URL的图片标记，当作普通文本
  // ❌ ![alt](   ) - 只有空格的URL，当作普通文本

  let state = STATE.NORMAL;
  let result: {
    type: string;
    content: string;
    language?: string;
  }[] = [];
  let currentText = '';
  let currentTag = '';
  let inCodeBlock = false;
  let i = 0;
  while (i < text.length) {
    const char = text[i];

    // console.log('char', char, state);
    if (state === STATE.NORMAL) {
      // 检查是否进入图片标记 ![
      if (char === '!' && text[i + 1] === '[') {
        // 从当前位置开始查找完整的图片标记
        const remainingText = text.substring(i);

        // 更严谨的图片正则，支持嵌套括号和各种URL格式
        // ![alt text](url "optional title") 或 ![alt text](url)
        const imageRegex = /^!\[([^\]]*)\]\(([^)\s]+(?:\s+"[^"]*")?)\)/;
        const match = remainingText.match(imageRegex);

        if (match) {
          // 找到完整的图片标记
          const fullImageMarkdown = match[0];

          // 验证URL部分是否合理（不为空且不只是空格）
          const urlPart = match[2].trim();
          if (urlPart) {
            // 保存之前的正常文本
            // if (currentText) {
            //   if (result[result.length - 1]?.type === STATE.NORMAL) {
            //     result[result.length - 1].content += currentText;
            //   } else {
            //     result.push({
            //       type: STATE.NORMAL,
            //       content: currentText
            //     });
            //   }

            // }
            currentText = '';
            // 添加完整的图片标记
            result.push({
              type: STATE.IN_IMAGE,
              content: fullImageMarkdown
            });

            // 跳过整个图片标记
            i += fullImageMarkdown.length;
            continue;
          }
        }

        // 不是完整的图片标记或URL为空，当作普通文本处理
        currentText += char;
        if (result[result.length - 1]?.type === STATE.NORMAL) {
          result[result.length - 1].content += char;
        } else {
          result.push({
            type: STATE.NORMAL,
            content: currentText
          });
        }
        i += 1;
        continue;
      }

      // 检查是否进入 mermaid 代码块
      if (char === '`' && text.substring(i, i + 10) === '```mermaid') {
        // 保存之前的正常文本
        result.push({
          type: STATE.IN_MERMAID,
          content: ''
        });
        currentText = '';
        state = STATE.IN_MERMAID;
        i += 10; // 跳过 '```mermaid'
        continue;
      }

      // 检查是否进入 echart 代码块
      if (char === '`' && text.substring(i, i + 9) === '```echart') {
        // 保存之前的正常文本
        result.push({
          type: STATE.IN_ECHART,
          content: ''
        });
        currentText = '';
        state = STATE.IN_ECHART;
        i += 9; // 跳过 '```echart'
        continue;
      }

      // 检查是否进入 html 代码块
      if (char === '`' && text.substring(i, i + 7) === '```html') {
        // 保存之前的正常文本
        result.push({
          type: STATE.IN_HTML,
          content: ''
        });
        currentText = '';
        state = STATE.IN_HTML;
        i += 7;
        continue;
      }

            // 检查是否进入普通代码块
      if (char === '`' && text.substring(i, i + 3) === '```') {
        // 检查是否有语言标识符
        let endOfFirstLine = text.indexOf('\n', i + 3);
        if (endOfFirstLine === -1) endOfFirstLine = text.length;

        const firstLine = text.substring(i + 3, endOfFirstLine).trim();
        // 如果不是 mermaid、echart 或 html，则作为普通代码块处理
        if (firstLine !== 'mermaid' && firstLine !== 'echart' && firstLine !== 'html') {
          result.push({
            type: STATE.IN_CODE_BLOCK,
            content: '', // 初始为空，后续会填充代码内容
            language: firstLine
          });
          currentText = '';
          state = STATE.IN_CODE_BLOCK;
          // 跳过 '```' 和语言标识符行
          i = endOfFirstLine === text.length ? endOfFirstLine : endOfFirstLine + 1;
          continue;
        }
      }


      if (char === '<' && text.substring(i, i + 11) === '<blockquote') {
        // if (currentText) {
        //   // result.push({
        //   //   type: 'NORMAL',
        //   //   content: currentText
        //   // });
        //   currentText = '';
        // }
        result.push({
          type: STATE.IN_TAG,
          content: '<blockquote'
        });
        currentText = '';
        state = STATE.IN_TAG;
        currentTag = '<blockquote'; // blockquote.length = 10
        i += 11; // 跳过'<blockquote'
      } else {
        currentText += char;
        if (result[result.length - 1]?.type === STATE.NORMAL) {
          result[result.length - 1].content += char;
        } else {
          result.push({
            type: STATE.NORMAL,
            content: currentText
          });
        }
        i += 1;
      }
      continue;
    }

    if (state === STATE.IN_TAG) {
      currentTag += char;

      // 检测标签结束
      if (char === '>') {
        // 解析data-type属性并确定blockquote类型
        const dataTypeMatch = currentTag.match(/data-type="([^"]+)"/);
        // console.log('dataTypeMatch', dataTypeMatch, currentTag);
        if (dataTypeMatch) {

          const dataType = dataTypeMatch[1];
          // 根据不同的data-type设置不同的状态
          if (dataType === 'reasoning_content') {
            state = STATE.IN_BLOCKQUOTE_REASONING;
          } else if (dataType === 'tool_call') {
            state = STATE.IN_BLOCKQUOTE_TOOL_CALL;
          } else if (dataType === 'tool_call_result') {
            state = STATE.IN_BLOCKQUOTE_TOOL_RESULT;
          } else if (dataType === 'tool_call_start') {
            state = STATE.IN_BLOCKQUOTE_TOOL_START;
          } else {
            state = STATE.IN_BLOCKQUOTE_UNKNOWN;
          }
          if (result[result.length - 1]?.type === STATE.IN_TAG) {
            result[result.length - 1].content += char;
            result[result.length - 1].type = state;
          }
        } else {
          state = STATE.NORMAL;
          if (result[result.length - 1]?.type === STATE.IN_TAG) {
            result[result.length - 1].content += char;
            result[result.length - 1].type = STATE.NORMAL;
          }
        }
      } else {
        if (result[result.length - 1]?.type === STATE.IN_TAG) {
          result[result.length - 1].content += char;
        }
      }
      i += 1;
      continue;
    }
    if (state === STATE.IN_BLOCKQUOTE_REASONING
      || state === STATE.IN_BLOCKQUOTE_TOOL_CALL
      || state === STATE.IN_BLOCKQUOTE_TOOL_RESULT
      || state === STATE.IN_BLOCKQUOTE_UNKNOWN
      || state === STATE.IN_BLOCKQUOTE_TOOL_START
    ) {
      // console.log('in blockquote', char,);
      if (char === '<' && text.substring(i, i + 13) === '</blockquote>') {
        currentText += '</blockquote>';
        if (result[result.length - 1]?.type === state) {
          result[result.length - 1].content += '</blockquote>';
        }
        currentText = '';
        currentTag = '';
        state = STATE.NORMAL;
        i += 13; // 跳过'</blockquote'
      } else {
        currentText += char;
        if (result[result.length - 1]?.type === state) {
          result[result.length - 1].content += char;
        }
        i += 1;
      }
    }
    if (state === STATE.IN_MERMAID || state === STATE.IN_HTML || state === STATE.IN_CODE_BLOCK || state === STATE.IN_ECHART) {
      // 检查是否遇到结束的 ```
      if (char === '`' && text.substring(i, i + 3) === '```') {
        // currentText += '```';
        // 将完整的代码块添加到结果中
        // if (result[result.length - 1]?.type === state) {
        //   result[result.length - 1].content += currentText;
        // }
        currentText = '';
        state = STATE.NORMAL;
        i += 3; // 跳过 '```'
      } else {
        currentText += char;
        if (result[result.length - 1]?.type === state) {
          result[result.length - 1].content += char;
        }
        i += 1;
      }
      continue;
    }
    continue;
  }
  return result;
};

// 处理剩余文本
// console.log('currentText', currentText);
// if (currentText) {
//   result.push({
//     type: 'NORMAL',
//     content: currentText
//   });
// }



/**
 * 从图片标记中提取图片信息
 * 格式: ![alt text](image_url "optional title")
 *
 * @param {string} imageMarkdown 图片的markdown标记
 * @returns {{ alt: string, src: string, title?: string }}
 */
function extractImageInfo(imageMarkdown: string) {
  // 更严谨的匹配，支持各种URL格式和可选标题
  const imageRegex = /^!\[([^\]]*)\]\(([^)\s]+)(?:\s+"([^"]*)")?\)$/;
  const match = imageMarkdown.match(imageRegex);

  if (match) {
    const alt = match[1] || '';
    const src = match[2];
    const title = match[3] || undefined;

    // 验证src不为空
    if (src && src.trim()) {
      return {
        alt,
        src: src.trim(),
        title
      };
    }
  }

  return null;
}


/**
 * 从任意文本（Markdown、HTML 或混合）中
 * 提取 <blockquote data-type="tool_call">…</blockquote> 的内容，
 * 并删除这些标签。
 *
 * @param {string} text 原始文本
 * @returns {{ contents: string[], cleanedText: string }}
 */
function extractToolCalls(text) {
  const contents = [];
  // 定义正则，匹配 <blockquote data-type="tool_call">…</blockquote>
  const regex = /<blockquote\s+[^>]*data-type=(["'])tool_call\1[^>]*>([\s\S]*?)<\/blockquote>/gi;

  const cleanedText = text.replace(regex, (_, _quote, inner) => {
    contents.push(inner);
    return '';
  });

  return { contents, cleanedText };
}


function extractToolCallStart(text: string) {
  const contents = [];
  const regex = /<blockquote\s+[^>]*data-type=(["'])tool_call_start\1[^>]*>([\s\S]*?)<\/blockquote>/gi;
  const cleanedText = text.replace(regex, (_, _quote, inner) => {
    contents.push(inner);
    return '';
  });
  return { contents, cleanedText };
}

function extractToolCallResult(text: string) {
  const contents = [];
  const regex = /<blockquote\s+[^>]*data-type=(["'])tool_call_result\1[^>]*>([\s\S]*?)<\/blockquote>/gi;
  const cleanedText = text.replace(regex, (_, _quote, inner) => {
    contents.push(inner);
    return '';
  });
  return { contents, cleanedText };
}

const ReasoningComponent = ({ content }: { content: React.ReactNode }) => {
  // 点击深度思考，展开或收起
  const [isOpen, setIsOpen] = useState(true);
  return (
    <div className='reasoning-content'>
      <div className='reasoning-content--title' onClick={() => setIsOpen(!isOpen)}>
        <span>深度思考</span>
        <Icon type={isOpen ? 'mcp-web-mcp-tool-arrow-up-grey' : 'mcp-web-mcp-tool-arrow-down-grey'} />

      </div>
      <div className='reasoning-content--content' style={{ display: isOpen ? 'block' : 'none' }}>
        {content}
      </div>
    </div>
  )
}

const CollapsePanel = Collapse.Panel;

const Markdown: React.FC<{ content: string }> = ({ content }) => {
  // console.log('content', content);
  // 扩展组件配置，增加tool-call支持
  // console.log('content', content);
  // console.log('content', content);
  const [collapseActiveKey, setCollapseActiveKey] = useState<string[]>([]);
  const [htmlPreviewVisible, setHtmlPreviewVisible] = useState<boolean>(false);
  const { activePage } = useContext(PageContext);

  const markdownList = parseBlockquotes(content);

  const isWindows = window.bdpStudioAPI.isWindows();

  // console.log('markdownList', markdownList);
  const getStatus = (item: any) => {
    if (item.status === 'pending') {
      return '执行中';
    }
    return item.status === 'success' ? '已完成' : '执行失败';
  }

  const getStatusCls = (item: any) => {
    if (item.status === 'pending') {
      return 'tool-call-header--status-pending';
    }
    return item.status === 'success' ? 'tool-call-header--status-success' : 'tool-call-header--status-error';
  }

  // console.log('markdownList', markdownList);

  useEffect(() => {
    if (activePage !== 'CHAT_STUDIO' && htmlPreviewVisible) {
      // const iframes = document.querySelectorAll('[id^="htmlPreview"]') as NodeListOf<HTMLIFrameElement>;
      // if (iframes?.length) {
      //   iframes.forEach(iframe => {
      //     iframe.srcdoc = '';
      //   });
      // }
      setHtmlPreviewVisible(false);
    }
  }, [activePage]);

  const mardownRenderer = markdownList.map((item, key) => {
    // console.log('item', item);
    // console.log('item', item.content);
    if (item.type === 'IN_IMAGE') {
      const imageInfo = extractImageInfo(item.content);
      if (imageInfo) {
        return (
          <ImagePreview
            key={key}
            src={imageInfo.src}
            alt={imageInfo.alt}
            title={imageInfo.title}
            enablePreview={true}
          />
        );
      }
      return null;
    }

    if (item.type === 'IN_BLOCKQUOTE_TOOL_CALL') {
      const { contents: toolCallContents, cleanedText: toolCallCleanedText } = extractToolCalls(item.content);

      console.log('toolCallContents', toolCallContents[0]);
      let currToolCall: any = {};
      let currToolCallResult: any = {};
      let currMcpName: string = '';
      let currToolCallStart: any = {};
      try {
        currToolCall = JSON.parse(toolCallContents[0] || '{}');
        if (currToolCall.id) {
          try {
            const toolCallResult = markdownList.find(result => {
              try {
                if (result.type === 'IN_BLOCKQUOTE_TOOL_RESULT') {
                  const { contents: toolCallResultContents, cleanedText: toolCallResultCleanedText } = extractToolCallResult(result.content);
                  console.log('toolCallResultContents', toolCallResultContents[0]);
                  const resultData = JSON.parse(toolCallResultContents[0] || '{}');
                  return resultData.tool_call_id === currToolCall.id;
                }
                return false;
              } catch (error) {

                return false;
              }
            });
            const toolCallStart = markdownList.find(result => {
              if (result.type === 'IN_BLOCKQUOTE_TOOL_START') {
                const { contents: toolCallStartContents, cleanedText: toolCallStartCleanedText } = extractToolCallStart(result.content);
                try {
                  currToolCallStart = JSON.parse(toolCallStartContents[0] || '{}');
                } catch (e) {
                  currToolCallStart = {};
                }

                return currToolCallStart.id === currToolCall.id;
              }
              return false;
            });
            if (toolCallStart) {
              let toolCallArguments = '{}';
              try {
                toolCallArguments = extractToolCallStart(toolCallStart.content).contents[0];
                toolCallArguments = JSON.parse(toolCallArguments)?.function?.arguments || '{}';

              } catch (e) {
                // console.log('toolCallArguments error', e);
              }
              // console.log('toolCallArguments', toolCallArguments);
              currToolCall.function.arguments = toolCallArguments;
            }
            if (toolCallResult) {
              // console.log('toolCallResult', toolCallResult);
              try {
                const { contents: toolCallResultContents, cleanedText: toolCallResultCleanedText } = extractToolCallResult(toolCallResult.content);
                currToolCallResult = JSON.parse(toolCallResultContents[0] || '{}')?.content;
                currMcpName = JSON.parse(toolCallResultContents[0] || '{}')?.mcpName;
                currToolCall.status = 'success';
              } catch (e) {
                currToolCall.status = 'error';
              }

            }
            if (toolCallStart) {

            }
          } catch (e) {

          }

        }
      } catch (error) {
        console.log('error', error);
      }
      // console.log('currToolCall', currToolCall);
      let _arguments = {};
      if (typeof currToolCall?.function?.arguments === 'string') {
        try {
          _arguments = JSON.parse(currToolCall?.function?.arguments || '{}');
        } catch (e) {
          _arguments = currToolCall?.function?.arguments || {};
        }
      }
      const isActive = collapseActiveKey.includes(currToolCall.id);
      let result = currToolCallResult;
      try {
        result = JSON.parse(currToolCallResult || '{}');
      } catch (e) {
        result = currToolCallResult;
      }
      const renderedResult = {
        id: currToolCall?.id,
        name: currToolCall?.function?.name,
        mcpName: currMcpName,
        arguments: _arguments,
        result: result,
        status: currToolCall?.status || 'pending'
      }

      return <Collapse key={key} accordion={false} activeKey={collapseActiveKey} onChange={(key) => setCollapseActiveKey(key as string[])}>
        <CollapsePanel key={currToolCall.id} showArrow={false} header={
          <div className='tool-call-header'>
            {renderedResult.mcpName ? renderedResult.mcpName + '-': ''}
            {renderedResult.name}
            <div className='tool-call-header--separator' />
            {renderedResult.status === 'success' && <img src={CheckedGreen} alt='success' />}
            {renderedResult.status === 'error' && <img src={ErrorRed} alt='error' />}
            {renderedResult.status === 'pending' && <img className='tool-call-header--status-pending-icon' src={ExcutingBlue} alt='excuting' />}
            <span className={getStatusCls(renderedResult)}>{
              getStatus(renderedResult)
            }</span>
            <span className='tool-call-header--copy'>
              <Icon type='mcp-web-copy-grey' style={{ fontSize: '16px' }} onClick={(e) => {
                e.stopPropagation();
                window.bdpStudioAPI.copyClipboard(
                  JSON.stringify({
                  "params": renderedResult.arguments,
                  "response": renderedResult.result
                  }, null, 2)
                );
                message.success('复制成功');
              }} />
            </span>
            <span className='tool-call-header--arrow'>
              <Icon style={{ fontSize: '18px' }} type={isActive ? 'mcp-web-mcp-tool-arrow-up-grey' : 'mcp-web-mcp-tool-arrow-down-grey'} />
            </span>
          </div>
        }>

          <pre><code>
            {
              JSON.stringify({
                "params": renderedResult.arguments,
                "response": renderedResult.result
              }, null, 2)
            }
          </code></pre>

        </CollapsePanel>
      </Collapse>
    }
    if (item.type === 'IN_BLOCKQUOTE_TOOL_RESULT' || item.type === 'IN_BLOCKQUOTE_TOOL_CALL_START') {
      // console.log('item', item);
      return null;
    }

    if (item.type === 'IN_MERMAID') {
      // 提取 mermaid 代码内容（去掉 ```mermaid 和 ```）
      const mermaidContent = item.content
        .replace(/^```mermaid\s*/, '')  // 去掉开头的 ```mermaid
        .replace(/\s*```$/, '')         // 去掉结尾的 ```
        .trim();

      if (mermaidContent) {
        return <Mermaid key={key} content={mermaidContent} />;
      }
      return null;
    }

    if (item.type === 'IN_ECHART') {
      // 提取 echart 代码内容（去掉 ```echart 和 ```）
      const echartContent = item.content
        .replace(/^```echart\s*/, '')  // 去掉开头的 ```echart
        .replace(/\s*```$/, '')         // 去掉结尾的 ```
        .trim();

      if (echartContent) {
        return <EChartRenderer key={key} content={echartContent} />;
      }
      return null;
    }

    if (item.type === 'IN_CODE_BLOCK') {
      // 处理普通代码块
      const codeContent = item.content.trim();
      const language = item.language || '';

      if (codeContent) {
        return (
          <CodeBlock
            key={key}
            content={codeContent}
            language={language}
          />
        );
      }
      return null;
    }

    if (item.type === 'IN_HTML') {
      const htmlContent = item.content
        .replace(/^```html\s*/, '')
        .replace(/\s*```$/, '')
        .trim();
      if (htmlContent) {

        const afterOpenChange = (open) => {
          if (open) {
            const iframe = document.getElementById(`htmlPreview-${key}`) as HTMLIFrameElement;
            if (iframe) {
              iframe.srcdoc = htmlContent;
            }
          }
        }

        const drawerProps = isWindows ? {
          headerStyle: { background: '#e9ebf2' }
        } : {
          title: 'HTML 预览',
          closeIcon: null,
          extra: <Button icon={<FullscreenExitOutlined />} onClick={() => setHtmlPreviewVisible(false)} />,
          headerStyle: { padding: '5px 16px 5px 0px', background: '#e9ebf2' }
        };

        return <div style={{ backgroundColor: '#eee', borderRadius: 8 }}>
          <CodeBlock
            key={key}
            content={htmlContent}
            language='html'
          />
          {/* <div style={{
            padding: 10,
            display: 'flex',
            flexDirection: 'row',
            gap: 8,
            paddingBottom: 10,
            backgroundColor: '#eee',
            marginTop: -16,
            borderRadius: 8
          }}>
            <Button size='large' icon={<FullscreenOutlined />} onClick={() => setHtmlPreviewVisible(true)}>预览</Button>
          </div> */}
          <Drawer
            contentWrapperStyle={{ height: '100%' }}
            mask={false}
            visible={htmlPreviewVisible}
            afterOpenChange={afterOpenChange}
            onClose={() => setHtmlPreviewVisible(false)}
            placement='bottom'
            className='html-preview-drawer'
            {...drawerProps}
          >
            <iframe id={`htmlPreview-${key}`} style={{ width: '100%', height: '100%', border: 0 }} />
          </Drawer>
        </div>;
      }
      return null;
    }
    if (item.type === 'NORMAL' && item.content?.trim()) {
      // console.log('item', key, item.content);
      return <ReactMarkdown
        rehypePlugins={[RehypeHighlight, RehypeRaw]}
        remarkPlugins={[RemarkGfm]}
        key={key}
        components={{
          blockquote: ({ node, ...props }) => {
            const dataType = props['data-type'];
            if (dataType === 'reasoning_content') {
              return (
                <ReasoningComponent content={props.children as string} />
              )
            }
            return <blockquote {...props} />;
          },
          code: ({ node, ...props }) => {
            const lang = props.className?.replace('language-', '');
            console.log('lang', lang);
            return <code {...props} />;
          },
          table: ({ node, ...props }) => {
            return <table className='markdown-table' {...props} />;
          },
          a: ({ children, href, ...restProps }) => {
            return <a
              onClick={(e) => {
                e.preventDefault();
                window.bdpStudioAPI.openExternalUrl(href);
              }}
              {...restProps}
            >
              {children}
            </a>;

          },
          img: ({ src, alt, ...restProps }) => {
            return <img
              style={{ userDrag: 'none', maxWidth: '60%' } as CSSProperties}
              src={src}
              alt={alt}
            />;
          }
        }}
      >{item.content?.trim()}</ReactMarkdown>
    }
    if (item.type === 'IN_BLOCKQUOTE_REASONING') {
      const _content = item.content.trim();
      if (!_content) {
        return null;
      }
      return <ReactMarkdown
        rehypePlugins={[RehypeHighlight, RehypeRaw]}
        remarkPlugins={[RemarkGfm]}
        components={{
          blockquote: ({ node, ...props }) => {
            if (props['data-type'] === 'reasoning_content') {
              return <ReasoningComponent content={props.children as string} />;
            }
            return <blockquote {...props} />;
          },
          a: ({ children, href, ...restProps }) => {
            return <a
              onClick={(e) => {
                e.preventDefault();
                window.bdpStudioAPI.openExternalUrl(href);
              }}
              {...restProps}
            >
              {children}
            </a>;

          },
        }}
      >{_content}</ReactMarkdown>
    }
  });

  return <>
    {mardownRenderer}
  </>
};

export default React.memo(Markdown);
