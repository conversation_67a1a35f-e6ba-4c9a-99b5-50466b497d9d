import React, { useState, useEffect, useRef } from 'react';
import { message } from '@bdpfe/components';
import Icon from '@/components/MCPIcon';
import hljs from 'highlight.js';

interface CodeBlockProps {
  content: string;
  language?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ content, language }) => {
  const [copySuccess, setCopySuccess] = useState(false);
  const codeRef = useRef<HTMLElement>(null);
  const [isHighlighted, setIsHighlighted] = useState(false);

  const handleCopy = async () => {
    try {
      await window['bdpStudioAPI'].copyClipboard(content);
      setCopySuccess(true);
      message.success('复制成功');
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  useEffect(() => {
    if (codeRef.current && content) {
      // 重置高亮状态
      setIsHighlighted(false);

      // 设置原始内容
      codeRef.current.textContent = content;

      // 使用 setTimeout 确保 DOM 更新后再进行高亮
      const timeoutId = setTimeout(() => {
        if (codeRef.current) {
          try {
            // 如果指定了语言，尝试使用指定语言进行高亮
            if (language && hljs.getLanguage(language)) {
              const highlighted = hljs.highlight(content, { language });
              codeRef.current.innerHTML = highlighted.value;
            } else {
              // 没有指定语言或语言不支持，使用自动检测
              hljs.highlightElement(codeRef.current);
            }
          } catch (error) {
            console.warn('代码高亮失败:', error);
            // 如果高亮失败，保持原始文本
            codeRef.current.textContent = content;
          }

          setIsHighlighted(true);
        }
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [content, language]);

  return (
    <div style={{ margin: '16px 0' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        color: 'rgba(0, 0, 0, 1)',
        fontSize: 14,
        fontWeight: 'bold',
        height: 34,
        padding: '0px 10px',
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
        backgroundColor: '#eee',
      }}>
        <span>{language || 'Code'}</span>
        <Icon
          type='mcp-web-copy-grey'
          style={{
            fontSize: 16,
            color: copySuccess ? '#3355FFF' : undefined,
            cursor: 'pointer'
          }}
          onClick={handleCopy}
          title="复制代码"
        />
      </div>
      <div style={{
        border: '0.5px solid #e3e3e3',
        borderTop: 'none',
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
        fontSize: '13px',
        overflow: 'hidden',
        position: 'relative',
      }}>
        <pre style={{
          padding: 16,
          backgroundColor: '#FAFAFA',
          color: '#383A42',
          marginBottom: 0,
          overflow: 'auto',
          fontSize: '13px',
          lineHeight: '1.5',
          fontFamily: 'Monaco, Consolas, "Courier New", monospace',
        }}>
          <code
            ref={codeRef}
            className={language ? `language-${language}` : ''}
            style={{ whiteSpace: 'pre' }}
          >
            {content}
          </code>
        </pre>
      </div>
    </div>
  );
};

export default React.memo(CodeBlock);
