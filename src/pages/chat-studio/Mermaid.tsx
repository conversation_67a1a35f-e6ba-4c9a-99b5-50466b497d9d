import React, { useEffect, useRef, useState, useCallback } from 'react';
import mermaid from 'mermaid';
// 移除不再需要的 Button 和图标导入
import MCPIcon from '@/components/MCPIcon';

interface MermaidProps {
  content: string;
}

const Mermaid: React.FC<MermaidProps> = ({ content }) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [showSourceCode, setShowSourceCode] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [scale, setScale] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const dragStartPos = useRef({ x: 0, y: 0 });

  // const [lastMermaidContent, setLastMermaidContent] = useState<string | null>(null);
  // const lastMermaidContentRef = useRef<string | null>(null);

  useEffect(() => {
    // 初始化 mermaid 配置
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      fontFamily: 'Arial, sans-serif',
      fontSize: 14,
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
      },
      sequence: {
        useMaxWidth: true,
        wrap: true,
      },
      gantt: {
        useMaxWidth: true,
      },
    });
  }, []);

  const handleCopyCode = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      // 降级方案：使用传统的复制方法
      try {
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch (fallbackErr) {
        console.error('降级复制也失败:', fallbackErr);
      }
    }
  }, [content]);

  const handleZoomIn = useCallback(() => {
    setScale(prev => Math.min(prev + 0.2, 10)); // 最大放大到 3 倍
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prev => Math.max(prev - 0.2, 0.5)); // 最小缩小到 0.5 倍
  }, []);

  const handleToggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  const handleResetZoom = useCallback(() => {
    setScale(1);
  }, []);

  const handleResetPosition = useCallback(() => {
    setPosition({ x: 0, y: 0 });
  }, []);

  // 拖拽开始
  const handleDragStart = useCallback((clientX: number, clientY: number) => {
    setIsDragging(true);
    setDragStart({ x: clientX, y: clientY });
    dragStartPos.current = { x: position.x, y: position.y };
  }, [position]);

  // 拖拽移动
  const handleDragMove = useCallback((clientX: number, clientY: number) => {
    if (!isDragging) return;

    const deltaX = clientX - dragStart.x;
    const deltaY = clientY - dragStart.y;

    setPosition({
      x: dragStartPos.current.x + deltaX,
      y: dragStartPos.current.y + deltaY,
    });
  }, [isDragging, dragStart]);

  // 拖拽结束
  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 鼠标事件处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // 只在图表区域响应拖拽
    if (e.target === elementRef.current || elementRef.current?.contains(e.target as Node)) {
      e.preventDefault();
      handleDragStart(e.clientX, e.clientY);
    }
  }, [handleDragStart]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    handleDragMove(e.clientX, e.clientY);
  }, [handleDragMove]);

  const handleMouseUp = useCallback(() => {
    handleDragEnd();
  }, [handleDragEnd]);

  // 触摸事件处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      handleDragStart(touch.clientX, touch.clientY);
    }
  }, [handleDragStart]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (e.touches.length === 1 && isDragging) {
      e.preventDefault();
      const touch = e.touches[0];
      handleDragMove(touch.clientX, touch.clientY);
    }
  }, [handleDragMove, isDragging]);

  const handleTouchEnd = useCallback(() => {
    handleDragEnd();
  }, [handleDragEnd]);

  const renderMermaid = useCallback(async (mermaidContent: string) => {
    if (!elementRef.current || !mermaidContent) return;

    setIsLoading(true);
    setError(null);

    try {
      // 生成唯一的 ID
      const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 先验证 mermaid 语法
      await mermaid.parse(mermaidContent?.trim());

      // 验证成功后再渲染
      const { svg } = await mermaid.render(id, mermaidContent?.trim());

      if (elementRef.current) {
        // 只有渲染成功时才更新内容和记录
        elementRef.current.innerHTML = svg;
        // setLastMermaidContent(mermaidContent);
      }
    } catch (err) {
      console.error('Mermaid rendering error:', err);
      setError(err instanceof Error ? err.message : '渲染 Mermaid 图表时发生错误');
      // 如果有 lastMermaidContent，则保持当前显示的内容不变
    } finally {
      setIsLoading(false);
    }
  }, []);

  const debouncedRenderMermaid = useCallback((mermaidContent: string) => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的定时器
    debounceTimerRef.current = setTimeout(() => {
      renderMermaid(mermaidContent);
    }, 50); // 300ms 防抖延迟
  }, [renderMermaid]);

  useEffect(() => {
    if (content) {
      debouncedRenderMermaid(content);
    }

    // 清理函数：组件卸载时清除定时器
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [content, debouncedRenderMermaid]);

  // 拖拽事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  return (
    <div style={{
      border: '1px solid #C1C5CF',
      borderRadius: '6px',
      backgroundColor: '#fafafa',
      marginBottom: '20px',
      textAlign: 'center',
      position: 'relative',
      overflow: 'hidden',
      ...(isFullscreen && {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        backgroundColor: '#fff',
        borderRadius: 0,
        border: 'none',
      })
    }}>
      <div style={{
        background: '#fff',
        borderBottom: '1px solid #C1C5CF',
        display: 'flex',
        alignItems: 'center',
        height: 44,
        padding: '0 16px'
      }}>
        <div style={{
          flex: 1,
          textAlign: 'left',
        }}>流程图</div>
        <div style={{
          marginLeft: 'auto',
          display: 'flex',
          alignItems: 'center',
          gap: 12
        }}>
          <MCPIcon
            onClick={() => setShowSourceCode(!showSourceCode)}
            type='mcp-web-source-code'
            style={{
              color: showSourceCode ? '#3355FFF' : undefined,
            }}
            className="chat-container__message_list--action-icon"
            title="查看源2代码"
          />
          <MCPIcon
            onClick={handleCopyCode}
            style={{
              color: copySuccess ? '#3355FFF' : undefined,
            }}
            type="mcp-web-copy-grey"
            className="chat-container__message_list--action-icon"
            title="复制源代码"
          />

          <MCPIcon
            onClick={handleZoomOut}
            type='mcp-web-scale-down'
            style={{
              opacity: scale <= 0.5 ? 0.5 : 1,
              cursor: scale <= 0.5 ? 'not-allowed' : 'pointer'
            }}
            className="chat-container__message_list--action-icon"
            title="缩小"
          />
          <span
            onClick={scale === 1 ? undefined : handleResetZoom}
            style={{
              fontSize: 12,
              color: scale === 1 ? '#ccc' : '#666',
              cursor: scale === 1 ? 'not-allowed' : 'pointer',
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: '#f5f5f5',
              border: '1px solid #ddd',
              minWidth: '40px',
              textAlign: 'center'
            }}
            title={`重置缩放 (${Math.round(scale * 100)}%)`}
          >
            {Math.round(scale * 100)}%
          </span>
          <MCPIcon
            onClick={handleZoomIn}
            type='mcp-web-scale-up'
            style={{

              opacity: scale >= 10 ? 0.5 : 1,
              cursor: scale >= 10 ? 'not-allowed' : 'pointer'
            }}
            className="chat-container__message_list--action-icon"
            title="放大"
          />
          <MCPIcon
            onClick={handleToggleFullscreen}
            type='mcp-web-full-screen'
            title={isFullscreen ? '退出全屏' : '全屏显示'}
            className="chat-container__message_list--action-icon"
          />
          <MCPIcon
            onClick={handleResetPosition}
            type='mcp-web-reset-position'
            style={{
              opacity: (position.x === 0 && position.y === 0) ? 0.5 : 1,
              cursor: (position.x === 0 && position.y === 0) ? 'not-allowed' : 'pointer'
            }}
            title="重置位置"
          />
        </div>

      </div>
      <div style={{
        padding: '16px',
        position: 'relative',
        overflow: 'hidden',
        height: '100%',
        width: '100%',
      }}>
        {isLoading && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#666',
            fontSize: '14px',
          }}>
            正在渲染图表...
          </div>
        )}

        {/* 源代码显示 */}
        {showSourceCode && (
          <div style={{
            marginBottom: '16px',
            textAlign: 'left',
            backgroundColor: '#f8f8f8',
            position: 'absolute',
            top: '0px',
            right: '0px',
            left: '0px',
            bottom: '0px',
            zIndex: 10,
          }}>
            <div style={{
              padding: '8px 12px',
              borderBottom: '1px solid #e8e8e8',
              backgroundColor: '#f0f0f0',
              fontSize: '12px',
              fontWeight: 'bold',
              color: '#666',
              height: '30px',
            }}>
              Mermaid 源代码
            </div>
            <pre style={{
              margin: 0,
              padding: '12px',
              fontSize: '12px',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              lineHeight: '1.4',
              overflow: 'auto',
              height: 'calc(100% - 30px)',
              color: '#333',
            }}>
              {content}
            </pre>
          </div>
        )}

        {/* Mermaid 图表渲染区域 */}
        <div
          ref={containerRef}
          style={{
            ...(isFullscreen && {
              height: 'calc(100vh - 100px)',
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }),
            cursor: isDragging ? 'grabbing' : 'grab',
            userSelect: 'none',
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <div
            ref={elementRef}
            style={{
              minHeight: isLoading ? '100px' : 'auto',
              opacity: isLoading ? 0.3 : 1,
              transition: isDragging ? 'none' : 'opacity 0.3s ease, transform 0.2s ease',
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transformOrigin: 'center',
              pointerEvents: isDragging ? 'none' : 'auto',
              ...(isFullscreen && {
                maxWidth: 'none',
                maxHeight: 'none',
              }),
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(Mermaid);
