import React, { CSSProperties, useState } from 'react';
import { Modal } from '@bdpfe/components';
import {
  FileImageOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import './image-preview.less';

interface ImagePreviewProps {
  src: string;
  alt: string;
  title?: string;
  style?: CSSProperties;
  onClick?: () => void;
  enablePreview?: boolean; // 是否启用放大预览
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt,
  title,
  style,
  onClick,
  enablePreview = true
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);

  const handleLoad = () => {
    setLoading(false);
    setError(false);
  };

  const handleError = () => {
    setLoading(false);
    setError(true);
  };

  const handleImageClick = () => {
    if (onClick) {
      onClick();
    } else if (enablePreview) {
      setPreviewVisible(true);
    }
  };

  const handlePreviewClose = () => {
    setPreviewVisible(false);
  };

  if (error) {
    return (
      <div className="image-preview__error-container">
        <FileImageOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
        <div>图片加载失败</div>
        <div style={{ fontSize: '12px', marginTop: '4px' }}>{alt || src}</div>
      </div>
    );
  }

  return (
    <>
      <div className="image-preview__container">
        {loading && (
          <div className="image-preview__loading-container">
            <LoadingOutlined style={{ fontSize: '24px' }} spin />
          </div>
        )}
        <img
          src={src}
          alt={alt}
          title={title}
          className="image-preview__image"
          style={{
            opacity: loading ? 0.3 : 1,
            cursor: (onClick || enablePreview) ? 'pointer' : 'default',
            WebkitUserDrag: 'none',
            ...style
          } as CSSProperties}
          onLoad={handleLoad}
          onError={handleError}
          onClick={handleImageClick}
          draggable={false}
        />
      </div>

      {/* 放大预览模态框 */}
      {enablePreview && (
        <Modal
          visible={previewVisible}
          onCancel={handlePreviewClose}
          footer={null}
          width="90vw"
          style={{ top: 20 }}
          bodyStyle={{
            padding: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh'
          }}
        >
          <div className="image-preview__modal-content">
            <img
              src={src}
              alt={alt}
              className="image-preview__modal-image"
            />

            {/* 图片信息 */}
            <div className="image-preview__modal-info">
              {alt && <div className="image-preview__modal-info-title">{alt}</div>}
              {title && <div className="image-preview__modal-info-subtitle">{title}</div>}
              <div className="image-preview__modal-info-tip">
                点击图片外区域关闭预览
              </div>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ImagePreview;
