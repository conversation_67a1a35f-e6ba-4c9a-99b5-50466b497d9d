import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const SendTextIcon: React.FC<IconProps> = (props) => {
  const { color, ...restProps } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...restProps}
    >
      <defs>
        <clipPath id="master_svg0_219_68332/219_67826">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_219_68332/219_67826)">
        <g>
          <path d="M16,0L0,0L0,16L16,16L16,0Z" fill={color || 'currentColor'} fillOpacity="0.009999999776482582" />
        </g>
        <g>
          <path
            d="M13.12715098876953,2.5560302858125Q13.042250988769531,2.5560354828125,12.962050988769532,2.5841011328125L1.9520119887695313,6.4376161328125Q1.8543929887695312,6.4717761328125,1.7783399887695313,6.541866132812499Q1.7022867887695312,6.6119661328125,1.6602816887695313,6.7064761328125Q1.6198887387695313,6.7973561328125,1.6173503597695313,6.8967761328125Q1.6148119787695312,6.9962061328125,1.6505145887695312,7.0890361328125Q1.6862172887695313,7.1818561328125,1.7547259887695312,7.2539561328125Q1.8232339887695312,7.3260561328125,1.9141179887695312,7.3664461328125L6.692900988769531,9.4903561328125L8.816800988769531,14.2690861328125Q8.85881098876953,14.3636861328125,8.934860988769532,14.4337861328125Q9.010910988769531,14.5037861328125,9.10853098876953,14.5379861328125Q9.155010988769531,14.5542861328125,9.20378098876953,14.5611861328125Q9.252540988769532,14.5680861328125,9.301710988769532,14.5652861328125Q9.350870988769532,14.5624861328125,9.398560988769532,14.5501861328125Q9.446250988769531,14.5378861328125,9.490620988769532,14.5165861328125Q9.534990988769533,14.4951861328125,9.574340988769531,14.4655861328125Q9.61369098876953,14.4359861328125,9.646500988769532,14.3992861328125Q9.67932098876953,14.3625861328125,9.704350988769532,14.3200861328125Q9.729370988769531,14.2776861328125,9.745640988769532,14.2312861328125L13.596350988769531,3.2290761328125Q13.627250988769532,3.1453121328125,13.627250988769532,3.0560301328125Q13.627250988769532,3.0067841328125002,13.617650988769531,2.9584851328125Q13.60805098876953,2.9101851328125,13.589250988769532,2.8646881328125Q13.570350988769531,2.8191911328125,13.543050988769531,2.7782451328125Q13.515650988769531,2.7372991328125,13.48085098876953,2.7024771328125Q13.446050988769532,2.6676551328125,13.405050988769531,2.6402954328125Q13.36415098876953,2.6129360328125,13.318650988769532,2.5940905328125Q13.273150988769531,2.5752450328125,13.224850988769532,2.5656376328125Q13.176550988769531,2.5560302738125,13.127250988769532,2.5560302738125L13.12715098876953,2.5560302848125L13.12715098876953,2.5560302858125ZM11.224490988769531,4.2517261328125L3.472720988769531,6.9648461328125L6.961020988769532,8.5151961328125L11.224490988769531,4.2517261328125ZM7.668100988769531,9.2223361328125L11.931450988769532,4.9589861328125L9.21841098876953,12.7104861328125L7.668100988769531,9.2223361328125Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default SendTextIcon;
