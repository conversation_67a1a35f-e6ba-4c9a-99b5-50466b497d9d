.image-preview {
  &__container {
    position: relative;
    display: flex;
    margin: 10px 0;
    justify-content: center;
    align-items: center;


  }

  &__image {
    max-width: 60%;
    display: block;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &__error-container {
    padding: 20px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    text-align: center;
    color: #999;
    margin: 10px 0;
  }

  &__loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  &__modal {
    &-content {
      position: relative;
      max-width: 100%;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
      border-radius: 4px;
    }

    &-info {
      margin-top: 16px;
      text-align: center;
      color: #666;
      font-size: 14px;

      &-title {
        font-weight: bold;
        margin-bottom: 4px;
      }

      &-subtitle {
        margin-bottom: 4px;
      }

      &-tip {
        font-size: 12px;
        color: #999;
      }
    }

    &-actions {
      position: absolute;
      top: -40px;
      right: 0;
      display: flex;
      gap: 8px;
    }

    &-button {
      background: rgba(0, 0, 0, 0.6);
      border: none;
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;
      color: white;
      transition: background 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }
}