import React, { useEffect, useRef, useState } from 'react';
import { message } from '@bdpfe/components';
import * as echarts from 'echarts';
import './echart-renderer.less';

// 临时解决方案：如果echarts不可用，使用window.echarts
// declare global {
//   interface Window {
//     echarts?: any;
//   }
// }

interface EChartRendererProps {
  content: string;
  key?: string | number;
}

const EChartRenderer: React.FC<EChartRendererProps> = ({ content, key }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!chartRef.current) return;

    const initChart = () => {
      try {
        setLoading(true);
        setError(null);

        // 检查echarts是否可用
        if (!echarts) {
          throw new Error('ECharts 库未加载，请确保已正确安装和导入 ECharts');
        }

        // 销毁现有图表实例
        if (chartInstance.current) {
          chartInstance.current.dispose();
          chartInstance.current = null;
        }

        // 解析EChart配置
        let chartOption;
        console.log('content', content);
        try {
          chartOption = JSON.parse(content);
        } catch (parseError) {
          // 如果不是JSON，尝试作为JavaScript代码执行
          try {
            // 创建一个安全的执行环境
            const func = new Function('echarts', `return ${content}`);
            chartOption = func(echarts);
            console.log('chartOption', chartOption, content);
          } catch (evalError) {
            throw new Error('EChart配置解析失败: 请提供有效的JSON或JavaScript对象');
          }
        }

        // 验证配置对象
        if (!chartOption || typeof chartOption !== 'object') {
          throw new Error('EChart配置必须是一个对象');
        }

        // 创建图表实例
        chartInstance.current = echarts.init(chartRef.current);

        // 设置图表配置
        chartInstance.current.setOption(chartOption, true);

        // 监听窗口大小变化
        const handleResize = () => {
          if (chartInstance.current) {
            chartInstance.current.resize();
          }
        };

        window.addEventListener('resize', handleResize);

        setLoading(false);
        setError(null);

        // 清理函数
        return () => {
          window.removeEventListener('resize', handleResize);
          if (chartInstance.current) {
            chartInstance.current.dispose();
            chartInstance.current = null;
          }
        };
      } catch (err) {
        setError(err instanceof Error ? err.message : '渲染EChart时发生未知错误');
        // setLoading(false);
        return () => {

        }
      }
    };

    const cleanup = initChart();

    return cleanup;
  }, [content]);

  // 复制图表配置到剪贴板
  const copyToClipboard = () => {
    try {
      window.bdpStudioAPI.copyClipboard(content);
      message.success('EChart配置已复制到剪贴板');
    } catch (err) {
      message.error('复制失败');
    }
  };

  // if (error) {
  //   return (

  //   );
  // }

  return (
    <div className="echart-container">
      {loading && (
        <div className="echart-loading">
          加载中...
        </div>
      )}
      {
        !loading && error && (
          <div className="echart-error">
            <div className="echart-error-title">EChart渲染错误</div>
            <div>{error}</div>
            <details className="echart-error-details">
              <summary className="echart-error-summary">查看原始配置</summary>
              <pre className="echart-error-code">
                {content}
              </pre>
            </details>
          </div>
        )
      }

      <div
        ref={chartRef}
        className="echart-chart"
      />

      <div className="echart-footer">
        <span>EChart 图表</span>
        <button
          onClick={copyToClipboard}
          className="echart-copy-btn"
        >
          复制配置
        </button>
      </div>
    </div>
  );
};

export default EChartRenderer;
