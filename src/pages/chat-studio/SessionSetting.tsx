import { SessionContext } from '@/context/SessionContext';
import { api } from '@/services/api';
import { Tabs } from '@bdpfe/components';
import { Switch } from '@bdpfe/components';
import Icon from '@/components/MCPIcon';
import React, { useContext, useEffect, useState, useRef } from 'react';
import uniqBy from 'lodash/uniqBy';
import { Server } from '@/common/types';
import interact from 'interactjs';
import { PageContext } from '@/context';

// export type Tool = import('@modelcontextprotocol/sdk/types').Tool;

const TabPane = Tabs.TabPane;
export const SessionSetting: React.FC = () => {
  const { serverList } = useContext(PageContext);
  const { currentSession, chatSettingVisible, setChatSettingVisible } = useContext(SessionContext);
  const [mcps, setMcps] = useState<Server[]>([]);
  const [selectedMcpTools, setSelectedMcpTools] = useState<number[]>([]);
  const [isResizing, setIsResizing] = useState<boolean>(false); // 拖拽状态
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const localSelectedMcpTools = localStorage.getItem(`selectedMcps_${currentSession?.id}`);
    if (localSelectedMcpTools) {
      setSelectedMcpTools(JSON.parse(localSelectedMcpTools));
    } else {
      setSelectedMcpTools([]);
    }
  }, [currentSession?.id]);

  useEffect(() => {
    if (currentSession?.id) {
      setMcps(uniqBy(serverList, 'id').filter(mcp => mcp.serverStatus === 1));
    }
  }, [currentSession?.id, serverList]);

  // 添加resizable功能
  useEffect(() => {
    if (!containerRef.current) return;

    const element = containerRef.current;

    const interactable = interact(element)
      .resizable({
        edges: { left: true }, // 只允许从左边拖拽
        listeners: {
          start() {
            setIsResizing(true);
          },
          move(event) {
            const target = event.target as HTMLElement;
            let newWidth = event.rect.width;

            // 设置最小和最大宽度限制
            newWidth = Math.max(280, Math.min(600, newWidth));

            // 直接设置DOM宽度
            target.style.width = `${newWidth}px`;
          },
          end() {
            setIsResizing(false);
            // 保存宽度到localStorage，使用当前最新的宽度值
            const currentWidth = parseInt(element.style.width.replace('px', ''), 10);
            localStorage.setItem(`sessionSettingWidth_${currentSession?.id}`, currentWidth.toString());
          }
        },
        modifiers: [
          // 保持纵横比和限制
          interact.modifiers.restrictSize({
            min: { width: 280, height: 100 },
            max: { width: 600, height: 2000 }
          })
        ]
      });

    // 清理函数
    return () => {
      interactable.unset();
    };
  }, [chatSettingVisible, currentSession?.id]);

  // 初始化DOM宽度
  useEffect(() => {
    if (containerRef.current) {
      // 设置默认宽度
      containerRef.current.style.width = '320px';
    }
  }, []);

  // 从localStorage恢复宽度 - 直接设置到DOM
  useEffect(() => {
    if (currentSession?.id && containerRef.current) {
      const savedWidth = localStorage.getItem(`sessionSettingWidth_${currentSession.id}`);
      if (savedWidth) {
        const parsedWidth = parseInt(savedWidth, 10);
        if (!isNaN(parsedWidth) && parsedWidth >= 280 && parsedWidth <= 600) {
          // 直接设置DOM宽度
          containerRef.current.style.width = `${parsedWidth}px`;
        } else {
          // 如果保存的宽度不在有效范围内，使用默认宽度
          containerRef.current.style.width = '320px';
        }
      } else {
        // 设置默认宽度
        containerRef.current.style.width = '320px';
      }
    }
  }, [currentSession?.id]);

  const handleSelectMcpTool = (mcpId: number) => {
    setSelectedMcpTools(prev => {
      if (prev.includes(mcpId)) {
        localStorage.setItem(`selectedMcps_${currentSession?.id}`, JSON.stringify(prev.filter(tool => tool !== mcpId)));
        return prev.filter(tool => tool !== mcpId);
      }
      localStorage.setItem(`selectedMcps_${currentSession?.id}`, JSON.stringify([...prev, mcpId]));
      return [...prev, mcpId];
    });
  }

  return (
    <div
      ref={containerRef}
      id='chat-studio-mcp-setting'
      className={`chat-studio__mcp-setting ${isResizing ? 'resizing' : ''}`}
      style={{
        display: chatSettingVisible ? 'block' : 'none'
      }}
    >
      <div className='chat-studio__mcp-setting_title'>
        MCP工具配置
        <div className='chat-studio__mcp-setting_title_collapse' onClick={() => setChatSettingVisible(!chatSettingVisible)}>
          <Icon type='mcp-web-collapse-session-grey' style={{ fontSize: 16 }} />
        </div>
      </div>
      <div className='chat-studio__mcp-setting_content'>
        {
          mcps.map(mcp => {
            return (
              <div className='chat-studio__mcp-setting_item' key={mcp.id}>
                <React.Fragment key={mcp.id}>
                  <span>{mcp.serverName}</span>
                  <Switch
                    key={mcp.id}
                    checked={selectedMcpTools.includes(mcp.id)}
                    onChange={() => handleSelectMcpTool(mcp.id)}
                  />
                </React.Fragment>
              </div>
            );
          })
        }
      </div>
    </div>
  );
};
