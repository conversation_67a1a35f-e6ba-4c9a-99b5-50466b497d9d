.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
}

.studio-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  background: #e9ebf2;

  .ant-btn-text {
    background: transparent;

    &:hover {
      background: #ebf1ff;
      color: #5c7cff;
    }

    &:active {
      background: #d6e2ff;
      color: #2b42d9;
    }
  }

  .bdp-input-search .ant-input-group.ant-input-wrapper {
    border-radius: 6px;
  }

  .ant-input-group > .ant-input:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-color: #c1c5cf;
   &.ant-input-lg{
    height: 32px;
   }

    &:hover {
      border-color: #c1c5cf;
      border-right: none;
    }

    &:focus {
      box-shadow: none;
      border-right: none;
    }
  }

  .ant-input-search
    > .ant-input-group
    > .ant-input-group-addon:last-child
    .ant-input-search-button {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;

    border-left: none;
    border-color: #c1c5cf;

    &:hover {
      border-color: #c1c5cf;
    }
  }

  .ant-input-group-addon {
    background-color: transparent;
  }

  .ant-input-group-wrapper.ant-input-search:hover {
    > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button,
    .ant-input-group > .ant-input:first-child {
      border-color: #5c7cff;
    }
  }
}

.studio-container__header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-app-region: drag;
  flex: none;
}

.studio-container__header--action {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag;
}

.studio-container__header--title {
  font-weight: bold;
  font-size: 14px;
  img {
    width: 86px;
    height: 20px;
  }
}

.studio-container__header--extra {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag;
  width: 136px;
  gap: 8px;
}
.studio-container__header--icon {
  width: 40px;
  height: 100%;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  img {
    width: 12px;
    height: 12px;
  }
}

.bdp-studio {
  display: flex;
  width: 100%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-top: 0.5px solid #00000019;
  border-left: 0.5px solid #00000019;
  flex: none;
  background-image: url('@/assets/image/background.png');
  background-size: 100% 80px;
  background-color: #fff;
  background-repeat: no-repeat;
}

.bdp-studio__content {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.bdp-studio__page {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.bdp-studio__page--active {
  display: block;
}

.studio-container__side {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0px 12px;
  width: 72px;
  min-width: 72px;
  height: calc(100% - 40px);
  margin-top: 40px;
  background: #e9ebf2;
  -webkit-app-region: drag;
}

.studio-container__wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: calc(100vw - 72px);
}

.bdp-studio__list-item {
  color: #1a1a1a;
  border-radius: 4px;
  padding: 7px 12px;

  &:hover {
    background: #ebf1ff;
    color: #5c7cff;
  }

  &:active {
    background: #d6e2ff;
    color: #2b42d9;
  }
}

.bdp-studio__list-item--disabled {
  background: #f5f5f5;
  color: #cccccc;
  cursor: not-allowed;
}

.bdp-studio__list-item--focus {
  background: #ebf1ff;
  color: #5c7cff;
}

.bdp-studio__list-item--active {
  color: #3355ff;

  &:hover {
    background: #ebf1ff;
    color: #5c7cff;
  }

  &:active {
    background: #d6e2ff;
    color: #2b42d9;
  }
}
