import React from 'react';
import AppConainer from '@/AppContainer';
import Home from '@/pages/index';
import ReactDOMClient, { Root } from 'react-dom/client';

let root: Root;

export function renderApp() {
  const rootElement = document.querySelector('#root');
  if (!rootElement) throw new Error('Root element not found');
  root = ReactDOMClient.createRoot(rootElement);
  root.render(
    <AppConainer>
      <Home />
    </AppConainer>
  );
}

export function unmountApp() {
  root.unmount();
}