import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Input, Button, Switch, Select, message, Spin, Tag } from '@bdpfe/components';
import { ModelServiceListContext, PageContext } from '@/context';
import { api } from '@/services/api';
import Icon from '@/components/MCPIcon';
import './index.less';
import { ToolOutlined } from '@ant-design/icons';

type Model = {
  createdAt: string;
  id: number;
  model: string;
  provider: string;
  enabled: 0 | 1;
  is_default: 0 | 1;
  logo: string;
  modelTag: string;
}

const ModelConfig: React.FC = () => {
  const { activeProviderId, providerList, setLoading, setProviderList, loading } = useContext(ModelServiceListContext);
  const { sensorsFunc } = useContext(PageContext);
  const [modelList, setModelList] = useState<Model[]>([]);
  const [defaultModel, setDefaultModel] = useState<Model | null>(null);
  const [verifyLoading, setVerifyLoading] = useState(false);

  const isWindows = window.bdpStudioAPI.isWindows();

  const activeProvider = providerList.find(item => item.id === activeProviderId);

  // const handleSwitchChange = async (checked: boolean) => {
  //   await api.saveProviderStatus(activeProvider.name, checked ? 1 : 0);
  //   const providerList = await api.getModelProviderList();
  //   setProviderList([...providerList]);
  // }

  const enableModel = async (id: number, checked: boolean) => {
    sensorsFunc('AI_PLAT_switch_model', { provider: JSON.stringify(activeProvider), modelId: `${id}`, checked: `${checked}` });
    const res = await window['bdpStudioAPI'].enabledModel(id, checked);
    if (res) {
      const list = modelList.map(item => {
        if (item.id === id) {
          if (checked) {
            return { ...item, enabled: 1 } as Model;
          }
          if (item.id === defaultModel?.id) setDefaultModel(null);
          return { ...item, enabled: 0, is_default: 0 } as Model;
        }
        return item;
      });
      setModelList(list);
    }
  }

  const handleDefaultModelChange = async (value: number) => {
    sensorsFunc('AI_PLAT_switch_default_model', { provider: JSON.stringify(activeProvider), modelId: `${value}` });
    const res = await window['bdpStudioAPI'].setDefaultModel(value);
    if (res) {
      setDefaultModel(modelList.find(item => item.id === value));
    }
  }

  const handleResetApiKey = async () => {
    const provider = providerList.find(item => item.name === activeProvider.name);
    sensorsFunc('AI_PLAT_reset_api_key', { provider: JSON.stringify(provider) });
    await window['bdpStudioAPI'].saveProviderCurrentAPIKey(activeProvider.name, activeProvider.defaultApiKey);
    if (provider) {
      provider.currentAPIKey = activeProvider.defaultApiKey;
    }
    setProviderList([...providerList]);
  }

  const handleApiKeyVerify = async () => {
    sensorsFunc('AI_PLAT_verify_api_key', { provider: JSON.stringify(activeProvider) });
    setVerifyLoading(true);
    const res = await window['bdpStudioAPI'].verifyModelProvider(activeProvider.name);
    if (res) {
      message.success('API KEY 验证成功');
    } else {
      message.error('API KEY 验证失败');
    }
    setVerifyLoading(false);
  }

  const handleApiKeyChange = async (e) => {

    const provider = providerList.find(item => item.name === activeProvider.name);
    if (provider) {
      provider.currentAPIKey = e.target.value;
    }
    setProviderList([...providerList]);
  }

  const handleApiKeyBlur = async (e) => {
    const provider = providerList.find(item => item.name === activeProvider.name);
    sensorsFunc('AI_PLAT_edit_api_key', {
      provider: JSON.stringify(provider),
      apiKey: `${e.target.value}`,
    });
    await window['bdpStudioAPI'].saveProviderCurrentAPIKey(activeProvider.name, e.target.value);
    if (provider) {
      provider.currentAPIKey = e.target.value;
    }
    setProviderList([...providerList]);
  }

  const getModelList = async () => {
    if (!activeProvider) return;
    setLoading(true);
    // modelTag含有FunctionCall的优先显示
    const list = (await api.getModelListByProvider(activeProvider.name)) as Model[];
    const functionCallList = list.filter(item => item.modelTag?.includes('FunctionCall'));
    const otherList = list.filter(item => !item.modelTag?.includes('FunctionCall'));
    setModelList([...functionCallList, ...otherList]);
    setDefaultModel(list.find(item => item.is_default === 1));
    setLoading(false);
  }

  const handleResetApiBaseUrl = async () => {
    const provider = providerList.find(item => item.name === activeProvider.name);
    sensorsFunc('AI_PLAT_reset_api_base_url', {
      provider: JSON.stringify(provider),
      apiBaseUrl: provider.apiBaseUrl,
    });
    await window['bdpStudioAPI'].resetDefaultBaseModelUrl(activeProvider.name);
    if (provider) {
      provider.apiBaseUrl = activeProvider.defaultBaseModelUrl;
    }
    setProviderList([...providerList]);
  }

  const handleApiBaseUrlChange = (e) => {
    const provider = providerList.find(item => item.name === activeProvider.name);
    if (provider) {
      provider.apiBaseUrl = e.target.value;
    }
    setProviderList([...providerList]);
  }


  const handleApiBaseUrlBlur = async (e) => {
    const provider = providerList.find(item => item.name === activeProvider.name);
    sensorsFunc('AI_PLAT_edit_api_base_url', { provider: JSON.stringify(provider), apiBaseUrl: e.target.value });
    if (provider) {
      provider.apiBaseUrl = e.target.value;
    }
    await window['bdpStudioAPI'].updateProvider(activeProvider.name, {
      apiBaseUrl: e.target.value,
    });
    setProviderList([...providerList]);
  }

  useEffect(() => {
    getModelList();
  }, [activeProvider]);

  if (!activeProvider) return null;


  const getModelTag = (model: Model) => {
    return (
      (model.modelTag || '').split(',').filter(Boolean).map((item: string) => {
        const colorMap = {
          'FunctionCall': 'blue',
          '推理模型': 'yellow',
          '多模态': 'green',
          '超长上下文': 'purple',
        };
        return (
          <Tag key={item} color={colorMap[item]} style={{ marginLeft: 'auto', marginRight: 0 }}>
            {item}
          </Tag>
        )
      })
    )
  }

  return (
    <div className='model-config'>
      {/* <div className='model-config-header'>{activeProvider.name} <Switch checked={activeProvider.status === 1} onChange={handleSwitchChange} /></div>
      <Divider style={{ width: '100%', margin: '10px 0' }} /> */}
      <div className='model-config-title'>API 地址</div>
      <div className='model-config-input'>
        <Input
          // defaultValue={activeProvider.apiBaseUrl}
          value={activeProvider.apiBaseUrl}
          placeholder='API 地址'
          size='large'
          style={{ width: '100%', marginTop: '5px', borderRadius: '8px' }}
          disabled={verifyLoading}
          onChange={handleApiBaseUrlChange}
          onBlur={handleApiBaseUrlBlur}
        />
        <Button onClick={handleResetApiBaseUrl} size='large' style={{ marginLeft: '10px' }}>重置</Button>
      </div>
      <div className='model-config-title'>API KEY</div>
      <div className='model-config-input'>
        <Input.Password
          // defaultValue={activeProvider.currentAPIKey || activeProvider.defaultApiKey}
          value={activeProvider.currentAPIKey}
          placeholder='API 密钥'
          visibilityToggle={false}
          size='large'
          onChange={handleApiKeyChange}
          disabled={verifyLoading}
          onBlur={handleApiKeyBlur}
        />
        <Button size='large' style={{ marginLeft: '10px' }} onClick={handleResetApiKey}>重置</Button>
        <Button size='large' type='primary' style={{ marginLeft: '10px' }} onClick={handleApiKeyVerify} loading={verifyLoading}>验证API KEY</Button>
      </div>
      <div className='model-config-title'>模型列表
        <Icon
          type='mcp-web-refresh-grey'
          onClick={
            () => {
              sensorsFunc('AI_PLAT_refresh_model_list', {provider: JSON.stringify(activeProvider) });
              getModelList();
            }}
        />
      </div>
      <div className='model-config-list' style={{ maxHeight: `calc(100vh - ${isWindows ? 440 : 400}px)` }}>
        <Spin spinning={loading}>
          {
            modelList.map((model: Model) => (
              <div key={model.id} className='model-config-item'>
                <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: 8, maxWidth: 'calc(100% - 50px)' }}>
                  {model.logo && <div className='model-logo-wrapper'><img src={model.logo} /></div>}
                  <div className='model-config__ellipsis-text'>{model.model}</div>
                  <div className='model-config__model-tag'>{getModelTag(model)}</div>
                </div>
                <Switch checked={model.enabled === 1} onChange={checked => enableModel(model.id, checked)} disabled={verifyLoading} />
              </div>
            ))
          }
        </Spin>
      </div>
      <div className='model-config-title'>默认模型配置</div>
      <div className='model-config-default'>
        <Select
          value={defaultModel?.id}
          style={{ width: '100%' }}
          size='large'
          onChange={handleDefaultModelChange}
          disabled={verifyLoading}
          showSearch
          optionFilterProp='label'
          filterOption={(input, option) => {
            return (option?.label as string)?.toLowerCase().includes(input.toLowerCase());
          }}
          suffixIcon={<Icon type='mcp-web-arrow-down-grey' />}
          popupClassName='model-config-default__select-popup'
        >
          {
            modelList.filter(item => item.enabled === 1).map(item => (
              <Select.Option key={item.id} value={item.id} label={item.model}>
                {item.logo && <div className='model-logo-wrapper'><img src={item.logo} /></div>}
                <div className='model-config__ellipsis-text'>{item.model}</div>
                {item.modelTag === 'function_call' && <ToolOutlined />}
              </Select.Option>
            ))
          }
        </Select>
      </div>
    </div>
  );
}

export default ModelConfig;