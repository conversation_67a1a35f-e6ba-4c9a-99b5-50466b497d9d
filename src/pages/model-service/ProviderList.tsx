import React, { useEffect, useState, useContext, useMemo } from 'react';
import { api } from '@/services/api';
import { Input } from 'antd';
import { Button, Tag } from '@bdpfe/components';
import { PlusOutlined } from '@ant-design/icons';
import { Provider } from '@/common/types';
import { ModelServiceListContext } from '@/context';


const ProviderList: React.FC = () => {
  const { providerList, setProviderList, activeProviderId, setActiveProviderId, setLoading } = useContext(ModelServiceListContext);
  const [searchValue, setSearchValue] = useState<string>('');
  // const [filteredProviderList, setFilteredProviderList] = useState<Provider[]>([]);
  const [draggedItem, setDraggedItem] = useState<Provider | null>(null);

  // const enabledProviderListId = useMemo(() => enabledProviderList.map(item => item.id), [enabledProviderList]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
    onSearch(e.target.value);
  }

  const onSearch = (value: string) => {
    // setFilteredProviderList(providerList.filter(item => item.name.toLowerCase().includes(value.toLowerCase())));
  }

  const handleDragStart = (item: Provider) => {
    setDraggedItem(item);
  };

  const handleDragOver = (e, targetItem: Provider) => {
    e.preventDefault();
    if (!draggedItem || draggedItem.id === targetItem.id) return;

    const draggedIndex = providerList.findIndex(item => item.id === draggedItem.id);
    const targetIndex = providerList.findIndex(item => item.id === targetItem.id);

    const newItems = [...providerList];
    newItems.splice(draggedIndex, 1);
    newItems.splice(targetIndex, 0, draggedItem);

    setProviderList(newItems);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
  };

  const handleActiveProvider = (provider: Provider) => {
    setActiveProviderId(provider.id);
  }

  const filteredProviderList = useMemo(() => {
    if (searchValue) {
      return providerList.filter(item => item.name.toLowerCase().includes(searchValue.toLowerCase()));
    } else {
      return providerList;
    }
  }, [providerList, searchValue]);

  return (
    <div className='provider-list'>
      <div className='provider-list__search'>
        <Input.Search placeholder='搜索模型平台...' size='large' value={searchValue} onSearch={onSearch} onChange={onChange}/>
      </div>
      <div className='provider-list__content'>
        {filteredProviderList?.map((provider: Provider) => (
          <div
            key={provider.id}
            className={`provider-list__item bdp-studio__list-item drag-item ${activeProviderId === provider.id ? 'bdp-studio__list-item--active' : ''}`}
            onClick={() => handleActiveProvider(provider)}
            draggable={!!searchValue}
            onDragStart={() => handleDragStart(provider)}
            onDragOver={(e) => handleDragOver(e, provider)}
            onDragEnd={handleDragEnd}
          >
            {provider.name}
            {provider.status === 1 && <Tag color='blue' style={{ marginLeft: 'auto', marginRight: 0, borderRadius: '16px' }}>ON</Tag>}
          </div>
        ))}
      </div>
      <div className='provider-list__add'>
        <Button size='large'><PlusOutlined />添加</Button>
      </div>
    </div>
  )
}

export default ProviderList;