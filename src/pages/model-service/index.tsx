import React, { useEffect, useState } from 'react';
import ProviderList from './ProviderList';
import ModelConfig from './ModelConfig';
import './index.less';
import { ModelServiceListContext } from '@/context';
import { Spin } from 'antd';
import { Provider } from '@/common/types';
import { api } from '@/services/api';

const ModelService: React.FC = () => {
  const [providerList, setProviderList] = useState<Provider[]>([]);
  const [activeProviderId, setActiveProviderId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const getProviderList = async () => {
      const list = (await api.getModelProviderList()) as Provider[];
      setProviderList(list);
      setActiveProviderId(list[0].id);
    }
    getProviderList();
  }, []);

  return (
    <ModelServiceListContext.Provider value={{
      providerList,
      setProviderList,
      activeProviderId,
      setActiveProviderId,
      loading,
      setLoading,
    }}>
      <div className='model-service'>
        <div className='model-service__title'>模型配置</div>
        <ModelConfig />
      </div>
    </ModelServiceListContext.Provider>
  )
}

export default ModelService;