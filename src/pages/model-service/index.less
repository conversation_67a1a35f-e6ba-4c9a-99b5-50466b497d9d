.model-service {
  display: flex;
  flex-direction: column;
  -webkit-app-region: drag;

  .ant-spin-nested-loading {
    width: 100%;
    height: calc(100vh - 54px);
  }

  .ant-spin-container {
    height: 100%;
  }
}

.model-service__title {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #1b1c1f;
  padding: 12px 0 0 20px;
}

.provider-list {
  display: flex;
  flex-direction: column;
  min-width: 260px;
  height: calc(100vh - 54px);
  border-right: 0.5px solid #00000019;
}

.provider-list__search {
  height: 50px;
  padding: 10px 8px;
}

.provider-list__content {
  flex: 1;
  overflow-y: auto;

  display: flex;
  flex-direction: column;
  padding: 8px 5px 8px 8px;
}

.provider-list__item {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  cursor: grab;
  font-size: 14px;
  transition: all 0.2s ease-in-out 0s;
  margin-bottom: 5px;
}

.drag-item {
  cursor: grab;
}

.provider-list__add {
  padding: 10px 8px;
  height: 50px;

  .ant-btn {
    width: 100%;
    border-radius: 16px;
  }
}

.model-config {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 70%;
  padding: 0 20px 20px;
  -webkit-app-region: no-drag;

  .ant-input-affix-wrapper {
    border-radius: 8px;
  }

  .ant-btn {
    border-radius: 8px;
  }
}

.model-config-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  font-size: 14px;
  font-weight: bold;
}

.model-config-title {
  font-size: 14px;
  margin: 24px 0px 8px;
  user-select: none;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;

  >svg {
    font-size: 14px;
    color: #797c8e;
    margin-right: 10px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.model-config-input.ant-input-group.ant-input-group-compact {
  margin-top: 5px;
  width: 100%;
  display: inline-flex;

  > :first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-color: transparent;
    background-color: #fff;

    .ant-input {
      background-color: #fff;
    }

    &:hover,
    &:focus {
      border-color: #5c7cff;
    }

    &:active {
      border-color: #3355ff;
      box-shadow: none;
    }
  }

  .ant-btn {

    &:hover,
    &:focus,
    &:active {
      &+.ant-btn {
        border-left-color: #5c7cff;
      }
    }
  }

  .ant-btn:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.model-config-tip {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px 0px;
  justify-content: space-between;
}

.model-config-tip__link {
  color: #1677ff;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s;
  border: 0;
  padding: 0;
  background: none;
  user-select: text;
  font-size: 11px;
  margin: 0px 5px;
}

.model-config-tip__text {
  font-size: 11px;
  color: rgba(0, 0, 0, 1);
  opacity: 0.4;
}

.model-config-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 5px 16px;
  border: 1px solid #ebeef9;
  border-radius: 8px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.model-config-item {
  font-size: 14px;
  padding: 8px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ant-switch {
    background-color: #dfe4f0;
  }

  .ant-switch-checked {
    background-color: #3570ff;
  }
}

.model-logo-wrapper {
  border: 1px solid #ebeef9;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  img {
    width: 24px;
    height: 24px;
  }
}

.model-config__ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-config__model-tag {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.model-config-default__select-popup {
  .ant-select-item-option-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.model-config-default {
  .ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.model-config-input {
  display: flex;
  align-items: center;

  >.ant-input-password {
    max-width: calc(100% - 70px);
  }
}