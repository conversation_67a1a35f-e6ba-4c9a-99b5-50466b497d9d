import React, { useEffect, useState, useRef } from 'react';
import { api } from '@/services/api';
import './index.less';
import { login } from '../login';
import { Button } from 'antd';
import Icon from '@/components/MCPIcon';
import minimizeIcon from '@/assets/image/minimize.svg';
import maximizeIcon from '@/assets/image/maximize.svg';
import closeIcon from '@/assets/image/close-window.svg';
import { useMemoizedFn } from 'ahooks';

// let isLogin = false;

const QRCodeLogin: React.FC = () => {
  const [qrCode, setQrCode] = useState<{
    imgId: string;
    imgUrl: string;
  }>({
    imgId: '',
    imgUrl: '',
  });
  const [qrcodeTime, setQrcodeTime] = useState<number>(Date.now());
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const isExpiredRef = useRef<boolean>(false);

  const timerRef = useRef<NodeJS.Timeout>();
  const expireTimerRef = useRef<NodeJS.Timeout>();

  const isWindows = window.bdpStudioAPI.isWindows();

  // 获取二维码的函数
  const fetchQrCode = async () => {
    try {
      setIsLoading(true);
      const res = await api.getQrCode();
      setQrCode(res);
      setQrcodeTime(Date.now());
      setIsExpired(false);
    } catch (error) {
      console.error('获取二维码失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理二维码点击刷新
  const handleQrCodeClick = () => {
    if (isExpired || isLoading) {
      updateQrcode();
    }
  };

  const updateQrcode = useMemoizedFn(() => {
    clearTimeout(expireTimerRef.current);
    clearInterval(timerRef.current);
    fetchQrCode().then(() => {
      // 设置30秒后过期
      expireTimerRef.current = setTimeout(() => {
        setIsExpired(true);
        clearInterval(timerRef.current);
        clearTimeout(expireTimerRef.current);
      }, 30 * 1000);

      // 开始轮询状态
      timerRef.current = setInterval(() => {
        if (isExpiredRef.current) {
          return;
        }
        window['bdpStudioAPI'].getQrCodeStatus().then((res) => {
          if (res.success) {
            // 开始登录写入用户
            window['bdpStudioAPI'].saveUser({
              userId: res.data.userCode,
              userName: res.data.userName,
              session: res.data.token,
              id: res.data.id,
            }).then(async (_) => {
              // 登录成功，清除所有定时器
              clearInterval(timerRef.current);
              clearTimeout(expireTimerRef.current);
              await window['bdpStudioAPI'].init();
              login();
            });
          } else {
            // 继续轮询
          }
        });
      }, 3000);
    });
  });



  useEffect(() => {
    // 初始获取二维码
    updateQrcode();
    // 清理函数
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (expireTimerRef.current) clearTimeout(expireTimerRef.current);
    };
  }, []);

  useEffect(() => {
    isExpiredRef.current = isExpired;
  }, [isExpired]);

  return <div className='login' style={{ paddingTop: isWindows ? 40 : 0 }}>
    <div className='login__header' style={{ display: isWindows ? 'flex' : 'none' }}>
      <div className='login__header--icon' onClick={() => window.bdpStudioAPI.minimize()}><img src={minimizeIcon} /></div>
      <div className='login__header--icon' onClick={() => isWindows ? window.bdpStudioAPI.maximize() : window.bdpStudioAPI.fullscreen()}><img src={maximizeIcon} /></div>
      <div className='login__header--icon' onClick={() => window.bdpStudioAPI.close()}><img src={closeIcon} /></div>
    </div>
    <div className='login-wrapper'>
      <div className='qrcode-title'>扫码登录</div>
      <div className='qrcode-desc'>手机丰声扫一扫登录</div>
      <div className="qrcode-container" onClick={handleQrCodeClick}>
        {qrCode.imgUrl && (
          <img
            width={220}
            height={220}
            src={'data:image/png;base64,' + qrCode.imgUrl}
            className="qrcode-img"
          />
        )}
        {isExpired && (
          <div className="qrcode-overlay">
            <div className="overlay-content">
              <div className="overlay-text">二维码已失效</div>
              <Button type='primary'><Icon type='mcp-web-refresh-grey' style={{ fontSize: 14, marginRight: 3 }} onClick={handleQrCodeClick} />点击刷新</Button>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="qrcode-loading">
            <div className="loading-text">正在刷新...</div>
          </div>
        )}
      </div>
    </div>
  </div>;
}

export default QRCodeLogin;
