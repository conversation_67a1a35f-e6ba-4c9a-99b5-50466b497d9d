.login {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-image: url('@/assets/image/login-background.png');
  background-size: 100% 100%;
  background-color: #fff;
  background-repeat: no-repeat;
}

.login__header {
  height: 40px;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  -webkit-app-region: drag;
  position: absolute;
  top: 0;
}

.login__header--icon {
  -webkit-app-region: no-drag;
  width: 40px;
  height: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  &:hover {
    opacity: 0.8;
  }
  img {
    width: 12px;
    height: 12px;
  }
}

.login-wrapper {
  border-radius: 12px;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 100%);
  box-sizing: border-box;
  border: 2px solid #ffffff;
  backdrop-filter: blur(10px);
  box-shadow: 0px 4px 10px 0px #d6e5f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: calc(25% - 200px);

  width: 400px;
  height: 480px;

  .qrcode-title {
    font-size: 24px;
    font-weight: 600;
    color: #1b1c1f;
    margin-bottom: 15px;
  }
  .qrcode-desc {
    font-size: 16px;
    line-height: 24px;
    color: #797c8e;
    margin-bottom: 40px;
  }
}

// 二维码容器样式
.qrcode-container {
  position: relative;
  width: 220px;
  height: 220px;
  cursor: pointer;
  margin-bottom: 40px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px 0px #d6e5f7;

  .qrcode-img {
    width: 220px !important;
    height: 220px !important;
    border-radius: 8px;
  }

  // 过期蒙层样式
  .qrcode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);

    .overlay-content {
      text-align: center;
      color: white;

      .ant-btn-primary {
        border-radius: 8px;
        background: #3570ff;
        border: 1px solid #3570ff;
        &:hover {
          opacity: 0.8;
        }
      }

      .overlay-text {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #797c8e;
        margin-bottom: 18px;
      }

      .overlay-refresh {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }

  // 加载状态样式
  .qrcode-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;

    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }

  // 鼠标悬停效果
  &:hover {
    .qrcode-overlay {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(5px);
    }
  }
}
