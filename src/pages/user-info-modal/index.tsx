import React, { useEffect, useState } from 'react';
import './index.less';
import { UserOutlined } from '@ant-design/icons';
import { Modal, Button } from '@bdpfe/components';
import { PageContext } from '@/context';
import { Avatar } from 'antd';
import { logout } from '@/pages/logout';


const UserInfoModal: React.FC = () => {
  const { open, setOpen, userInfo } = React.useContext(PageContext);

  return (
    <Modal
      open={open}
      onCancel={() => setOpen(false)}
      className='login-modal'
      width={300}
      footer={null}
    >
      <div className='login-modal__avatar'>
        <Avatar icon={<UserOutlined />} />
      </div>
      {userInfo && <div className='login-modal__user-name'>{userInfo?.userName}</div>}
      <div className='login-modal__button'>
        <Button onClick={logout}>退出</Button>
      </div>
    </Modal>
  );
}

export default UserInfoModal;
