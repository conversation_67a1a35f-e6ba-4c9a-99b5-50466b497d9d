import React, { useEffect, useState, useRef } from 'react';
import { PageContext, NewVersionInfo, ProgressInfo } from '@/context';
import { message } from '@bdpfe/components';
import ChatStudio from './chat-studio';
import SideBar from '@/components/SideBar';
import Setting from './setting';
import ModelService from './model-service';
import MCPServers from './mcp-servers';
import logo from '@/assets/image/logo-assistant.svg';
import minimizeIcon from '@/assets/image/minimize.svg';
import maximizeIcon from '@/assets/image/maximize.svg';
import closeIcon from '@/assets/image/close-window.svg';
import './index.less';
import { api } from '@/services/api';
import { useMemoizedFn } from 'ahooks';
import { User } from '@/services/types';
import UserInfoModal from './user-info-modal';
import { Server } from '@/common/types';
import { logout } from './logout';
import { parseMcpUrl } from '@/utils/mcpUrlParser';

interface UpdateInfo {
  version: string;
  files?: any[];
  path?: string;
  sha512?: string;
  releaseDate?: string;
}

export function isFailedResult(data: any): data is {
  success: boolean;
  errorMsg: string
} {
  if (data.success === false) {
    return true;
  }
  return false;
}

const IndexPage: React.FC<any> = (props) => {
  const [activePage, setActivePage] = useState('CHAT_STUDIO');
  const [loadedPages, setLoadedPages] = useState<Set<string>>(new Set(['CHAT_STUDIO'])); // 记录已加载的页面，默认加载首页
  const [open, setOpen] = useState(false);
  const [user, setUser] = useState<User>(null);
  const [serverList, setServerList] = useState<Server[]>([]);
  const [downloadProgress, setDownloadProgress] = useState<ProgressInfo | null>(null);
  const [downloaded, setDownloaded] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [hasNewVersion, setHasNewVersion] = useState(false);
  const [newVersionInfo, setNewVersionInfo] = useState<NewVersionInfo>(false);
  const serverListRef = useRef(serverList);

  // SharedWorker 引用
  const timerWorkerRef = useRef<SharedWorker | null>(null);

  const isWindows = window.bdpStudioAPI.isWindows();

  const _sensorsFunc = useMemoizedFn(((eventId: string, props?: any) => {
    const sensorsProps = {};
    if (props && typeof props === 'object') {
      Object.keys(props).forEach(key => {
        if (typeof props[key] === 'string') {
          sensorsProps[key] = props[key];
        }
      });
    }
    window.bdpStudioAPI.sensors(eventId, sensorsProps);
  }));

  // 监听activePage变化，将新页面添加到已加载页面集合中
  useEffect(() => {
    setLoadedPages(prev => new Set([...prev, activePage]));
  }, [activePage]);

  // 初始化SharedWorker定时器
  useEffect(() => {
    // 创建SharedWorker实例
    try {
      timerWorkerRef.current = new SharedWorker(new URL('../workers/timerWorker', import.meta.url), {
        type: 'module'
      });
    } catch (error) {
      console.error('创建SharedWorker失败:', error);
      return;
    }

    if (!timerWorkerRef.current) return;

    const port = timerWorkerRef.current.port;

    // 设置消息监听器
    port.onmessage = async (event) => {
      const { type, timestamp, message } = event.data;

      switch (type) {
        case 'WORKER_READY':
          console.log('SharedWorker 准备就绪:', message);
          break;

        case 'TIMER_TICK':
          console.log('定时器触发:', {
            timestamp: new Date(timestamp).toLocaleString(),
            message
          });
          const user = await api.getUser();
          if (user && typeof user === 'object') {
            const token = await api.checkToken(user.session);
            console.log(token);
            if (!token) {
              logout();
            }
          }
          // 这里可以添加你需要的业务逻辑
          // 例如：检查更新、刷新数据等
          break;

        case 'TIMER_STARTED':
          console.log('定时器已启动:', message);
          break;

        case 'TIMER_STOPPED':
          console.log('定时器已停止:', message);
          break;

        default:
          console.log('收到未知消息:', event.data);
      }
    };

    // 错误处理
    port.onmessageerror = (error) => {
      console.error('SharedWorker 消息错误:', error);
    };

    timerWorkerRef.current.onerror = (error) => {
      console.error('SharedWorker 错误:', error);
    };

    // 启动端口
    port.start();

    // 清理函数
    return () => {
      if (timerWorkerRef.current) {
        // 停止定时器
        timerWorkerRef.current.port.postMessage({ type: 'STOP_TIMER' });
        // 关闭端口
        timerWorkerRef.current.port.close();
        timerWorkerRef.current = null;
      }
    };
  }, []);

  const checkUpdate = async () => {
    const result = await api.updater.checkForUpdates();
    const latest = await api.checkLatestVersion();
    // const currAppVersion = await api.getAppVersion();
    setNewVersionInfo(latest ? {
      version: latest.version,
      url: latest.url,
      content: latest.desc,
      isForce: latest.is_force === 1,
    } : false);
    setHasNewVersion(!!latest);
    if (latest && latest.is_force !== 1 && result && result.isUpdateAvailable) {
      // 如果是windows就执行静默下载
      if (window.bdpStudioAPI.isWindows()) {
        api.updater.downloadUpdate();
      }
    }
  }

  useEffect(() => {
    checkUpdate();
    const uuid = Math.random().toString(36).substring(2, 15);
    // api.onLogout(uuid, logout);
    api.updater.onDownloadProgress((progress: ProgressInfo) => {
      setDownloading(true);
      setDownloadProgress(progress);
    });
    api.updater.onUpdateDownloaded((info: UpdateInfo) => {
      setDownloadProgress(null);
      setDownloaded(true);
      setDownloading(false);
    });
    api.updater.onError(() => {
      setDownloading(false);
      setDownloaded(false);
      setDownloadProgress(null);
    });
    return () => {
      api.offLogout(uuid);
    }
  }, []);

  useEffect(() => {
    serverListRef.current = serverList;
  }, [serverList]);

  useEffect(() => {
    const init = async () => {

      const providerList = await api.getModelProviderList();
      if (providerList) {
        api.getModelListByProvider(providerList[0].name);
      }
      const user = await api.getUser();

      if (typeof user === 'object') {
        api.getDefaultApiKey();
        setUser({
          id: 0,
          userId: user.userId,
          userName: user.userName,
          session: user.session,
        });
      }
      const mcpList = (await window['bdpStudioAPI'].getMcpList()) as Server[];
      setServerList(mcpList);

      setTimeout(() => {
        handlePendingMcpUrl();
      }, 1000);
    }

    init();

     // 处理待处理的MCP URL
    const handlePendingMcpUrl = async () => {
      try {
        // 获取待处理的URL
        const pendingUrl = await window.bdpStudioAPI.getPendingUrl();
        if (!pendingUrl) {
          return; // 没有待处理URL
        }

        console.log('处理待处理的MCP URL:', pendingUrl);

        // 解析URL
        const parseResult = parseMcpUrl(pendingUrl);
        if (!parseResult.success) {
          message.error(`解析MCP配置失败: ${parseResult.error}`);
          await window.bdpStudioAPI.clearPendingUrl();
          return;
        }

        // 调用保存接口
        const saveResult = await window.bdpStudioAPI.saveMcp(parseResult.data);
        console.log(saveResult);

        if (!isFailedResult(saveResult)) {
          message.success('MCP配置已成功保存');

          // 刷新MCP服务器列表
          const updatedMcpList = await window.bdpStudioAPI.getMcpList();
          const listWithLoading = updatedMcpList.map(server => ({
            ...server,
            serverStatus: server.serverStatus as (0 | 1),
            loading: serverList.find(item => item.id === server.id)?.loading || false
          }));
          setServerList(listWithLoading);

        } else {
          message.error('MCP配置保存失败: ' + saveResult.errorMsg);
        }
        // 清空待处理URL
        await window.bdpStudioAPI.clearPendingUrl();
      } catch (error) {
        console.error('处理MCP URL失败:', error);
        message.error('处理MCP配置时发生错误');
        // 清空有问题的URL
        await window.bdpStudioAPI.clearPendingUrl();
      }
    };

    // 组件加载时检查待处理URL
    // 监听URL接收事件
    const handleMcpUrlReceived = (result: { hasPendingUrl: boolean; url: string }) => {
      console.log('接收到MCP URL事件:', result);
      if (result.hasPendingUrl) {
        // 延迟一下处理，确保状态已更新
        setTimeout(() => {
          handlePendingMcpUrl();
        }, 500);
      }
    };

    window.bdpStudioAPI.onMcpUrlReceived(handleMcpUrlReceived);

    // 组件卸载时移除监听
    return () => {
      window.bdpStudioAPI.removeMcpUrlListener();
    };

  }, []);



  return (
    <PageContext.Provider value={{
      serverList,
      setServerList,
      activePage,
      setActivePage,
      userInfo: user,
      open,
      setOpen,
      sensorsFunc: _sensorsFunc,
      downloadProgress,
      downloaded,
      downloading,
      hasNewVersion,
      newVersionInfo,
    }}>
      <div className='studio-container'>
        <div className='studio-container__side'>
          <SideBar />
        </div>
        <div className='studio-container__wrapper'>
          <div className='studio-container__header' style={ isWindows ? { display: 'flex', marginLeft: -60 } : { display: 'none' }}>
            <div className='studio-container__header--title'><img src={logo} /></div>
            <div className='studio-container__header--extra'>
              <div className='studio-container__header--icon' onClick={() => window.bdpStudioAPI.minimize()}><img src={minimizeIcon} /></div>
              <div className='studio-container__header--icon' onClick={() => isWindows ? window.bdpStudioAPI.maximize() : window.bdpStudioAPI.fullscreen()}><img src={maximizeIcon} /></div>
              <div className='studio-container__header--icon' onClick={() => window.bdpStudioAPI.close()}><img src={closeIcon} /></div>
            </div>
          </div>
          <div className="bdp-studio" style={{ height: isWindows ? 'calc(100% - 40px)' : '100%' }}>
            <div className="bdp-studio__content">
              {loadedPages.has('CHAT_STUDIO') && (
                <div className='bdp-studio__page' style={{ display: activePage === 'CHAT_STUDIO' ? 'block' : 'none' }}>
                  <ChatStudio />
                </div>
              )}
              {loadedPages.has('MODEL_SERVICE') && (
                <div className='bdp-studio__page' style={{ display: activePage === 'MODEL_SERVICE' ? 'block' : 'none' }}>
                  <ModelService />
                </div>
              )}
              {loadedPages.has('MCP_SERVER') && (
                <div className='bdp-studio__page' style={{ display: activePage === 'MCP_SERVER' ? 'block' : 'none' }}>
                  <MCPServers />
                </div>
              )}
              {loadedPages.has('SETTING') && (
                <div className='bdp-studio__page' style={{ display: activePage === 'SETTING' ? 'block' : 'none' }}>
                  <Setting />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {open && <UserInfoModal />}
    </PageContext.Provider>
  );
};

export default IndexPage;
