.setting-menu {
  display: flex;
  flex-direction: column;
  min-width: 250px;
  border-right: 0.5px solid #ebeef9;
  padding: 16px;
  gap: 4px;
  margin-bottom: 0;
}

.setting-menu__item {
  margin-bottom: 5px;
  > li {
    padding: 9px 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    color: #2c2d33;
    font-size: 14px;
    border: 1px solid transparent;
    &:hover {
      color: #3570ff;
      background: #ebf1ff;
      border-color: #ccdbff;
    }
  }
}
.setting-menu__item--active {
  > li {
    color: #3570ff;
    background: #ebf1ff;
    border-color: #ccdbff;
    &:hover {
      opacity: 0.8;
    }
  }
}

.setting-menu__item--title {
  display: flex;
  align-items: center;
  gap: 8px;
}
