import React, { useContext, useEffect, useState } from 'react';
import './index.less';
import classNames from 'classnames';
import { PageContext, SettingContext } from '@/context';
import environmentConfig from '@/assets/image/env-config-grey.svg';
import environmentConfigLight from '@/assets/image/env-config-blue.svg';
import info from '@/assets/image/info-grey.svg';
import infoLight from '@/assets/image/info-blue.svg';
import newVersion from '@/assets/image/new-version.svg';
import { api } from '@/services/api';

const SettingMenu: React.FC = () => {
  const { activeMenu, setActiveMenu } = useContext(SettingContext);
  const { hasNewVersion } = useContext(PageContext);
  const cls = (menu: string) => classNames('setting-menu__item ', {
    'setting-menu__item--active': activeMenu === menu
  });

  return (
    <ul className='setting-menu'>
      <a className={cls('ENVIRONMENT_CONFIG')} onClick={() => setActiveMenu('ENVIRONMENT_CONFIG')}>
        <li>
          <div className='setting-menu__item--title'><img src={activeMenu === 'ENVIRONMENT_CONFIG' ? environmentConfigLight : environmentConfig} />环境配置</div>
        </li>
      </a>
      <a className={cls('VERSION')} onClick={() => setActiveMenu('VERSION')}>
        <li>
          <div className='setting-menu__item--title'><img src={activeMenu === 'VERSION' ? infoLight : info} />关于</div>
          {hasNewVersion && <img src={newVersion} />}
        </li>
      </a>
    </ul>
  )
}

export default SettingMenu;
