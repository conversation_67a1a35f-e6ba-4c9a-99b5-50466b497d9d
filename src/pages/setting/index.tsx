import React, { useState } from 'react';
import { SettingContext, ActiveMenu } from '@/context';
import SettingMenu from './setting-menu';
import EnvironmentConfig from './environment-config';
import Version from './version';
import './index.less';

const Setting: React.FC = () => {
  const [activeMenu, setActiveMenu] = useState<ActiveMenu>('ENVIRONMENT_CONFIG');
  return (
    <SettingContext.Provider value={{ activeMenu, setActiveMenu }}>
      <div className='mcp-setting'>
        <div className='mcp-setting__title'>设置</div>
        <div style={{ display: 'flex', height: '100%' }}>
        <SettingMenu />
          {activeMenu === 'ENVIRONMENT_CONFIG' && <div className='mcp-setting__page'><EnvironmentConfig /></div>}
          {activeMenu === 'VERSION' && <div className='mcp-setting__page'><Version /></div>}
        </div>
      </div>
    </SettingContext.Provider>
  )
}

export default Setting;