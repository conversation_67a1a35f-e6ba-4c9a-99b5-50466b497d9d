import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from '@bdpfe/components';
import { api } from '@/services/api';
import successIcon from '@/assets/image/success-status.svg';
import './index.less';

const EnvironmentConfig: React.FC = () => {
  const [isSettingUV, setIsSettingUV] = useState(false);
  const [isSettingNPX, setIsSettingNPX] = useState(false);
  const [isSettingNodejs, setIsSettingNodejs] = useState(false);
  const [isInstallUVLoading, setIsInstallUVLoading] = useState(false);
  const [isInstallNPXLoading, setIsInstallNPXLoading] = useState(false);
  const [isInstallNodejsLoading, setIsInstallNodejsLoading] = useState(false);
  const [isInstallMset, setIsInstallMset] = useState(false);

  const [uvPath, setUvPath] = useState('');
  const [npxPath, setNpxPath] = useState('');
  const [nodejsPath, setNodejsPath] = useState('');
  const [msetPath, setMsetPath] = useState('');
  const [isInstallMsetLoading, setIsInstallMsetLoading] = useState(false);

  useEffect(() => {
    api.getBunStatus().then(res => {
      setIsSettingNPX(res);
      if (res) {
        api.getBunPath().then(res => {
          setNpxPath(res);
        });
      }
    });
    api.getUvStatus().then(res => {
      setIsSettingUV(res);
      if (res) {
        api.getUvPath().then(res => {
          setUvPath(res);
        });
      }
    });
    api.getNodejsStatus().then(res => {
      setIsSettingNodejs(res);
      if (res) {
        api.getNodejsPath().then(res => {
          setNodejsPath(res);
        });
      }
    });
    api.getMsetStatus().then(res => {
      setIsInstallMset(res);
      if (res) {
        api.getMsetPath().then(res => {
          setMsetPath(res);
        });
      }
    });
  }, []);
  const installNPX = async () => {
    setIsInstallNPXLoading(true);
    const res = await api.installBun();
    if (res) {
      setIsSettingNPX(true);
      api.getBunPath().then(res => {
        setNpxPath(res);
      });
    }
    setIsInstallNPXLoading(false);
  }
  const installUV = async () => {
    setIsInstallUVLoading(true);
    const res = await api.installUv();
    if (res) {
      setIsSettingUV(true);
      api.getUvPath().then(res => {
        setUvPath(res);
      });
    }
    setIsInstallUVLoading(false);
  }

  const installNodejs = async () => {
    setIsInstallNodejsLoading(true);
    const res = await api.installNodejs();
    if (res) {
      setIsSettingNodejs(true);
      api.getNodejsPath().then(res => {
        setNodejsPath(res);
      });
    }
    setIsInstallNodejsLoading(false);
  }

  const installMset = async () => {
    setIsInstallMsetLoading(true);
    const res = await api.installMset();
    if (res) {
      setIsInstallMset(true);
      api.getMsetPath().then(res => {
        setMsetPath(res);
      });
    }
  }
  return (
    <div className='environment-config'>
      {/* <div>
        <div style={{ margin: '10px' }}>
          {!isSettingNPX ? <Alert
            showIcon type='error' message="NPX 环境变量未设置"
            action={
              <Button loading={isInstallNPXLoading} onClick={installNPX}>安装npx</Button>
            }
          >

          </Alert>
            : <Alert
              showIcon
              type='success'
              message="NPX 环境变量已设置"
              description={`${npxPath}`}
            />

          }
        </div>
        <div style={{ margin: '10px' }}>
          {!isSettingUV ? <Alert showIcon type='error' message="UV 环境变量未设置"
            action={
              <Button type='primary' loading={isInstallUVLoading} onClick={installUV}>安装uv</Button>
            }
          >
          </Alert> : <Alert showIcon type='success' message="UV 环境变量已设置" description={`${uvPath}`} />}
        </div>
      </div> */}

      { (isSettingNPX || isSettingUV || isSettingNodejs || isInstallMset) && <div className='environment-config__env-list'>
        <div className='environment-config__env-list--title'>我的环境</div>
        {/* {
          isSettingNPX && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>NPX</div>
              <div className='environment-config__env-item--path' title={npxPath}>{npxPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <img src={successIcon} />
              <div className='environment-config__env-item--status'>安装成功</div>
            </div>
          </div>
        } */}
        {/* {
          isSettingUV && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>UV</div>
              <div className='environment-config__env-item--path' title={uvPath}>{uvPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <img src={successIcon} />
              <div className='environment-config__env-item--status'>安装成功</div>
            </div>
          </div>
        } */}
        {/* {
          isSettingNodejs && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>Node.js</div>
              <div className='environment-config__env-item--path' title={nodejsPath}>{nodejsPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <img src={successIcon} />
              <div className='environment-config__env-item--status'>安装成功</div>
            </div>
          </div>
        } */}
        {
          isInstallMset && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>MSET</div>
              <div className='environment-config__env-item--path'>{msetPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <img src={successIcon} />
              <div className='environment-config__env-item--status'>安装成功</div>
            </div>
          </div>
        }
      </div>}

      { (!isSettingNPX || !isSettingUV || !isSettingNodejs || !isInstallMset) && <div className='environment-config__env-list'>
        <div className='environment-config__env-list--title'>其他依赖包</div>
        {/* {
          !isSettingNPX && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>NPX</div>
              <div className='environment-config__env-item--path'>{npxPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <Button type='primary' loading={isInstallNPXLoading} onClick={installNPX}>安装</Button>
            </div>
          </div>
        } */}
        {/* {
          !isSettingUV && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>UV</div>
              <div className='environment-config__env-item--path'>{uvPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <Button type='primary' loading={isInstallUVLoading} onClick={installUV}>安装</Button>
            </div>
          </div>
        } */}
        {/* {
          !isSettingNodejs && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>Node.js</div>
              <div className='environment-config__env-item--path'>{nodejsPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <Button type='primary' loading={isInstallNodejsLoading} onClick={installNodejs}>安装</Button>
            </div>
          </div>
        } */}

        {
          !isInstallMset && <div className='environment-config__env-item'>
            <div className='environment-config__env-item--left'>
              <div className='environment-config__env-item--name'>MSET</div>
              <div className='environment-config__env-item--path'>{msetPath}</div>
            </div>
            <div className='environment-config__env-item--right'>
              <Button type='primary' loading={isInstallMsetLoading} onClick={installMset}>安装</Button>
            </div>
          </div>
        }

      </div>}


    </div>
  );
};

export default EnvironmentConfig;
