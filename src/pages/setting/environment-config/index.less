.environment-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px;
}

.environment-config__env-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 0;
}

.environment-config__env-list--title {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: #1b1c1f;
}

.environment-config__env-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #ebeef9;
}

.environment-config__env-item--left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.environment-config__env-item--name {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #2c2d33;
}

.environment-config__env-item--path {
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #797c8e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.environment-config__env-item--right {
  display: flex;
  align-items: center;
  gap: 4px;

  .ant-btn {
    width: 64px;
    border-radius: 8px;
  }
}

.environment-config__env-item--status {
  font-size: 12px;
  color: #28C58E;
}