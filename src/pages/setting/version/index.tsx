import React, { useState, useEffect, useContext } from 'react';
import logo from '@/assets/image/logo-assistant.svg';
import { Progress, Typography } from 'antd';
import { Button, Collapse, message, Modal } from '@bdpfe/components';
import collapseIcon from '@/assets/image/collapse.svg';
import { api } from '@/services/api';
import './index.less';
import { PageContext } from '@/context';

const { Text } = Typography;


const Version: React.FC = () => {
  const { downloaded, downloadProgress, newVersionInfo } = useContext(PageContext);
  const [appVersion, setAppVersion] = useState('');

  const updateLog = [];

  const getAppVersion = async () => {
    const version = await api.getAppVersion();
    setAppVersion(version);
  }

  const handleUpdate = () => {
    api.updater.quitAndInstall();
  }

  useEffect(() => {
    getAppVersion();
  }, []);


  const handleCheckUpdate = async () => {
    const result = await api.updater.checkForUpdates();
    if (result && result.isUpdateAvailable) {
      message.info('已检测到新版本，请点击立即安装按钮进行更新');
    } else {
      message.info('未发现新版本，当前版本为最新版本');
    }
  };



  return (
    <div className='version'>
      <img src={logo} />
      <div className='version__info'>
        <div>版本号：{appVersion}</div>
        <Button type="primary" onClick={handleCheckUpdate}>检查更新</Button>
      </div>
      {newVersionInfo ? <div className='version__new-version'>
        <div className='version__new-version--title'><span style={{ marginRight: 5 }}>发现新版本</span>{newVersionInfo.version}</div>
        <div className='version__new-version--content'>{newVersionInfo.content}</div>
        <Button type="primary" size='large' onClick={handleUpdate}>立即安装</Button>
        {downloadProgress && (
          <div>
            <Progress
              percent={Math.round(downloadProgress.percent)}
              size="small"
              format={(percent) => `${percent}%`}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              下载速度: {Math.round(downloadProgress.bytesPerSecond / 1024)} KB/s
            </Text>
          </div>
        )}
      </div> : <div className='version__new-version'>
        当前版本为: {appVersion}
      </div>}
      <div className='version__update-log'>
        {updateLog.length > 0 && <Collapse expandIconPosition='end' expandIcon={({ isActive }) => <img src={collapseIcon} style={isActive ? { transform: 'rotate(180deg)', top: '30%' } : {}} />}>
          <Collapse.Panel header="更新日志" key="update-log" >
            {
              updateLog.map((item, index) => (
                <div key={index}>
                  <div className='version__update-log--title'>
                    <div>
                      版本号：{item.version}
                    </div>
                    <div>
                      {item.date}
                    </div>
                  </div>
                  <div className='version__update-log--content'>{item.content}</div>
                </div>
              ))
            }
          </Collapse.Panel>
        </Collapse>}
      </div>
    </div>
  )
}

export default Version;
