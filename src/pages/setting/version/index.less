.version {
  display: flex;
  flex-direction: column;
  padding: 20px 16px 0px 16px;
  align-items: center;
  gap: 20px;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.version__info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #2c2d33;

  .ant-btn {
    font-size: 12px;
    font-weight: 500;
  }
}

.version__new-version {
  width: 100%;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border-radius: 8px;
  border: 0.5px solid #c1c5cf;
  overflow-y: auto;

  .ant-btn-primary {
    width: 100px;
    border-radius: 8px;
    background: #3570ff;
    border: 1px solid #3570ff;
  }
}

.version__new-version--title {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #1b1c1f;
}

.version__new-version--content {
  font-size: 12px;
  color: #797c8e;
  white-space: pre-line;
  line-height: 22px;
}

.version__update-log {
  width: 100%;
  max-height: 50%;

  .ant-collapse {
    border-radius: 12px;
    background: #fff;
    border: 0.5px solid #c1c5cf;
    height: 100%;
  }

  .ant-collapse-item:last-child>.ant-collapse-content {
    border-radius: 0 0 12px 12px;
    height: calc(100% - 45px);
  }

  .ant-collapse>.ant-collapse-item:last-child,
  .ant-collapse>.ant-collapse-item:last-child>.ant-collapse-header {
    border-radius: 0 0 12px 12px;
  }

  .ant-collapse>.ant-collapse-item {
    border-bottom: none;
    height: 100%;
  }

  .ant-collapse-content>.ant-collapse-content-box {
    height: 100%;
    overflow-y: auto;
    border-radius: 12px;
  }
}

.version__update-log--title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #1b1c1f;
  margin-bottom: 5px;
}

.version__update-log--content {
  font-size: 12px;
  color: #797c8e;
  white-space: pre-line;
  line-height: 22px;
  margin-bottom: 15px;
}