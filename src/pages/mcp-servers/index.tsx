import React, { useContext, useEffect, useMemo, useState } from 'react';
import './index.less';
import { MCPServersContext, PageContext } from '@/context';
import ServerConfigDrawer from './ServerConfigDrawer';
import { Input, Button, Spin, Empty } from 'antd';
import background from '@/assets/image/background.png';
import { Server } from '@/common/types';
import AddIcon from '@/components/Icon/AddIcon';
import ServerCard from './ServerCard';
import useMemoizedFn from 'ahooks/lib/useMemoizedFn';
import Icon from '@/components/MCPIcon';
import EditJsonDrawer from './EditJsonDrawer';
import searchEmpty from '@/assets/image/search-empty.svg';
import SearchIcon from '@/components/Icon/SearchIcon';

const MCPServers: React.FC = () => {
  const { sensorsFunc, serverList, setServerList } = useContext(PageContext);
  const [activeId, setActiveId] = useState<number | null>(null);
  const [mcpMarketList, setMcpMarketList] = useState<Server[]>([]);
  const [openConfig, setOpenConfig] = useState(false);
  const [openJsonEditor, setOpenJsonEditor] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [loadingList, setLoadingList] = useState(false);
  const [startingServersLength, setStartingServersLength] = useState(0);

  const isWindows = window['bdpStudioAPI'].isWindows();

  // 更新服务器loading状态的函数
  const updateServerLoading = useMemoizedFn((serverId: number, loading: boolean) => {
    const currServerList = serverList.map(server => {
      if (server.id === serverId) {
        return { ...server, loading };
      }
      return server;
    });
    // console.log('currServerList', currServerList);
    // setServerList(prevList =>
    //   prevList.map(server =>
    //     server.id === serverId ? { ...server, loading } : server
    //   )
    // );
    setServerList([...currServerList]);
  });

  const getServerList = useMemoizedFn(async () => {
    const list = (await window['bdpStudioAPI'].getMcpList()) as any[];
    // const serverList = serverList.map(server => ({ ...server, loading: false }));
    // 为每个服务器设置初始loading状态
    const listWithLoading = list.map(server => ({ ...server, loading: serverList.find(item => item.id === server.id)?.loading || false }));
    setServerList(listWithLoading);
  });

  const getMcpMarketList = useMemoizedFn(async (value?: string) => {
    setLoadingList(true);
    const list = (await window['bdpStudioAPI'].getMcpMarketList(1, 100, value || keyword)) as any[];
    setLoadingList(false);
    setMcpMarketList(list);
  });

  const updateServerList = useMemoizedFn(async (value?: string) => {
    await getServerList();
    await getMcpMarketList(value || keyword);
  });

  const handleAddMcpServer = () => {
    sensorsFunc('AI_PLAT_add_server');
    setOpenConfig(true);
    setActiveId(null);
  }

  const handleEditJson = () => {
    sensorsFunc('AI_PLAT_edit_json');
    setOpenJsonEditor(true);
  }

  const handleEditMcpServer = (server: Server) => {
    sensorsFunc('AI_PLAT_edit_server', { server: JSON.stringify(server) });
    setOpenConfig(true);
    setActiveId(server.id);
  }

  const handleCloseConfigDrawer = () => {
    setOpenConfig(false);
    setActiveId(null);
  }

  const filteredServerIdList = useMemo(() => {
    return serverList.filter(server => {
      const serverName = server.serverName.toLowerCase();
      const searchValue = keyword.toLowerCase();
      return serverName.includes(searchValue);
    }).map(server => server.id);
  }, [serverList, keyword]);

  useEffect(() => {
    updateServerList();
  }, []);

  return <MCPServersContext.Provider value={{
    activeId,
    setActiveId,
    startingServersLength,
    setStartingServersLength,
    updateServerLoading
  }}>

    <div className={`mcp-servers mcp-servers__bg ${isWindows ? '' : 'drag'}`} style={{ backgroundImage: `url(${background})` }}>
      <div className='mcp-servers__list'>
        <div className='mcp-servers__search'>
          <Input.Search
            placeholder='查找MCP Server'
            size='large' value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
            onSearch={(value) => {
              sensorsFunc('AI_PLAT_search_mcp_servers', { keyword: `${value}` });
              updateServerList(value);
            }}
            enterButton={<SearchIcon />}
          />
          <Button type='primary' ghost onClick={handleAddMcpServer} style={{ width: '120px' }}><AddIcon />添加MCP服务</Button>
        </div>

        <div style={{ width: '100%', overflowY: 'auto', height: 'calc(100vh - 52px)' }}>

           <div style={{ display: filteredServerIdList?.length ? 'block' : 'none'}}>
            <div className='mcp-servers__list--title'>我的服务</div>
            <div className='mcp-servers__list--content'>
              {
                serverList.map(server => {
                  // const serverName = server.serverName.toLowerCase();
                  // const searchValue = keyword.toLowerCase();
                  // const cardVisible = keyword ? serverName.includes(searchValue) : true;
                  const cardVisible = filteredServerIdList.includes(server.id);
                  return <div style={{ display: cardVisible ? 'block' : 'none' }} key={server.id}>
                    <ServerCard key={server.id} server={server} onClickCard={handleEditMcpServer} updateServerCallback={getServerList} />
                  </div>
                })
              }
            </div>
          </div>

          {
            mcpMarketList?.length > 0 && <Spin spinning={loadingList}>
              <div className='mcp-servers__list--title'>
                <div className='mcp-servers__list--title-text'>MCP广场服务</div>
                <Icon type='mcp-web-refresh-grey' onClick={() => { sensorsFunc('AI_PLAT_refresh_mcp_market'); getMcpMarketList() }} />
              </div>
              <div className='mcp-servers__list--content'>
                {
                  mcpMarketList.map(server => {
                    const isAdded = !!serverList.find(item => item.serverName === server.serverName);
                    return <ServerCard key={server.id} isMarketServer server={server} isAdded={isAdded} updateServerCallback={updateServerList} />
                  })
                }
              </div>
            </Spin>
          }
          {
            !filteredServerIdList?.length && !mcpMarketList?.length && <div className='mcp-servers__empty'>
              <Empty description='暂无搜索结果' image={searchEmpty} />
            </div>
          }
        </div>
      </div>
      {openConfig && <ServerConfigDrawer handleCloseConfig={handleCloseConfigDrawer} saveServerCallback={updateServerList} />}
      {openJsonEditor && <EditJsonDrawer handleCloseConfig={() => setOpenJsonEditor(false)} saveServerCallback={updateServerList} />}
    </div>
  </MCPServersContext.Provider>;
}

export default MCPServers;
