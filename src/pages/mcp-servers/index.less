.mcp-servers {
  display: flex;
  width: auto;
  height: 100%;
  padding: 20px;
  position: relative;

  .ant-input {
    border-radius: 4px;
  }

  &.drag {
    -webkit-app-region: drag;

    >div {
      -webkit-app-region: no-drag;
    }
  }
}

.mcp-servers__bg {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -44%, #ffffff 95%);
  background-repeat: no-repeat;
  background-size: 100% auto;
}

.mcp-server-list {
  display: flex;
  flex: 1;
  width: 100%;
  height: calc(100vh - 40px);
  padding: 20px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.mcp-server-list__header {
  width: 100%;
  padding-bottom: 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  font-size: 14px;
  font-weight: bold;
}

.mcp-server-list__content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  overflow-y: auto;
  padding: 2px;
}

.mcp-server-list__group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mcp-server-list__group-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.mcp-server-list__group-header {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.mcp-server-list__item {
  display: flex;
  flex-direction: column;
  border: 1px solid #00000019;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease 0s;
  height: 140px;
  background-color: #ffffff;

  &:hover {
    border-color: #5c7cff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px;
    transform: translateY(-2px);
  }
}

.mcp-server-list__item--header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.mcp-server-list__item--header .anticon {
  font-size: 18px;
  color: #3355ff;
  margin-right: 8px;
}

.mcp-server-list__item--name {
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mcp-server-list__item--status {
  margin-left: 8px;

  >div {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.38);
    box-shadow: none;
    animation: 2s ease 0s infinite normal none running pulse;
  }

  &.active>div {
    background-color: #3355ff;
  }
}

.mcp-server-list__item--desc {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.mcp-server-list__add-server {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-style: dashed;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.6);

  &:hover {
    border-color: #5c7cff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px;
    transform: translateY(-2px);
  }
}

.mcp-server-list__add-server .anticon {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.6);
}

.mcp-server-list__add-server-text {
  margin-top: 12px;
  font-weight: 500;
}

.back-btn-conntainer {
  padding: 12px 0px 0px 12px;
  width: 100%;
  background-color: #ffffff;
}

.back-btn-conntainer__text {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 1);
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  margin-bottom: 10px;

  .anticon {
    font-size: 16px;
  }

  &:hover {
    color: #3355ff;
  }
}

.server-config {
  width: 100%;
  display: flex;
  flex-direction: column;
  // overflow-y: scroll;
}

.server-config-wrapper {
  position: relative;

  .ant-tabs-tabpane {
    // overflow-y: scroll;
    // height: calc(100vh - 180px);
  }

  .ant-tabs>.ant-tabs-nav,
  .ant-tabs>div>.ant-tabs-nav {
    padding-left: 16px;
  }

  .ant-collapse {
    border: none;
    background: #ffffff;
  }

  .ant-collapse>.ant-collapse-item {
    margin-bottom: 12px;
    border: 1px solid #ebeef9;
    border-radius: 12px;
  }

  .ant-collapse-content {
    border-top: 1px solid #ebeef9;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }

  .ant-collapse-expand-icon:hover {
    color: #3570ff;

    .ant-collapse-arrow {
      background: #ebf1ff;
      border-radius: 4px;
    }
  }

  .ant-collapse>.ant-collapse-item>.ant-collapse-header .ant-collapse-header-text {
    max-width: 100%;
  }

  .ant-collapse-content>.ant-collapse-content-box {
    background: #f6f7fa;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
}

.server-config__tools--title {
  margin-bottom: 0.5em;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #797c8e;
}

.server-config__tools--table-wrapper {
  margin-top: 8px;
  border: 1px solid #eef1f7;
  border-radius: 8px;
  width: 100%;
}

.server-config__tools table {
  width: 100%;
  table-layout: auto;

  tr:not(:last-child) {
    border-bottom: 1px solid #eef1f7;
  }

  tr:first-child {
    >th:first-child {
      border-top-left-radius: 8px;
    }

    >td:last-child {
      border-top-right-radius: 8px;
    }
  }

  tr:last-child {
    >th:first-child {
      border-bottom-left-radius: 8px;
    }

    >td:last-child {
      border-bottom-right-radius: 8px;
    }
  }

  th {
    padding: 8px 16px;
    color: rgba(0, 0, 0, 0.88);
    background-color: #ffffff;

    border-inline-end: 1px solid #eef1f7;
    padding-inline-end: 16px;
    font-weight: normal;
    font-size: 14px;
    text-align: start;
  }

  td {
    border-inline-end: none;
    padding-inline-end: 0;
    padding: 8px 16px;
    background: #ffffff;
  }
}

.server-config__tools--table-th {
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  .ant-tag.ant-tag-red {
    border-radius: 8px;
  }
}

.server-config__tools--table-td {
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.server-config__tools--table-td-type {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 6px;
  height: 6px;
  vertical-align: middle;
  border-radius: 50%;
  background-color: #3355ff;
  margin-right: 8px;
}

.server-config__tools--table-td-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 1.5;
}

.server-config__tools--header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-config__tools--header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: calc(100% - 60px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.server-config__tools--header-title {
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #1b1c1f;
}

.server-config__tools--header-description {
  width: 100%;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: #797c8e;
}

.server-config__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 0 0px;
  border-top: 0.5px solid #ebeef9;

  .ant-btn {
    border-radius: 8px;

    &:not(:last-child) {
      margin-right: 12px;
    }
  }
}

.mcp-servers__list {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.mcp-servers__search {
  width: 100%;
  padding: 10px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  height: fit-content;

  .ant-input-group-wrapper.ant-input-search {
    width: 360px;
  }

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  button.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button {
    background: #ffffff;
    box-shadow: none;

    >svg {
      width: 16px;
      height: 16px;
      color: #1B1C1F;
    }
  }
}

.mcp-servers__list--title {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #1b1c1f;
  margin: 16px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  svg {
    font-size: 16px;
    color: #797c8e;
    margin-right: 10px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.mcp-servers__list--content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
  width: 100%;
  overflow-y: auto;
}

.mcp-servers__list--item {
  border-radius: 12px;
  opacity: 1;
  box-sizing: border-box;
  border: 1px solid #ebeef9;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 350px;
  height: 112px;

  &.active,
  &:hover {
    background: linear-gradient(168deg, #ddecff 1%, #ffffff 19%);
    border: 1px solid #3570ff;
    box-shadow: 0px 0px 12px 0px #dddfe2;
    transition: all 0.3s ease;
  }

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    width: 68px;
    border-radius: 8px;
  }
}

.mcp-servers__list--item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: calc(100% - 68px);
  color: #1b1c1f;
  font-size: 14px;

  svg {
    font-size: 30px;
  }

  img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid #ebeef9;
  }

  .ant-tag.ant-tag-success {
    width: 52px;
    height: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 8px;
    gap: 8px;
    border-radius: 4px;
    opacity: 1;
    background: #e9faf4;
    color: #28c58e;
    font-size: 12px;
    border: none;
  }
}

.mcp-servers__list--item-server-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mcp-servers__list--item-desc {
  color: #797c8e;
  font-size: 12px;
  width: 320px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  line-height: 1.5em;
  max-height: 3em;
}

.mcp-servers__drawer {
  -webkit-app-region: no-drag;
  // ... existing code ...

  // MCP Servers Drawer 拖拽样式
  .ant-drawer-content-wrapper {
    position: absolute;
    transition: none;

    // 添加左边缘的拖拽区域
    &::before {
      content: '';
      position: absolute;
      left: -4px;
      top: 0;
      bottom: 0;
      width: 2px;
      cursor: ew-resize;
      z-index: 10;
      background: transparent;
      transition: background 0.2s ease;
    }

    // 鼠标悬停时显示拖拽提示
    &:hover::before {
      background: rgba(51, 85, 255, 0.1);
    }

    // 正在拖拽时的样式
    &.resizing {
      user-select: none;
      pointer-events: none;

      &::before {
        background: rgba(51, 85, 255, 0.3);
      }
    }
  }

  .ant-drawer-mask {
    background: transparent;
  }

  .ant-drawer-header {
    padding: 12px 16px;
  }

  .ant-drawer-close {
    margin-right: 0;
    padding-left: 0;
  }

  .ant-drawer-body {
    padding: 0;
  }

  .ant-drawer-content {
    border-radius: 12px 0px 0px 12px;
    box-shadow: -4px 0px 24px 0px #dddfe2;
  }

  .ant-input {
    border-radius: 8px;
    border: 1px solid #ebeef9;
  }

  .bdp-input.ant-input.ant-input-lg {
    font-size: 14px;
    line-height: 1.5715;
  }

  .ant-form-item-label>label {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: #1b1c1f;
  }
}

.server-config__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-config__header--left {
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 200px;
  color: #1b1c1f;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;

  .ant-btn-dangerous.ant-btn-text {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .server-config__header--icon-wrapper {
    border-radius: 8px;
    border: 1px solid #ebeef9;
    font-size: 24px;
    padding: 2px;
  }

  img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid #ebeef9;
  }
}

.server-config__header--title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.server-config__header--right {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: #1b1c1f;
}

.edit-json-drawer {
  -webkit-app-region: no-drag;

  .ant-drawer-mask {
    background: transparent;
  }

  .ant-drawer-header {
    padding: 12px 16px;
  }

  .ant-drawer-close {
    margin-right: 0;
    padding-left: 0;
  }

  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .bdp-input-textarea.ant-input {
    height: calc(100% - 60px);
    width: 100%;
    border-radius: 8px;
    border: 0.5px solid #c1c5cf;
  }

  .bdp-input-textarea.ant-input {
    background-color: #f6f7fa;
  }
}

.edit-json-drawer__footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 12px;
  gap: 12px;

  .ant-btn {
    border-radius: 8px;
  }
}

.mcp-servers__empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .ant-empty-image {
    width: 180px;
    height: 180px;
  }

  .ant-empty-description {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    color: #797c8e;
  }
}