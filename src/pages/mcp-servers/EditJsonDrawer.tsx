import React, { useContext, useEffect, useState } from 'react';
import ArrowLeftIcon from '@/components/Icon/ArrowLeftIcon';
import { Input, Drawer, Button } from '@bdpfe/components';
import { MCPServersContext, PageContext } from '@/context';

const EditJsonDrawer: React.FC<{ handleCloseConfig: () => void; saveServerCallback: () => void }> = (props) => {
  const { handleCloseConfig, saveServerCallback } = props;
  const { serverList, sensorsFunc } = useContext(PageContext);
  const [serverJson, setServerJson] = useState<string>();
  const cancelEditJson = () => {
    sensorsFunc('AI_PLAT_cancel_edit_json');
    handleCloseConfig();
  }
  const saveJsonConfig = () => {
    sensorsFunc('AI_PLAT_save_json');
    saveServerCallback();
  }

  useEffect(() => {
    const mcpServers = {};
    serverList.forEach(server => {
      const serverConfig = JSON.parse(server.serverConfig)
      mcpServers[server.serverName] = {
        type: serverConfig.serverType,
        command: serverConfig.cmd,
        args: serverConfig.args,
        env: serverConfig.env,
        url: serverConfig.serverUrl,
        headers: serverConfig.requestHeaders,
      };
    });
    const mcpServerJson = JSON.stringify({ mcpServers }, null, 4);
    setServerJson(mcpServerJson);
  }, [serverList]);

  return (
    <Drawer
      closeIcon={<ArrowLeftIcon />}
      maskClosable={false}
      title="编辑JSON"
      open
      className='edit-json-drawer'
      onClose={cancelEditJson}
    >
      <Input.TextArea value={serverJson} onChange={e => setServerJson(e.target.value)}/>
      <div className='edit-json-drawer__footer'>
        <Button onClick={cancelEditJson} size='large'>取消</Button>
        <Button type='primary' onClick={saveJsonConfig} size='large'>保存</Button>
      </div>
    </Drawer>
  );
}

export default EditJsonDrawer;