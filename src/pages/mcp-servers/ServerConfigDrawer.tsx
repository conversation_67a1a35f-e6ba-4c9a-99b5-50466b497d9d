import React, { act, useContext, useEffect, useMemo, useState } from 'react';
import {
  CodeOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  Switch,
  Button,
  Tabs,
  Form,
  Input,
  Radio,
  Tooltip,
  message,
  Modal,
  Collapse,
  Tag,
  Drawer,
  EllipsisTooltip,
} from '@bdpfe/components';
import { MCPServersContext, PageContext } from '@/context';
import { StdioServerConfig, StreamableHttpServerConfig, SseServerConfig, Server } from '@/common/types';
import { Tool } from '@modelcontextprotocol/sdk/types';
import ArrowLeftIcon from '@/components/Icon/ArrowLeftIcon';
import interact from 'interactjs'
import CollapseArrowIcon from '@/components/Icon/CollapseArrowIcon';
import { useMemoizedFn } from 'ahooks';
import { api } from '@/services/api';

type ConfigForm = {
  serverName: string;
  serverDesc: string;
  serverType: 'stdio' | 'sse' | 'streamable_http';
  cmd?: string;
  args?: string;
  env?: string;
  serverUrl: string;
  requestHeaders?: string;
};

const ServerConfig: React.FC<{ handleCloseConfig: () => void; saveServerCallback: () => void }> = (props) => {
  const { handleCloseConfig, saveServerCallback } = props;
  const { serverList } = useContext(PageContext);
  const { activeId, setActiveId, startingServersLength, setStartingServersLength, updateServerLoading } = useContext(MCPServersContext);
  const { sensorsFunc } = useContext(PageContext);
  const [form] = Form.useForm<ConfigForm>();
  const [enabled, setEnabled] = useState(false);
  const [toolList, setToolList] = useState<
    { name: string; description: string; enabled: boolean; inputSchema: Tool['inputSchema'] }[]
  >([]);
  const [width, setWidth] = useState(380);
  const [isResizing, setIsResizing] = useState<boolean>(false);

  const serverType = Form.useWatch('serverType', form);

  const [saveDisabled, setSaveDisabled] = useState(true);
  const [editMode, setEditMode] = useState<'form' | 'json'>('form');
  const [jsonConfig, setJsonConfig] = useState<string>('');
  const [jsonError, setJsonError] = useState<string>('');

  // 通过activeId从serverList中获取当前服务器
  const activeServer = useMemo(() => {
    return serverList.find(server => server.id === activeId) || null;
  }, [serverList, activeId]);

  const isMarketServer = useMemo(() => {
    return !!activeServer?.serverFromObj;
  }, [activeServer?.serverFromObj]);

  // MCP配置类型定义
  interface McpStdioConfig {
    command: string;
    args?: string[];
    env?: Record<string, string>;
  }

  interface McpHttpConfig {
    url: string;
    headers?: Record<string, string>;
    type: 'sse' | 'streamable_http';
  }

  type McpConfig = McpStdioConfig | McpHttpConfig;

  // 数据转换函数：从项目内部格式转换为MCP标准格式
  const convertToMcpFormat = (serverConfig: string, serverName: string = ''): Record<string, McpConfig> => {
    try {
      const config = JSON.parse(serverConfig);
      let mcpConfig: McpConfig;

      switch (config.serverType) {
        case 'stdio':
          mcpConfig = {
            command: config.cmd || '',
            args: config.args || [],
            env: config.env || {}
          };
          break;
        case 'sse':
        case 'streamable_http':
          mcpConfig = {
            url: config.serverUrl || '',
            headers: config.requestHeaders || {},
            type: config.serverType
          };
          break;
        default:
          mcpConfig = {
            command: '',
            args: [],
            env: {}
          };
      }

      return { [serverName || 'server']: mcpConfig };
    } catch (error) {
      return { [serverName || 'server']: {
        command: '',
        args: [],
        env: {}
      }};
    }
  };

  // 数据转换函数：从MCP标准格式转换为项目内部格式
  const convertFromMcpFormat = (mcpConfigWrapper: Record<string, McpConfig>): {
    serverName: string;
    serverType: 'stdio' | 'sse' | 'streamable_http';
    cmd?: string;
    args?: string[];
    env?: Record<string, string>;
    serverUrl?: string;
    requestHeaders?: Record<string, string>;
  } => {
    // 获取第一个服务器配置
    const serverName = Object.keys(mcpConfigWrapper)[0] || 'server';
    const mcpConfig = mcpConfigWrapper[serverName];

    if (isStdioConfig(mcpConfig)) {
      return {
        serverName,
        serverType: 'stdio',
        cmd: mcpConfig.command || '',
        args: mcpConfig.args || [],
        env: mcpConfig.env || {}
      };
    } else if (isHttpConfig(mcpConfig)) {
      return {
        serverName,
        serverType: mcpConfig.type || 'sse', // 默认为sse，可以根据需要调整
        serverUrl: mcpConfig.url || '',
        requestHeaders: mcpConfig.headers || {}
      };
    }

    return {
      serverName,
      serverType: 'stdio',
      cmd: '',
      args: [],
      env: {}
    };
  };

  // 工具函数：解析key=value字符串
  const parseKeyValueString = (str: string): Record<string, string> => {
    if (!str) return {};
    const result: Record<string, string> = {};
    str.split('\n').forEach((item) => {
      const [key, value] = item.split('=');
      if (key && value) {
        result[key.trim()] = value.trim();
      }
    });
    return result;
  };

  // 工具函数：格式化key=value字符串
  const formatKeyValueString = (obj: Record<string, string>): string => {
    if (!obj) return '';
    return Object.entries(obj).map(([key, value]) => `${key}=${value}`).join('\n');
  };

  // 类型保护函数
  const isStdioConfig = (config: McpConfig): config is McpStdioConfig => {
    return 'command' in config;
  };

  const isHttpConfig = (config: McpConfig): config is McpHttpConfig => {
    return 'url' in config;
  };

  // 根据JSON内容推断服务器类型
  const inferServerType = (mcpConfig: McpConfig): 'stdio' | 'sse' | 'streamable_http' => {
    if (isStdioConfig(mcpConfig)) {
      return 'stdio';
    } else if (isHttpConfig(mcpConfig)) {
      return 'sse'; // 默认为sse，可以根据需要调整
    }
    return 'stdio'; // 默认
  };

  // 验证服务器名包装的JSON格式
  const validateServerNameWrapper = (jsonObj: any): { valid: boolean; error?: string } => {
    if (!jsonObj || typeof jsonObj !== 'object') {
      return { valid: false, error: '配置必须是有效的JSON对象' };
    }

    const serverNames = Object.keys(jsonObj);
    if (serverNames.length === 0) {
      return { valid: false, error: '配置中必须包含至少一个服务器' };
    }

    if (serverNames.length > 1) {
      return { valid: false, error: '配置中只能包含一个服务器' };
    }

    const serverName = serverNames[0];
    const mcpConfig = jsonObj[serverName];

    if (isMarketServer) {
      const serverFromObj = JSON.parse(activeServer?.serverFromObj || '{}');
      // const serverName = serverFromObj.serverName;
      // console.log(serverFromObj);
      if (serverName && serverName !== activeServer.serverName) {
        return { valid: false, error: '领慧广场的MCP不允许修改服务器名称' };
      }
    }

    return validateMcpConfig(mcpConfig);
  };

  // JSON格式验证
  const validateMcpConfig = (mcpConfig: any): { valid: boolean; error?: string } => {
    // 检查是否是有效的对象
    if (!mcpConfig || typeof mcpConfig !== 'object') {
      return { valid: false, error: '服务器配置必须是有效的JSON对象' };
    }

    const config: StdioServerConfig | SseServerConfig | StreamableHttpServerConfig = JSON.parse(
      activeServer?.serverConfig || '{}',
    );

    if (mcpConfig.command) {
      // STDIO类型验证
      if (typeof mcpConfig.command !== 'string') {
        return { valid: false, error: 'command字段必须是字符串' };
      }
      if (mcpConfig.args && !Array.isArray(mcpConfig.args)) {
        return { valid: false, error: 'args字段必须是数组' };
      }
      if (mcpConfig.env && (typeof mcpConfig.env !== 'object' || Array.isArray(mcpConfig.env))) {
        return { valid: false, error: 'env字段必须是对象' };
      }
    } else if (mcpConfig.url) {

      if(isMarketServer && config.serverType !== 'stdio' && config['serverUrl']!== mcpConfig.url) {
        return { valid: false, error: '领慧广场的MCP不允许修改URL' };
      }

      if (mcpConfig.type && mcpConfig.type !== 'sse' && mcpConfig.type !== 'streamable_http') {
        return { valid: false, error: 'type字段必须是sse或streamable_http' };
      }

      if (isMarketServer && config.serverType !== mcpConfig.type) {
        return { valid: false, error: '领慧广场MCP不允许修改type字段' };
      }
      // SSE/StreamableHTTP类型验证

      if (typeof mcpConfig.url !== 'string') {
        return { valid: false, error: 'url字段必须是字符串' };
      }
      if (mcpConfig.headers && (typeof mcpConfig.headers !== 'object' || Array.isArray(mcpConfig.headers))) {
        return { valid: false, error: 'headers字段必须是对象' };
      }
    } else {
      return { valid: false, error: '缺少必要的command或url字段' };
    }

    return { valid: true };
  };

  // 模式切换函数：从表单切换到JSON编辑器
  const switchToJsonMode = () => {
    const values = form.getFieldsValue();
    const internalConfig = {
      serverType: values.serverType,
      cmd: values.cmd,
      args: values.args?.split('\n').filter(Boolean),
      env: parseKeyValueString(values.env || ''),
      serverUrl: values.serverUrl,
      requestHeaders: parseKeyValueString(values.requestHeaders || '')
    };

    const serverName = values.serverName || 'server';
    const mcpConfigWrapper = convertToMcpFormat(JSON.stringify(internalConfig), serverName);
    setJsonConfig(JSON.stringify(mcpConfigWrapper, null, 2));
    setJsonError('');
    setSaveDisabled(false); // JSON模式下默认启用保存按钮
    setEditMode('json');
  };

  // 模式切换函数：从JSON编辑器切换到表单
  const switchToFormMode = () => {
    try {
      const mcpConfigWrapper = JSON.parse(jsonConfig);
      const validation = validateServerNameWrapper(mcpConfigWrapper);

      if (!validation.valid) {
        setJsonError(validation.error || 'JSON格式错误');
        return;
      }

      const internalConfig = convertFromMcpFormat(mcpConfigWrapper);

      form.setFieldsValue({
        serverName: internalConfig.serverName,
        serverType: internalConfig.serverType,
        cmd: internalConfig.cmd,
        args: internalConfig.args?.join('\n'),
        env: formatKeyValueString(internalConfig.env || {}),
        serverUrl: internalConfig.serverUrl,
        requestHeaders: formatKeyValueString(internalConfig.requestHeaders || {})
      });

      setJsonError('');
      setSaveDisabled(!form.isFieldsTouched()); // 回到表单模式时检查表单状态
      setEditMode('form');
    } catch (error) {
      setJsonError('JSON格式错误，请检查后重试');
    }
  };

  // JSON内容变化处理
  const handleJsonChange = (value: string) => {
    setJsonConfig(value);
    try {
      const mcpConfigWrapper = JSON.parse(value);
      const validation = validateServerNameWrapper(mcpConfigWrapper);
      if (!validation.valid) {
        setJsonError(validation.error || 'JSON格式错误');
        setSaveDisabled(true);
      } else {
        setJsonError('');
        setSaveDisabled(false);
      }
    } catch (error) {
      setJsonError('JSON格式错误');
      setSaveDisabled(true);
    }
  };

  const getToolList = async () => {
    if (!activeServer) return;
    const toolList = await window['bdpStudioAPI'].getToolListByMcp(activeServer.id);
    setToolList(toolList);
  };

  const saveServerConfig = async (refreshServerList: boolean = true) => {
    try {
      let finalConfig: StdioServerConfig | SseServerConfig | StreamableHttpServerConfig;
      let serverName: string;
      let serverDesc: string;

      if (editMode === 'json') {
        // JSON编辑模式的保存逻辑
        try {
          const mcpConfigWrapper = JSON.parse(jsonConfig);
          const validation = validateServerNameWrapper(mcpConfigWrapper);

          if (!validation.valid) {
            message.error(validation.error || 'JSON格式错误');
            return;
          }

          const internalConfig = convertFromMcpFormat(mcpConfigWrapper);

          finalConfig = internalConfig as StdioServerConfig | SseServerConfig | StreamableHttpServerConfig;
          serverName = internalConfig.serverName || activeServer?.serverName || '';
          serverDesc = activeServer?.serverDesc || '';

        } catch (error) {
          message.error('JSON格式错误，请检查后重试');
          return;
        }
      } else {
        // 表单模式的保存逻辑（保持原有逻辑）
        const values = await form.validateFields();
        if (!values) return;

        const { args, env, requestHeaders } = values;
        serverName = values.serverName.trim();
        const cmd = values.cmd?.trim();
        serverDesc = values.serverDesc?.trim();
        const serverUrl = values.serverUrl?.trim();
        let argsArray: string[] = [];
        let envObj: Record<string, string> = {};
        let requestHeaderObj: Record<string, string> = {};

        if (serverList.find(server => {
          if (activeServer?.id) {
            return (server.serverName === serverName) && (server.id !== activeServer.id);
          } else {
            return (server.serverName === serverName);
          }
        })) {
          message.error('服务器名称已存在');
          return;
        }

        if (args) {
          argsArray = args.split('\n').map((item) => item.trim());
        }
        if (env) {
          env.split('\n').forEach((item) => {
            const [key, value] = item.split('=');
            if (key && value) {
              envObj[key.trim()] = value.trim();
            }
          });
        }
        if (requestHeaders) {
          requestHeaders.split('\n').forEach((item) => {
            const [key, value] = item.split('=');
            if (key && value) {
              requestHeaderObj[key.trim()] = value.trim();
            }
          });
        }

        finalConfig = {
          serverType,
          ...(serverType === 'stdio' && { cmd, args: argsArray, env: envObj }),
          ...(serverType === 'sse' && { serverUrl, requestHeaders: requestHeaderObj }),
          ...(serverType === 'streamable_http' && {
            serverUrl,
            requestHeaders: requestHeaderObj,
          }),
        } as StdioServerConfig | SseServerConfig | StreamableHttpServerConfig;
      }

      // 内置工具校验：防止修改关键字段
      if (activeServer?.isBuiltin === 1) {
        // 获取原始配置
        const originalConfig = JSON.parse(activeServer.serverConfig || '{}') as StdioServerConfig | SseServerConfig | StreamableHttpServerConfig;

        // 校验关键字段是否被修改
        if (serverName !== activeServer.serverName) {
          message.error('内置工具不允许修改名称');
          return;
        }

        if (finalConfig.serverType !== originalConfig.serverType) {
          message.error('内置工具不允许修改类型');
          return;
        }

        if (finalConfig.serverType === 'stdio' && originalConfig.serverType === 'stdio') {
          if ((finalConfig as StdioServerConfig).command !== (originalConfig as StdioServerConfig).command) {
            message.error('内置工具不允许修改命令');
            return;
          }
        }

        if (finalConfig.serverType === 'sse' && originalConfig.serverType === 'sse') {
          if ((finalConfig as SseServerConfig).serverUrl !== (originalConfig as SseServerConfig).serverUrl) {
            message.error('内置工具不允许修改URL');
            return;
          }
        }

        if (finalConfig.serverType === 'streamable_http' && originalConfig.serverType === 'streamable_http') {
          if ((finalConfig as StreamableHttpServerConfig).serverUrl !== (originalConfig as StreamableHttpServerConfig).serverUrl) {
            message.error('内置工具不允许修改URL');
            return;
          }
        }
      }

      const res = await window['bdpStudioAPI'].saveMcp({
        ...(activeServer || {}),
        serverName,
        serverDesc,
        serverConfig: JSON.stringify(finalConfig),
      });

      const hasResult = (data: any): data is { success: boolean; result: any } => {
        if (data.success === true) {
          return true;
        }
        return false;
      }

      if (hasResult(res)) {
        setSaveDisabled(true);
        if (refreshServerList) {
          saveServerCallback && saveServerCallback();
        }
        if (typeof res === 'object') {
          if (!activeServer && res.result.id) setActiveId(res.result.id);
          sensorsFunc('AI_PLAT_save_server_config', { server: JSON.stringify(res) });
        }

        return Promise.resolve(true);
      } else {
        message.error(res.errorMsg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSave = async () => {
    const res = await saveServerConfig();
    if (res) {
      message.success('服务器更新成功');
    }
  }

  const cancelServerConfig = () => {
    sensorsFunc('AI_PLAT_cancel_server_config', activeServer ? { server: JSON.stringify(activeServer) } : {});
    handleCloseConfig();
  }

  const deleteServer = () => {
    if (!activeServer) return;
    sensorsFunc('AI_PLAT_delete_server', { server: JSON.stringify(activeServer) })
    Modal.confirm({
      title: '删除服务器',
      content: '确定要删除此服务器吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await window['bdpStudioAPI'].deleteMcp(activeServer.id);
        if (res) {
          message.success('服务器删除成功');
          setActiveId(null);
          saveServerCallback && saveServerCallback();
          handleCloseConfig();
        }
      },
    });
  };

  const startServer = async () => {
    if (!activeServer) return;
    sensorsFunc('AI_PLAT_start_server', { server: JSON.stringify(activeServer) });

    const saveServerRes = await saveServerConfig(false);
    if (!saveServerRes) return;

    const enabledServers = serverList.filter((server) => server.serverStatus === 1);
    const maxEnableLength = enabledServers.length + startingServersLength;
    if (maxEnableLength >= 10) {
      message.warning('最多启动10个服务');
      return;
    }
    updateServerLoading(activeServer.id, true);
    setStartingServersLength((value: number) => value + 1);
    const res = await window['bdpStudioAPI'].startMcp(activeServer.id);

    if (res && res.success) {
      message.success('服务器启动成功');
      setEnabled(true);
      getToolList();
    } else {
      message.error(res.error || '服务器启动失败');
    }
    updateServerLoading(activeServer.id, false);
    setStartingServersLength((value: number) => value - 1);
    setTimeout(() => {
      saveServerCallback && saveServerCallback();
    }, 30);
  };

  const stopServer = async () => {
    if (!activeServer) return;
    sensorsFunc('AI_PLAT_stop_server', { server: JSON.stringify(activeServer) })
    const saveServerRes = await saveServerConfig(false);
    if (!saveServerRes) return;
    updateServerLoading(activeServer.id, true);
    const res = await api.stopMcp(activeServer.id);
    if (res) {
      message.success('服务器停止成功');
      setEnabled(false);
    }
    updateServerLoading(activeServer.id, false);
    setTimeout(() => {
      saveServerCallback && saveServerCallback();
    }, 30);
  };

  const toggleServer = async () => {
    if (enabled) {
      await stopServer();
    } else {
      await startServer();
    }
  };

  const handleToolSwitch = async (toolName: string, checked: boolean) => {
    if (!activeServer) return;
    let res;
    sensorsFunc('AI_PLAT_switch_tool', { server: JSON.stringify(activeServer), toolName: toolName, checked: checked.toString() })
    if (checked) {
      res = await window['bdpStudioAPI'].enabledTool(activeServer.id, toolName);
    } else {
      res = await window['bdpStudioAPI'].disabledTool(activeServer.id, toolName);
    }
    if (res) {
      getToolList();
    }
  }

  const editFormSensors = (field: string, value: any) => {
    if (!activeServer) return;
    const editInfo = {};
    editInfo[field] = value;
    sensorsFunc('AI_PLAT_edit_server_filed', { server: JSON.stringify(activeServer), editInfo: JSON.stringify(editInfo) })
  }

  const generalForm = () => {
    return (
      <div>
        {/* 内置工具提示 */}
        {activeServer?.isBuiltin === 1 && (
          <div style={{
            margin: '0 16px 16px 16px',
            padding: '12px 16px',
            backgroundColor: '#f0f6ff',
            border: '1px solid #bfdbfe',
            borderRadius: '8px',
            fontSize: '14px',
            color: '#1e40af'
          }}>
            <span style={{ fontWeight: '500' }}>内置工具</span>
            <span style={{ marginLeft: '8px' }}>此为系统内置工具，仅允许修改参数(args)和环境变量(env)，其他配置不可更改</span>
          </div>
        )}

        {/* 模式切换头部 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}>
          <span style={{ fontSize: '14px', fontWeight: '500', color: '#333' }}>
            服务器配置
          </span>
          <a
            onClick={() => editMode === 'form' ? switchToJsonMode() : switchToFormMode()}
            style={{
              color: '#3570ff',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }}
          >
            {editMode === 'form' ? '编辑JSON' : '表单模式'}
          </a>
        </div>

        {/* 条件渲染：表单模式或JSON编辑模式 */}
        {editMode === 'form' ? (
          <Form
            form={form}
            layout="vertical"
            onValuesChange={() => setSaveDisabled(!form.isFieldsTouched())}
          >
            <Form.Item
              label="名称"
              name="serverName"
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input size="large" placeholder="名称" disabled={isMarketServer || activeServer?.isBuiltin === 1} onBlur={(e) => editFormSensors('serverName', e.target.value)} />
            </Form.Item>
            <Form.Item label="描述" name="serverDesc">
              <Input.TextArea size="large" placeholder="描述" rows={3} disabled={isMarketServer} onBlur={(e) => editFormSensors('serverDesc', e.target.value)} />
            </Form.Item>
            <Form.Item
              label="类型"
              name="serverType"
              rules={[{ required: true, message: '请输入类型' }]}
            >
              <Radio.Group disabled={isMarketServer || activeServer?.isBuiltin === 1} onChange={(e) => editFormSensors('serverType', e.target.value)}>
                <Radio value="stdio">STDIO</Radio>
                <Radio value="sse">SSE</Radio>
                <Radio value="streamable_http">StreamableHTTP</Radio>
              </Radio.Group>
            </Form.Item>
            {serverType === 'stdio' && (
              <>
                <Form.Item label="命令" name="cmd" rules={[{ required: true, message: '请输入命令' }]}>
                  <Input size="large" disabled={activeServer?.isBuiltin === 1} onBlur={(e) => editFormSensors('cmd', e.target.value)} />
                </Form.Item>
                <Form.Item
                  label={
                    <>
                      参数
                      <Tooltip title="每个参数占一行">
                        <QuestionCircleOutlined style={{ marginLeft: 5 }} />
                      </Tooltip>
                    </>
                  }
                  name="args"
                >
                  <Input.TextArea size="large" placeholder={`arg1\narg2`} rows={3} onBlur={(e) => editFormSensors('args', e.target.value)} />
                </Form.Item>
                <Form.Item
                  label={
                    <>
                      环境变量
                      <Tooltip title="格式：KEY=value，每行一个">
                        <QuestionCircleOutlined style={{ marginLeft: 5 }} />
                      </Tooltip>
                    </>
                  }
                  name="env"
                >
                  <Input.TextArea size="large" placeholder={`KEY1=value1\nKEY2=value2`} rows={3} onBlur={(e) => editFormSensors('env', e.target.value)} />
                </Form.Item>
              </>
            )}
            {['sse', 'streamable_http'].includes(serverType) && (
              <>
                <Form.Item
                  label={
                    <>
                      URL
                      <Tooltip title="远程 URL 地址">
                        <QuestionCircleOutlined style={{ marginLeft: 5 }} />
                      </Tooltip>
                    </>
                  }
                  name="serverUrl"
                  rules={[{ required: true, message: '请输入URL' }]}

                >
                  <Input disabled={isMarketServer} size="large" placeholder="http://localhost:3000/sse" onBlur={(e) => editFormSensors('serverUrl', e.target.value)} />
                </Form.Item>
                <Form.Item
                  label={
                    <>
                      请求头
                      <Tooltip title="HTTP 请求的自定义请求头">
                        <QuestionCircleOutlined style={{ marginLeft: 5 }} />
                      </Tooltip>
                    </>
                  }
                  name="requestHeaders"
                >
                  <Input.TextArea
                    size="large"
                    placeholder={`Content-Type=application/json\nAuthorization=Bearer token`}
                    onBlur={(e) => editFormSensors('requestHeaders', e.target.value)}
                  />
                </Form.Item>
              </>
            )}
          </Form>
        ) : (
          /* JSON编辑模式 */
          <div style={{ padding: '0 16px' }}>
            <div style={{ marginBottom: '12px' }}>
              <span style={{ fontSize: '12px', color: '#797c8e' }}>
                编辑MCP服务器配置的JSON格式。支持标准的MCP协议配置。
              </span>
            </div>
            <Input.TextArea
              value={jsonConfig}
              onChange={(e) => handleJsonChange(e.target.value)}
              style={{
                height: '300px',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                fontSize: '14px',
                backgroundColor: jsonError ? '#fef2f2' : '#f6f7fa',
                border: jsonError ? '1px solid #f87171' : '1px solid #c1c5cf',
                borderRadius: '8px'
              }}
              placeholder={`{
  "my-server": {
    "command": "npx",
    "args": ["-y", "@playwright/mcp@latest"],
    "env": {
      "NODE_ENV": "production"
    }
  }
}`}
            />
            {jsonError && (
              <div style={{
                color: '#ef4444',
                fontSize: '12px',
                marginTop: '8px',
                padding: '8px 12px',
                backgroundColor: '#fef2f2',
                borderRadius: '4px',
                border: '1px solid #fecaca'
              }}>
                <strong>错误:</strong> {jsonError}
              </div>
            )}
            <div style={{ marginTop: '12px', fontSize: '12px', color: '#797c8e' }}>
              <strong>支持的配置格式:</strong>
              <br />
              • 格式: <code>{`{"serverName": {config}}`}</code>
              <br />
              • STDIO类型: <code>command</code>, <code>args</code>, <code>env</code>
              <br />
              • SSE/HTTP类型: <code>url</code>, <code>headers</code>
            </div>
          </div>
        )}
      </div>
    );
  };
  const getExpandIcon = useMemoizedFn(({ isActive }) => <Tooltip title={isActive ? '收起' : '展开'}><CollapseArrowIcon style={isActive ? { transform: 'rotate(-180deg)', top: '40%' } : {}} /></Tooltip>);

  const toolListComponent = () => {
    return (
      <div style={{ padding: '0 16px' }}>
        <Collapse expandIcon={getExpandIcon} expandIconPosition="end">
          {toolList.map((tool) => {
            const header = (
              <div className="server-config__tools--header">
                <div className='server-config__tools--header-left'>
                  <div className="server-config__tools--header-title">{tool.name}</div>
                  <div className="server-config__tools--header-description">
                    <EllipsisTooltip style={{ width: '100%' }}>{tool.description}</EllipsisTooltip>
                  </div>
                </div>
                <div style={{ marginLeft: 5 }} onClick={e => e.stopPropagation()}>
                  <Switch
                    checked={tool.enabled}
                    onChange={(checked) => handleToolSwitch(tool.name, checked)}
                  />
                </div>
              </div>
            );
            const schema: Tool['inputSchema'] = tool.inputSchema;
            const requiredList = (schema.required as string[]) ?? [];

            const dataSource = Object.keys(schema.properties || {}).map((key) => ({
              key,
              propertyKey: key,
              required: requiredList.includes(key),
              // type, description
              ...(typeof schema.properties[key] === 'object' && schema.properties[key]),
            }));

            return (
              <Collapse.Panel header={header} key={tool.name}>
                <div className="server-config__tools">
                  <h5 className="server-config__tools--title">输入模式:</h5>
                  {/* <Table pagination={false} columns={columns} dataSource={dataSource} showHeader={false} rowKey="propertyKey" /> */}
                  <div className="server-config__tools--table-wrapper">
                    <table>
                      <tbody>
                        {dataSource.map((record: any) => {
                          return (
                            <tr key={record.propertyKey}>
                              <th>
                                <div className="server-config__tools--table-th">
                                  {record.propertyKey}
                                  {record.required && <Tag color="red">Required</Tag>}
                                </div>
                              </th>
                              <td>
                                <div className="server-config__tools--table-td">
                                  <div>
                                    <span className="server-config__tools--table-td-type" />
                                    {record.type}
                                  </div>
                                  <div className="server-config__tools--table-td-description">
                                    {record.description}
                                  </div>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </Collapse.Panel>
            );
          })}
        </Collapse>
      </div>
    );
  };

  useEffect(() => {
    if (!activeServer) {
      form.setFieldsValue({
        serverType: 'stdio',
      });
      // 初始化JSON配置为空
      setJsonConfig('{\n  "server": {\n    "command": "",\n    "args": [],\n    "env": {}\n  }\n}');
      return;
    };
    const config: StdioServerConfig | SseServerConfig | StreamableHttpServerConfig = JSON.parse(
      activeServer?.serverConfig || '{}',
    );
    form.setFieldsValue({
      serverName: activeServer.serverName,
      serverDesc: activeServer.serverDesc,
      serverType: config.serverType,
      ...config,
      ...('args' in config && { args: config.args?.join('\n') }),
      ...('env' in config && {
        env: Object.entries(config.env || {})
          .map(([key, value]) => `${key}=${value}`)
          .join('\n'),
      }),
      ...('requestHeaders' in config && {
        requestHeaders: Object.entries(config.requestHeaders || {})
          .map(([key, value]) => `${key}=${value}`)
          .join('\n'),
      }),
    });

    // 初始化JSON配置
    const serverName = activeServer.serverName || 'server';
    const mcpConfigWrapper = convertToMcpFormat(activeServer.serverConfig || '{}', serverName);
    setJsonConfig(JSON.stringify(mcpConfigWrapper, null, 2));

    // 重置编辑模式和保存按钮状态
    setEditMode('form');
    setSaveDisabled(true);
  }, [activeServer]);

  useEffect(() => {
    if (!activeServer) return;
    // 当服务器状态改变时，更新enabled状态
    // 但是如果服务器正在loading，则不改变enabled状态
    if (!activeServer.loading) {
      setEnabled(activeServer.serverStatus === 1);
      if (activeServer.serverStatus === 1) {
        getToolList();
      }
    }
  }, [activeServer?.serverStatus, activeServer?.loading]);

  useEffect(() => {
    // 使用自定义类名的子元素来控制resizable
    setTimeout(() => {
      const drawerContent = document.querySelector('.mcp-servers__drawer .ant-drawer-content-wrapper');
      if (!drawerContent) return;

      const interactable = interact(drawerContent as HTMLElement).resizable({
        edges: { left: true },
        listeners: {
          start() {
            setIsResizing(true);
            drawerContent.classList.add('resizing');
          },
          move: (event) => {
            const newWidth = Math.max(380, Math.min(1000, event.rect.width));
            setWidth(newWidth);
          },
          end() {
            setIsResizing(false);
            drawerContent.classList.remove('resizing');
          }
        },
        modifiers: [
          interact.modifiers.restrictSize({
            min: { width: 380, height: 100 },
            max: { width: 1000, height: 2000 }
          })
        ]
      });

      return () => {
        interactable.unset();
      }
    }, 1000);
  }, []);


  return (
    <Drawer
      closeIcon={<ArrowLeftIcon />}
      maskClosable={true}
      title={
        <div className='server-config__header'>
          <div className='server-config__header--left'>
            {activeServer ? (
              <>
                {activeServer.serverLogo ? <img src={activeServer.serverLogo} /> : <div className='server-config__header--icon-wrapper'><CodeOutlined /></div>}
                <span className='server-config__header--title' title={activeServer.serverName}>{activeServer.serverName}</span>
              </>
            ) : (
              '添加MCP服务'
            )}
          </div>
          <div className='server-config__header--right'>
            <span>服务状态</span>
            <Switch checked={enabled} onChange={toggleServer} loading={activeServer?.loading} disabled={!activeServer} />
          </div>
        </div>
      }
      open
      onClose={cancelServerConfig}
      width={width}
      className="mcp-servers__drawer"
      footer={null}
    >
      <div className="server-config">
        <div className="server-config-wrapper">
          {isMarketServer ? <a style={{
            position: 'absolute',
            right: 20,
            top: 10,
            fontSize: 14,
            cursor: 'pointer',
            zIndex: 10
          }} onClick={() => {
              const serverFromObj = JSON.parse(activeServer.serverFromObj || '{}');
              const mcpId = serverFromObj.mcpId;
              const source = serverFromObj.source;
              if (!serverFromObj.mcpId) {
                message.warning('不存在mcpId，无法打开领慧MCP广场详情，请重新添加在使用');
                return;
              }
              api.openAiPlatformUrl(`/project/enter/llm/mcp-square?mcpstp[id]=${mcpId}&mcpstp[title]=${activeServer.serverName}&mcpstp[source]=${source || '1'}&mcpstp[key]=${mcpId}`);
            }}>查看文档</a> : ''}
          <Tabs
            defaultActiveKey="tool"
            items={[
              {
                label: '通用',
                key: 'general',
                children: <>
                  <div style={{ height: 'calc(100vh - 180px)', overflow: 'auto', padding: '0 16px' }}>{generalForm()}</div>
                  <div className='server-config__footer'>
                    {activeServer?.id && activeServer?.isBuiltin !== 1 ? <Button disabled={activeServer?.loading || activeServer?.serverStatus === 1} size='large' type="text" danger icon={<DeleteOutlined />} onClick={deleteServer}>删除</Button> : <div />}
                    <div>
                      <Button onClick={cancelServerConfig} size='large'>取消</Button>
                      <Button type='primary' onClick={handleSave} disabled={saveDisabled || activeServer?.loading} size='large'>保存</Button>
                    </div>
                  </div>
                </>,
              },
              enabled && {
                label: '工具',
                key: 'tool',
                children: toolListComponent(),
              },
            ]}
          />

        </div>
      </div>
    </Drawer>
  );
};

export default ServerConfig;
