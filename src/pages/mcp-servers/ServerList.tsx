import React, { useContext, useEffect, useState, useMemo } from 'react';
import { MCPServersContext } from '@/context';
import { CodeOutlined, PlusOutlined } from '@ant-design/icons';
import { Server } from '@/common/types';
import { Divider, Tag } from 'antd';

const ServerList: React.FC = () => {
  const { setActiveId } = useContext(MCPServersContext);
  const [serverList, setServerList] = useState<Server[]>([]);

  const getServerList = async () => {
    const list = (await window['bdpStudioAPI'].getMcpList()) as any[];
    setServerList(list);
  }

  const { builtinTools, externalTools } = useMemo(() => {
    const builtin = serverList.filter(server => server.isBuiltin === 1);
    const external = serverList.filter(server => server.isBuiltin !== 1);
    return { builtinTools: builtin, externalTools: external };
  }, [serverList]);

  const onAddServer = async () => {
    await window['bdpStudioAPI'].saveMcp({
      serverName: 'MCP 服务器',
      serverConfig: JSON.stringify({
        serverType: 'stdio',
      })
    });
    getServerList();
  }

  const onClickServer = async (server: Server) => {
    setActiveId(server.id);
    // setActivePage('config');
  }

  // useEffect(() => {
  //   if (activePage === 'list') {
  //     getServerList();
  //   }
  // }, [activePage]);

  return <div className='mcp-server-list'>
    <div className='mcp-server-list__header'>MCP 服务器</div>
    <div className='mcp-server-list__content'>
            {/* 内置工具 */}
      {builtinTools.length > 0 && (
        <div className='mcp-server-list__group'>
          <div className='mcp-server-list__group-header'>
            <Tag color='blue'>内置工具</Tag>
          </div>
          <div className='mcp-server-list__group-items'>
            {builtinTools.map((server) => (
              <div key={server.id} onClick={() => onClickServer(server)} className='mcp-server-list__item'>
                <div className='mcp-server-list__item--header'>
                  <CodeOutlined />
                  <div className='mcp-server-list__item--name'>{server.serverName}</div>
                  <div className={`mcp-server-list__item--status ${server.serverStatus === 1 ? 'active' : ''}`}><div /></div>
                </div>
                <div className='mcp-server-list__item--desc'>{server.serverDesc}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 外部工具 */}
      <div className='mcp-server-list__group'>
        <div className='mcp-server-list__group-header'>
          <Tag color='default'>外部工具</Tag>
        </div>
        <div className='mcp-server-list__group-items'>
          <div className='mcp-server-list__item mcp-server-list__add-server' onClick={onAddServer}>
            <PlusOutlined />
            <div className='mcp-server-list__add-server-text'>添加服务器</div>
          </div>
          {externalTools.map((server) => (
            <div key={server.id} onClick={() => onClickServer(server)} className='mcp-server-list__item'>
              <div className='mcp-server-list__item--header'>
                <CodeOutlined />
                <div className='mcp-server-list__item--name'>{server.serverName}</div>
                <div className={`mcp-server-list__item--status ${server.serverStatus === 1 ? 'active' : ''}`}><div /></div>
              </div>
              <div className='mcp-server-list__item--desc'>{server.serverDesc}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>;
}

export default ServerList;
