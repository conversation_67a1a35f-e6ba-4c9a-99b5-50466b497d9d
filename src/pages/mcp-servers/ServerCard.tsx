import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Server } from '@/common/types';
import { Button, message, Switch, Tag } from 'antd';
import { CodeOutlined } from '@ant-design/icons';
import AddIcon from '@/components/Icon/AddIcon';
import { MCPServersContext } from '@/context/MCPServersContext';
import { useMemoizedFn } from 'ahooks';
import { PageContext } from '@/context';

interface ServerCardProps {
  isMarketServer?: boolean;
  isAdded?: boolean;
  server: Server;
  updateServerCallback?: (keyword?: string) => void;
  onClickCard?: (server: Server) => void;
}

const ServerCard: React.FC<ServerCardProps> = ({ isMarketServer = false, isAdded = false, server, onClickCard, updateServerCallback }) => {
  const { serverList } = useContext(PageContext);
  const { activeId, setActiveId, startingServersLength, setStartingServersLength, updateServerLoading } = useContext(MCPServersContext);
  const { sensorsFunc } = useContext(PageContext);

  const handleSwitch = useMemoizedFn(async (id: number, checked: boolean) => {
    let res: {
      success: boolean;
      error: string | null;
    } | boolean | string | null;

    if (checked) {
      sensorsFunc('AI_PLAT_start_server', { server: JSON.stringify(server) });
      const enabledServers = serverList.filter(server => server.serverStatus === 1);
      const maxEnableLength = enabledServers.length + startingServersLength;
      if (maxEnableLength >= 10) {
        message.warning('最多启动10个服务');
        return;
      }
      updateServerLoading(id, true);
      setStartingServersLength((value: number) => value + 1);
      res = await window['bdpStudioAPI'].startMcp(id);
      if (res.success) {
        message.success('启动成功');
        updateServerLoading(id, false);
        setTimeout(() => {
          updateServerCallback && updateServerCallback();
        }, 30);
      } else {
        message.error(res.error || '未知服务异常');
        updateServerLoading(id, false);
      }
      setStartingServersLength((value: number) => value - 1);
    } else {
      sensorsFunc('AI_PLAT_stop_server', { server: JSON.stringify(server) });
      updateServerLoading(id, true);
      res = await window['bdpStudioAPI'].stopMcp(id);
      if (res) {
        message.success('停止成功');
        updateServerLoading(id, false);
        setTimeout(() => {
          updateServerCallback && updateServerCallback();
        }, 30);
      } else {
        message.error('停止失败');
        updateServerLoading(id, false);
      }
    }
  })

  const handleAddMcpServer = async () => {
    sensorsFunc('AI_PLAT_add_server_from_market', { server: JSON.stringify(server) } );
    const res = await window['bdpStudioAPI'].saveMcp({
      serverName: server.serverName,
      serverDesc: server.serverDesc,
      serverConfig: server.serverConfig,
      serverLogo: server.serverLogo,
      serverFromObj: server.serverFromObj,
    });
    if (res.success) {
      message.success('添加成功');
      updateServerCallback && updateServerCallback();
    }
  }

  const cardAction = useMemo(() => {
    if (isMarketServer) {
      if (isAdded) {
        return <div />;
      }
      return <div onClick={(e) => e.stopPropagation()}><Button type='primary' onClick={handleAddMcpServer}><AddIcon />添加</Button></div>;
    }
    return <div onClick={(e) => e.stopPropagation()}><Switch checked={server.serverStatus === 1} onChange={(checked) => handleSwitch(server.id, checked)} loading={server.loading} /></div>;
  }, [isMarketServer, server.serverStatus, server.loading, isAdded]);

  return (
    <div key={server.id} className={`mcp-servers__list--item ${activeId === server.id ? 'active' : ''}`} onClick={() => { onClickCard && onClickCard(server) }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div className='mcp-servers__list--item-info'>
          {server.serverLogo ? <img src={server.serverLogo} alt='icon' /> : <CodeOutlined />}
          <span className='mcp-servers__list--item-server-name' title={server.serverName}>{server.serverName}</span>
          {server.isBuiltin === 1 && <Tag color='blue'>内置</Tag>}
          {(server.serverStatus === 1) && !isMarketServer ? <Tag color='success'>运行中</Tag> : null}
        </div>
        {cardAction}
      </div>
      <div className='mcp-servers__list--item-desc' title={server.serverDesc}>{server.serverDesc}</div>
    </div>
  );
}

export default ServerCard;
