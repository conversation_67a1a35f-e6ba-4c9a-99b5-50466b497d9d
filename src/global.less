#root,
body,
html {
  height: 100%;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Arial', 'sans-serif';
  margin: 0;
}

* {
  box-sizing: border-box;
}

body {
  hr {
    border-color: #0d0d0d0d;
    margin-block: 26px;
    border-width: 0.5px;
  }

  .ant-drawer-header {
    padding: 15px 16px;
    border-bottom: none;
  }

  .ant-drawer-title {
    font-size: 16px;
    color: #1b1c1f;
  }

  .ant-drawer-close {
    margin-right: 0;
    padding-left: 0;
  }

  .ant-drawer-body {
    padding: 16px;
    padding-top: 0;
  }

  .ant-drawer-content {
    border-radius: 12px 0px 0px 12px;
    box-shadow: -4px 0px 24px 0px #dddfe2;
  }

  .ant-pagination-item {
    a {
      color: #333;
    }
  }

  .ant-pagination-item-active {

    &:focus-visible a,
    &:hover a,
    a {
      color: #fff;
    }
  }

  .ant-btn-primary {
    border-radius: 8px;
    background: #3570ff;
    border: 1px solid #3570ff;
  }

  .bdp-input.ant-input[disabled],
  .bdp-input-textarea.ant-input[disabled],
  .bdp-input-search .ant-input[disabled],
  .bdp-input.ant-input-affix-wrapper-disabled,
  .bdp-input-password.ant-input-affix-wrapper-disabled {
    background: #ebedf2;
    border: 1px solid #ebeef9;
  }

  .bdp-input.ant-input,
  .bdp-input-textarea.ant-input,
  .bdp-input-search .ant-input,
  .bdp-input.ant-input-affix-wrapper,
  .bdp-input-password.ant-input-affix-wrapper {
    font-size: 14px;
  }

  .ant-switch,
  .bdp-switch-default.ant-switch {
    min-width: 40px;
    height: 20px;
    background-color: #DFE4F0;
  }

  .ant-switch-checked,
  .bdp-switch-default.ant-switch-checked {
    background-color: #3570FF;
  }

  .ant-switch-checked .ant-switch-handle {
    left: calc(100% - 18px);
  }

  .ant-switch-handle {
    width: 16px;
    height: 16px;
  }

  .ant-form label {
    font-size: 14px;
  }

  .ant-tabs-tab {
    font-size: 14px;
  }
}