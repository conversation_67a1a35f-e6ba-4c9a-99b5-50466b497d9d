import '@bdpfe/components/es/theme/style';
import './global.less';
import { api } from './services/api';
import './iconfont.js';
import './utils/logger';
import { renderApp } from './pages/renderApp';
import { renderLogin } from './pages/renderLogin';


const mountApp = () => {
  return new Promise(async (resolve) => {
    const user = await window['bdpStudioAPI'].getUser();
    if (user && typeof user === 'object' && 'session' in user) {

      api.checkToken(user.session).then(async (res) => {
        if (res) {
          await window['bdpStudioAPI'].init();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    } else {
      resolve(false);
    }
  })
}

mountApp().then((res) => {
  if (res) {
    renderApp();
  } else {
    renderLogin();
  }
});
