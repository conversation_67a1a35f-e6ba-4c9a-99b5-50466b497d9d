import { Provider, User, Message, AttachmentInfo } from './types';
export const api = {
  openExternalUrl: (url: string) => {
    window['bdpStudioAPI'].openExternalUrl(url);
  },
  openAiPlatformUrl: (url: string) => {
    window['bdpStudioAPI'].openAiPlatformUrl(url);
  },
  getUser: () => {
    return window['bdpStudioAPI'].getUser();
  },
  getModelProviderList: () => {
    return window['bdpStudioAPI'].getModelProviderList();
  },
  getEnabledModelList: () => {
    return window['bdpStudioAPI'].getEnabledModelList();
  },
  getModelListByProvider: (provider: string) => {
    return window['bdpStudioAPI'].getModelListByProvider(provider);
  },
  getAllModelList: () => {
    return window['bdpStudioAPI'].getAllModelList();
  },
  enabledModel: (id: number, enabled: boolean) => {
    return window['bdpStudioAPI'].enabledModel(id, enabled);
  },
  setDefaultModel: (id: number) => {
    return window['bdpStudioAPI'].setDefaultModel(id);
  },
  saveProviderStatus: (provider: string, status: number) => {
    return window['bdpStudioAPI'].saveProviderStatus(provider, status);
  },
  saveProviderCurrentAPIKey: (provider: string, currentAPIKey: string) => {
    return window['bdpStudioAPI'].saveProviderCurrentAPIKey(provider, currentAPIKey);
  },
  verifyModelProvider: (provider: string) => {
    return window['bdpStudioAPI'].verifyModelProvider(provider);
  },
  installUv: () => {
    return window['bdpStudioAPI'].installUv();
  },
  installBun: () => {
    return window['bdpStudioAPI'].installBun();
  },
  installNodejs: () => {
    return window['bdpStudioAPI'].installNodejs();
  },
  installMset: () => {
    return window['bdpStudioAPI'].installMset();
  },
  getMsetStatus: () => {
    return window['bdpStudioAPI'].getMsetStatus();
  },
  getMsetPath: () => {
    return window['bdpStudioAPI'].getMsetPath();
  },
  getNodejsPath: () => {
    return window['bdpStudioAPI'].getNodejsPath();
  },
  getNodejsStatus: () => {
    return window['bdpStudioAPI'].getNodejsStatus();
  },
  getUvStatus: () => {
    return window['bdpStudioAPI'].getUvStatus();
  },
  getBunStatus: () => {
    return window['bdpStudioAPI'].getBunStatus();
  },
  getUvPath: () => {
    return window['bdpStudioAPI'].getUvPath();
  },
  getBunPath: () => {
    return window['bdpStudioAPI'].getBunPath();
  },
  getMcpList: () => {
    return window['bdpStudioAPI'].getMcpList();
  },
  saveMcp: ({
    id,
    serverConfig,
    serverDesc,
    serverName,
  }: {
    id?: number;
    serverConfig?: string;
    serverDesc?: string;
    serverName?: string;
  }) => {
    return window['bdpStudioAPI'].saveMcp({
      id,
      serverConfig,
      serverDesc,
      serverName,
    });
  },
  deleteMcp: (id: number) => {
    return window['bdpStudioAPI'].deleteMcp(id);
  },
  startMcp: (id: number) => {
    return window['bdpStudioAPI'].startMcp(id);
  },
  stopMcp: (id: number) => {
    return window['bdpStudioAPI'].stopMcp(id);
  },
  getHomePath: () => {
    return window['bdpStudioAPI'].getHomePath();
  },
  getProcessPath: () => {
    return window['bdpStudioAPI'].getProcessPath();
  },
  getUserDataPath: () => {
    return window['bdpStudioAPI'].getUserDataPath();
  },
  getToolList: () => {
    return window['bdpStudioAPI'].getToolList();
  },
  enabledTool: (id: number, toolName: string) => {
    return window['bdpStudioAPI'].enabledTool(id, toolName);
  },
  disabledTool: (id: number, toolName: string) => {
    return window['bdpStudioAPI'].disabledTool(id, toolName);
  },
  chat: async (
    prompt: string,
    sessionId: number,
    mcpIds: string,
    answerMessageId?: number,
    questionMessageId?: number,
    attachments?: AttachmentInfo[],
    agentMode?: boolean
  ): Promise<Message> => {
    const res = await window['bdpStudioAPI'].chat(prompt, sessionId, mcpIds, answerMessageId, questionMessageId, attachments, agentMode);
    return {
      id: res.id,
      message: res.message,
      createdAt: res.createdAt,
      updatedAt: res.updatedAt,
      sessionId: Number(res.sessionId),
      role: res.role,
    };
  },
  getMessageList: (sessionId: number) => {
    return window['bdpStudioAPI'].getMessageList(sessionId);
  },
  createSession: (userId: string, provider?: string) => {
    return window['bdpStudioAPI'].createSession(userId, provider);
  },
  getSessionList: () => {
    return window['bdpStudioAPI'].getSessionList();
  },
  deleteSession: (id: number) => {
    return window['bdpStudioAPI'].deleteSession(id);
  },

  deleteAllMessages: (sessionId: number) => {
    return window['bdpStudioAPI'].deleteAllMessages(sessionId);
  },

  updateSession: (id: number, updates: Partial<{
    name: string;
    provider: string;
    model: string;
    temperature: number;
    top_p: number;
    max_tokens: number;
    system_prompt: string;
    top_k: number;
    repetition_penalty: number;
    seed: number;
  }>) => {
    return window['bdpStudioAPI'].updateSession(id, updates);
  },
  onMessage: (key: string, callback: (event: any, data: any) => void) => {
    return window['bdpStudioAPI'].onMessage(key, callback);
  },
  onClearContext: (key: string, callback: (event: any, data: any) => void) => {
    return window['bdpStudioAPI'].onClearContext(key, callback);
  },
  removeMessageListener: (key: string) => {
    return window['bdpStudioAPI'].removeMessageListener(key);
  },
  removeClearContextListener: (key: string) => {
    return window['bdpStudioAPI'].removeClearContextListener(key);
  },
  deleteMessage: (id: number) => {
    return window['bdpStudioAPI'].deleteMessage(id);
  },
  clearContext: (sessionId: number) => {
    return window['bdpStudioAPI'].clearContext(sessionId);
  },
  sendInterruptStream: (sessionId: number) => {
    return window['bdpStudioAPI'].sendInterruptStream(sessionId);
  },
  onInterruptStream: (key: string, callback: (event: any, data: any) => void) => {
    return window['bdpStudioAPI'].onInterruptStream(key, callback);
  },
  removeInterruptStreamListener: (key: string) => {
    return window['bdpStudioAPI'].removeInterruptStreamListener(key);
  },
  getMcpMarketList: (pageNum: number, pageSize: number, name: string) => {
    return window['bdpStudioAPI'].getMcpMarketList(pageNum, pageSize, name);
  },
  resetDefaultBaseModelUrl: (provider: string) => {
    return window['bdpStudioAPI'].resetDefaultBaseModelUrl(provider);
  },
  updateProvider: (provider: string, updates: Partial<Provider>) => {
    return window['bdpStudioAPI'].updateProvider(provider, updates);
  },
  getQrCode: () => {
    return window['bdpStudioAPI'].getQrCode();
  },
  getQrCodeStatus: () => {
    return window['bdpStudioAPI'].getQrCodeStatus();
  },
  checkToken: (token: string) => {
    return window['bdpStudioAPI'].checkToken(token);
  },
  saveUser: (user: User) => {
    return window['bdpStudioAPI'].saveUser(user);
  },
  init: () => {
    return window['bdpStudioAPI'].init();
  },
  logout: () => {
    return window['bdpStudioAPI'].logout();
  },
  onLogout: (key: string, callback: (event: any, data: any) => void) => {
    return window['bdpStudioAPI'].onLogout(key, callback);
  },
  offLogout: (key: string) => {
    return window['bdpStudioAPI'].offLogout(key);
  },
  checkLatestVersion: () => {
    return window['bdpStudioAPI'].checkLatestVersion();
  },
  getAppVersion: () => {
    return window['bdpStudioAPI'].getAppVersion();
  },
  getBaseServerUrl: () => {
    return window['bdpStudioAPI'].getBaseServerUrl();
  },

  getUpdateServerUrl: () => {
    return window['bdpStudioAPI'].getUpdateServerUrl();
  },

  // 更新器相关 API
  updater: {
    // 检查更新
    checkForUpdates: () => {
      return window['bdpStudioAPI'].updater.checkForUpdates();
    },
    // 下载更新
    downloadUpdate: () => {
      return window['bdpStudioAPI'].updater.downloadUpdate();
    },
    // 退出并安装更新
    quitAndInstall: () => {
      return window['bdpStudioAPI'].updater.quitAndInstall();
    },
    // 监听事件
    onCheckingForUpdate: (callback: () => void) => {
      return window['bdpStudioAPI'].updater.onCheckingForUpdate(callback);
    },
    onUpdateAvailable: (callback: (info: any) => void) => {
      return window['bdpStudioAPI'].updater.onUpdateAvailable(callback);
    },
    onUpdateNotAvailable: (callback: (info: any) => void) => {
      return window['bdpStudioAPI'].updater.onUpdateNotAvailable(callback);
    },
    onUpdateDownloaded: (callback: (info: any) => void) => {
      return window['bdpStudioAPI'].updater.onUpdateDownloaded(callback);
    },
    onDownloadProgress: (callback: (progress: any) => void) => {
      return window['bdpStudioAPI'].updater.onDownloadProgress(callback);
    },
    onError: (callback: (error: string) => void) => {
      return window['bdpStudioAPI'].updater.onError(callback);
    },
    onManualCheckNoUpdate: (callback: () => void) => {
      return window['bdpStudioAPI'].updater.onManualCheckNoUpdate(callback);
    },
    onManualCheckError: (callback: (error: string) => void) => {
      return window['bdpStudioAPI'].updater.onManualCheckError(callback);
    },
    // 移除所有监听器
    removeAllListeners: () => {
      return window['bdpStudioAPI'].updater.removeAllListeners();
    }
  },
  summarizeMessageTitle: (sessionId: number, provider: string, model: string) => {
    return window['bdpStudioAPI'].summarizeMessageTitle(sessionId, provider, model);
  },
  getDefaultApiKey: () => {
    return window['bdpStudioAPI'].getDefaultApiKey();
  },
  // 附件相关API
  uploadAttachment: () => {
    return window['bdpStudioAPI'].uploadAttachment();
  },

  uploadAttachmentFromClipboard: (fileData: { buffer: number[], originalName: string, mimeType: string, size: number }) => {
    return window['bdpStudioAPI'].uploadAttachmentFromClipboard(fileData);
  },
  getAttachmentsDirectory: () => {
    return window['bdpStudioAPI'].getAttachmentsDirectory();
  },
  deleteAttachment: (fileName: string) => {
    return window['bdpStudioAPI'].deleteAttachment(fileName);
  },
  readAttachment: (fileName: string) => {
    return window['bdpStudioAPI'].readAttachment(fileName);
  },
  parseDocument: (fileName: string) => {
    return window['bdpStudioAPI'].parseDocument(fileName);
  }
}
