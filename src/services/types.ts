export interface Provider {
  id: number;
  provider: string;
  name: string;
  apiBaseUrl: string;
  defaultApiKey: string;
  defaultBaseModelUrl: string;
  currentAPIKey: string;
}
export interface Session {
  id: number;
  name: string;
  provider: string;
  model: string;
  createdAt: string;
  updatedAt: string;
  max_tokens: number;
  temperature: number;
  top_p: number;
  system_prompt: string;
  top_k: number;
  repetition_penalty: number;
  seed: number;
}
export interface ToolCall {
  id: string;
  index: number;
  function: {
    name: string;
    arguments: string;
  };
}

export interface Model {
  id: number;
  model: string;
  provider: string;
  logo: string;
  modelTag: 'reasoning' | 'function_call' | 'vision';
}

export interface Message {
  id: number;
  message: string;
  createdAt: string;
  updatedAt: string;
  sessionId: number;
  role: string;
  provider?: string;
  model?: string;
  avatar?: string;
  attachments?: string; // JSON字符串，存储AttachmentInfo[]
}
export interface Server {
  serverName: string;
  serverConfig: string;
  serverDesc: string;
  serverStatus: number;
  createdAt: string;
  id: number;
}

export interface User {
  userId: string;
  userName: string;
  session: string;
  id: number;
}

export interface AttachmentInfo {
  id: string;           // UUID
  originalName: string; // 原始文件名
  fileName: string;     // 存储文件名(UUID + 扩展名)
  path: string;         // 完整路径
  size: number;         // 文件大小
  extension: string;    // 文件扩展名
  uploadTime: string;   // 上传时间
}