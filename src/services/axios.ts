import axios from 'axios';
import { message } from 'antd';

axios.defaults.timeout = 60000;
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
// const baseUrl = 'mapUrl' || '/inc-bdp-dqc-controller';

axios.interceptors.request.use(
  (config) => {
    return config;
  },
  (err) => Promise.reject(err),
);
axios.interceptors.response.use(
  (res) => {
    // cas 跳转登录
    if (res && res.data) {
      // 下载文件 返回数据流
      if (res.headers['content-type'].indexOf('multipart/form-data') !== -1) return res.data;
      const { ok, success, status, data, errMsg, msg, message: msge } = res.data;
      // status 302 是未登录 跳转到登陆页面
      if (status === 302) {
        window.location.href = `/portal-web-system/usermanage/system?url=${window.location.href
          .replace(/\//g, '@')
          .replace(/#/g, '*')}`;
        return null;
      }
      if (ok || success) {
        // 兼容 data 为 null 或者 undefined的行为
        return ['', null, undefined].includes(data) ? true : data;
      }
      if (errMsg || msg || msge) {
        message.error(errMsg || msg || msge);
        return null;
      }
      message.error('网络异常，请联系管理员！');
      return null;
    }
    return res;
  },
  (err) => {
    message.error(err.toString());
    return null;
  },
);
export default axios;
