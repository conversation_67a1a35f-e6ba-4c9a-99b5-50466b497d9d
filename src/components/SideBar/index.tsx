import React from 'react';
import { PageContext } from '@/context';
import classNames from 'classnames';
import { UserOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import { Avatar } from 'antd';
import { Tooltip, Modal } from '@bdpfe/components';
import SessionGreyIcon from '@/assets/image/session-grey.svg';
import SessionLightIcon from '@/assets/image/session-blue.svg';
import MCPGreyIcon from '@/assets/image/mcp-grey.svg';
import MCPLightIcon from '@/assets/image/mcp-blue.svg';
import ModelSettingGreyIcon from '@/assets/image/model-setting-grey.svg';
import ModelSettingLightIcon from '@/assets/image/model-setting-blue.svg';
import QuestionFilledIcon from '@/components/Icon/QuestionFilledIcon';
import settingIcon from '@/assets/image/setting-grey.svg';
import settingLightIcon from '@/assets/image/setting-blue.svg';
import MCPIcon from '@/components/MCPIcon';
import './index.less';
import { api } from '@/services/api';

const SideBar: React.FC = () => {
  const { activePage, setActivePage, sensorsFunc, open, setOpen, hasNewVersion, downloading, downloaded } = React.useContext(PageContext);
  const cls = (page: string) => classNames('bdp-studio__side-bar__item', {
    'bdp-studio__side-bar__item--active': activePage === page
  });


  const handleClick = (page: string, e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    sensorsFunc('AI_PLAT_click_sidebar', { page });
    setActivePage(page);
  }

  const onClickHelpCenter = (e) => {
    e.stopPropagation();
    sensorsFunc('AI_PLAT_click_sidebar', { page: 'HELP_CENTER' })
    // logout();
    window['bdpStudioAPI'].openExternalUrl('https://newkms.sf-express.com/postDetail?id=66048211');
  }

  const onClickLogin = (e) => {
    e.stopPropagation();
    sensorsFunc('AI_PLAT_click_avatar', { page: 'AVATAR' })
    if (!open) {
      setOpen(true);
    }
  }

  // 处理更新相关的点击事件
  const handleUpdateClick = () => {
    if (downloaded) {
      // 已下载完成，提示安装
      Modal.confirm({
        title: '安装更新',
        content: '更新已下载完成，是否退出应用并安装更新？',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          api.updater.quitAndInstall();
        }
      });
    } else if (!downloading) {
      // 未下载且未在下载中，开始下载
      api.updater.downloadUpdate();
    }
  }

  // 获取更新提示文本
  const getUpdateTooltip = () => {
    if (downloaded) return '更新已下载完成，点击安装';
    if (downloading) return '正在更新';
    return '存在新版本，请点击下载';
  }

  // 渲染更新图标
  const renderUpdateIcon = () => {
    if (!hasNewVersion) return null;

    return (
      <Tooltip placement='right' title={getUpdateTooltip()}>
        {downloaded ? (
          <CloudDownloadOutlined
            className='bdp-studio__side-bar__item--icon'
            style={{
              fontSize: '20px',
              color: '#3355ff',
              cursor: 'pointer'
            }}
            onClick={handleUpdateClick}
          />
        ) : (
          <MCPIcon
            type="mcp-web-refresh-grey"
            className='bdp-studio__side-bar__item--icon'
            style={{
              animation: downloading ? 'spin 1s linear infinite' : 'none',
              transformOrigin: 'center',
              cursor: downloading ? 'default' : 'pointer'
            }}
            onClick={handleUpdateClick}
          />
        )}
      </Tooltip>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'space-between', height: '100%' }} >
      <div className='bdp-studio__side-bar'>
        <Avatar className='bdp-studio__side-bar--avatar' icon={<UserOutlined />} onClick={onClickLogin} />
        <div className='bdp-studio__side-bar--items'>
          <div className={cls('CHAT_STUDIO')} onClick={(e) => handleClick('CHAT_STUDIO', e)}>
            <img src={activePage === 'CHAT_STUDIO' ? SessionLightIcon : SessionGreyIcon} />
            <span className='bdp-studio__side-bar__item--text'>会话</span>
          </div>
          <div className={cls('MCP_SERVER')} onClick={(e) => handleClick('MCP_SERVER', e)}>
            <img src={activePage === 'MCP_SERVER' ? MCPLightIcon : MCPGreyIcon} />
            <span className='bdp-studio__side-bar__item--text'>MCP服务</span>
          </div>
          <div className={cls('MODEL_SERVICE')} onClick={(e) => handleClick('MODEL_SERVICE', e)}>
            <img src={activePage === 'MODEL_SERVICE' ? ModelSettingLightIcon : ModelSettingGreyIcon} />
            <span className='bdp-studio__side-bar__item--text'>模型管理</span>
          </div>
        </div>
      </div>
      <div className='bdp-studio__side-bar--footer'>
        {renderUpdateIcon()}
        <div className={cls('SETTING')} onClick={(e) => handleClick('SETTING', e)}>
          <img src={activePage === 'SETTING' ? settingLightIcon : settingIcon} />
        </div>
        <Tooltip title='帮助中心'>
          <div className='help-center-icon bdp-studio__side-bar__item' onClick={onClickHelpCenter}>
            <QuestionFilledIcon />
          </div>
        </Tooltip>
      </div>
    </div>
  )
};

export default SideBar;
