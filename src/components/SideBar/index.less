.bdp-studio__side-bar {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  width: 100%;
  background: #e9ebf2;
}

.bdp-studio__side-bar--avatar {
  cursor: pointer;
  background-color: #3355ff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-app-region: none;
}

.bdp-studio__side-bar--items {
  margin-top: 0px;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 24px;
}

.bdp-studio__side-bar__item {
  padding: 5px;
  padding-bottom: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 6px;
  -webkit-app-region: none;
  cursor: pointer;
  color: #797c8e;

  &--icon {
    -webkit-app-region: none;
  }

  .anticon>svg {
    font-size: 20px;
    color: #666666;
  }

  // &:hover {
  //   // background: #ebf1ff;

  //   .anticon>svg,
  //   .bdp-studio__side-bar__item--text {
  //     color: #5c7cff;
  //   }
  // }
}

.bdp-studio__side-bar__item--text {
  font-size: 12px;
  margin-top: 2px;
  color: #797c8e;
}

.bdp-studio__side-bar__item--active {

  .anticon>svg,
  .bdp-studio__side-bar__item--text {
    color: #3355ff;
  }
}

.help-center-icon {
  svg {
    color: #797c8e;

    &:active {
      color: #3570ff;
    }
  }
}

.bdp-studio__side-bar--footer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}