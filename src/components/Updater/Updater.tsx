import React, { useState, useEffect, useCallback } from 'react';
import { Button, Progress, Modal, notification, Space, Typography, Alert } from 'antd';
import {
  DownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons';
import { api } from '../../services/api';

const { Text, Title } = Typography;

interface UpdateInfo {
  version: string;
  files?: any[];
  path?: string;
  sha512?: string;
  releaseDate?: string;
}

interface ProgressInfo {
  total: number;
  delta: number;
  transferred: number;
  percent: number;
  bytesPerSecond: number;
}

enum UpdateStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  UPDATE_AVAILABLE = 'update_available',
  NO_UPDATE = 'no_update',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error'
}

interface UpdaterProps {
  autoCheck?: boolean; // 是否自动检查更新
  className?: string;
}

const Updater: React.FC<UpdaterProps> = ({ autoCheck = false, className }) => {
  const [status, setStatus] = useState<UpdateStatus>(UpdateStatus.IDLE);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [downloadProgress, setDownloadProgress] = useState<ProgressInfo | null>(null);
  const [error, setError] = useState<string>('');
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 重置状态
  const resetState = useCallback(() => {
    setStatus(UpdateStatus.IDLE);
    setUpdateInfo(null);
    setDownloadProgress(null);
    setError('');
  }, []);

  // 检查更新
  const checkForUpdates = useCallback(async () => {
    try {
      resetState();
      setStatus(UpdateStatus.CHECKING);
      await api.updater.checkForUpdates();
    } catch (err) {
      setStatus(UpdateStatus.ERROR);
      setError(err instanceof Error ? err.message : '检查更新失败');
      notification.error({
        message: '更新检查失败',
        description: err instanceof Error ? err.message : '检查更新时发生错误',
      });
    }
  }, [resetState]);

  // 下载更新
  const downloadUpdate = useCallback(async () => {
    try {
      setStatus(UpdateStatus.DOWNLOADING);
      await api.updater.downloadUpdate();
    } catch (err) {
      setStatus(UpdateStatus.ERROR);
      setError(err instanceof Error ? err.message : '下载更新失败');
      notification.error({
        message: '下载失败',
        description: err instanceof Error ? err.message : '下载更新时发生错误',
      });
    }
  }, []);

  // 安装更新
  const installUpdate = useCallback(() => {
    Modal.confirm({
      title: '确认安装更新',
      content: '应用将会关闭并安装更新，安装完成后会自动重新启动。确定要继续吗？',
      okText: '立即安装',
      cancelText: '取消',
      onOk: () => {
        api.updater.quitAndInstall();
      },
    });
  }, []);

  // 显示更新详情
  const showUpdateModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  // 设置事件监听器
  useEffect(() => {
    // 检查更新中
    api.updater.onCheckingForUpdate(() => {
      setStatus(UpdateStatus.CHECKING);
    });

    // 发现可用更新
    api.updater.onUpdateAvailable((info: UpdateInfo) => {
      setStatus(UpdateStatus.UPDATE_AVAILABLE);
      setUpdateInfo(info);
      notification.info({
        message: '发现新版本',
        description: `版本 ${info.version} 可用，点击下载更新`,
        duration: 0,
        btn: (
          <Button type="primary" size="small" onClick={downloadUpdate}>
            立即下载
          </Button>
        ),
      });
    });

    // 没有可用更新
    api.updater.onUpdateNotAvailable((info: UpdateInfo) => {
      setStatus(UpdateStatus.NO_UPDATE);
      setUpdateInfo(info);
      notification.success({
        message: '已是最新版本',
        description: `当前版本 ${info.version} 已是最新版本`,
      });
    });

    // 下载进度
    api.updater.onDownloadProgress((progress: ProgressInfo) => {
      setDownloadProgress(progress);
    });

    // 下载完成
    api.updater.onUpdateDownloaded((info: UpdateInfo) => {
      setStatus(UpdateStatus.DOWNLOADED);
      setUpdateInfo(info);
      setDownloadProgress(null);
      notification.success({
        message: '更新下载完成',
        description: '新版本已下载完成，点击立即安装',
        duration: 0,
        btn: (
          <Button type="primary" size="small" onClick={installUpdate}>
            立即安装
          </Button>
        ),
      });
    });

    // 更新错误
    api.updater.onError((errorMsg: string) => {
      setStatus(UpdateStatus.ERROR);
      setError(errorMsg);
      notification.error({
        message: '更新错误',
        description: errorMsg,
      });
    });

    // 手动检查无更新
    api.updater.onManualCheckNoUpdate(() => {
      setStatus(UpdateStatus.NO_UPDATE);
      notification.info({
        message: '已是最新版本',
        description: '当前已是最新版本，无需更新',
      });
    });

    // 手动检查错误
    api.updater.onManualCheckError((errorMsg: string) => {
      setStatus(UpdateStatus.ERROR);
      setError(errorMsg);
      notification.error({
        message: '检查更新失败',
        description: errorMsg,
      });
    });

    // 自动检查更新
    if (autoCheck) {
      const timer = setTimeout(() => {
        checkForUpdates();
      }, 3000); // 3秒后自动检查

      return () => {
        clearTimeout(timer);
        api.updater.removeAllListeners();
      };
    }

    // 组件卸载时移除监听器
    return () => {
      api.updater.removeAllListeners();
    };
  }, [autoCheck, checkForUpdates, downloadUpdate, installUpdate]);

  // 获取状态描述
  const getStatusText = () => {
    switch (status) {
      case UpdateStatus.CHECKING:
        return '正在检查更新...';
      case UpdateStatus.UPDATE_AVAILABLE:
        return `发现新版本 ${updateInfo?.version}`;
      case UpdateStatus.NO_UPDATE:
        return '已是最新版本';
      case UpdateStatus.DOWNLOADING:
        return '正在下载更新...';
      case UpdateStatus.DOWNLOADED:
        return '更新已下载完成';
      case UpdateStatus.ERROR:
        return '更新出错';
      default:
        return '点击检查更新';
    }
  };

  // 获取按钮配置
  const getButtonConfig = () => {
    switch (status) {
      case UpdateStatus.CHECKING:
        return {
          text: '检查中...',
          loading: true,
          disabled: true,
          icon: <ReloadOutlined />,
        };
      case UpdateStatus.UPDATE_AVAILABLE:
        return {
          text: '下载更新',
          loading: false,
          disabled: false,
          icon: <DownloadOutlined />,
          onClick: downloadUpdate,
        };
      case UpdateStatus.DOWNLOADING:
        return {
          text: '下载中...',
          loading: true,
          disabled: true,
          icon: <CloudDownloadOutlined />,
        };
      case UpdateStatus.DOWNLOADED:
        return {
          text: '立即安装',
          loading: false,
          disabled: false,
          icon: <CheckCircleOutlined />,
          onClick: installUpdate,
          type: 'primary' as const,
        };
      case UpdateStatus.ERROR:
        return {
          text: '重新检查',
          loading: false,
          disabled: false,
          icon: <ExclamationCircleOutlined />,
          onClick: checkForUpdates,
          danger: true,
        };
      default:
        return {
          text: '检查更新',
          loading: false,
          disabled: false,
          icon: <ReloadOutlined />,
          onClick: checkForUpdates,
        };
    }
  };

  const buttonConfig = getButtonConfig();

  return (
    <div className={className}>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 状态显示 */}
        <div>
          <Text>{getStatusText()}</Text>
          {updateInfo && (
            <Button
              type="link"
              size="small"
              onClick={showUpdateModal}
              style={{ padding: 0, height: 'auto' }}
            >
              查看详情
            </Button>
          )}
        </div>

        {/* 下载进度 */}
        {status === UpdateStatus.DOWNLOADING && downloadProgress && (
          <div>
            <Progress
              percent={Math.round(downloadProgress.percent)}
              size="small"
              format={(percent) => `${percent}%`}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              下载速度: {Math.round(downloadProgress.bytesPerSecond / 1024)} KB/s
            </Text>
          </div>
        )}

        {/* 错误提示 */}
        {status === UpdateStatus.ERROR && error && (
          <Alert
            message="更新错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError('')}
          />
        )}

        {/* 操作按钮 */}
        <Button
          {...buttonConfig}
          block
        >
          {buttonConfig.icon}
          {buttonConfig.text}
        </Button>
      </Space>

      {/* 更新详情弹窗 */}
      <Modal
        title="更新详情"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            关闭
          </Button>,
          ...(status === UpdateStatus.UPDATE_AVAILABLE ? [
            <Button key="download" type="primary" onClick={downloadUpdate}>
              下载更新
            </Button>
          ] : []),
          ...(status === UpdateStatus.DOWNLOADED ? [
            <Button key="install" type="primary" onClick={installUpdate}>
              立即安装
            </Button>
          ] : [])
        ]}
      >
        {updateInfo && (
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Title level={5}>版本号</Title>
              <Text>{updateInfo.version}</Text>
            </div>
            {updateInfo.releaseDate && (
              <div>
                <Title level={5}>发布日期</Title>
                <Text>{new Date(updateInfo.releaseDate).toLocaleDateString()}</Text>
              </div>
            )}
            {updateInfo.files && updateInfo.files.length > 0 && (
              <div>
                <Title level={5}>更新文件</Title>
                {updateInfo.files.map((file, index) => (
                  <div key={index}>
                    <Text>{file.url || file.name || `文件 ${index + 1}`}</Text>
                  </div>
                ))}
              </div>
            )}
          </Space>
        )}
      </Modal>
    </div>
  );
};

export default Updater;
