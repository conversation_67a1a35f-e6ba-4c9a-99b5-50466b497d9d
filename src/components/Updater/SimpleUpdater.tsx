import React, { useState, useEffect, useCallback } from 'react';
import { Button, Dropdown, <PERSON>u, Badge, message } from 'antd';
import {
  DownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons';
import { api } from '../../services/api';

interface UpdateInfo {
  version: string;
  files?: any[];
  path?: string;
  sha512?: string;
  releaseDate?: string;
}

interface ProgressInfo {
  total: number;
  delta: number;
  transferred: number;
  percent: number;
  bytesPerSecond: number;
}

enum UpdateStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  UPDATE_AVAILABLE = 'update_available',
  NO_UPDATE = 'no_update',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error'
}

interface SimpleUpdaterProps {
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text';
  showText?: boolean;
  autoCheck?: boolean;
}

const SimpleUpdater: React.FC<SimpleUpdaterProps> = ({
  size = 'middle',
  type = 'default',
  showText = true,
  autoCheck = false
}) => {
  const [status, setStatus] = useState<UpdateStatus>(UpdateStatus.IDLE);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [downloadProgress, setDownloadProgress] = useState<ProgressInfo | null>(null);

  // 检查更新
  const checkForUpdates = useCallback(async () => {
    try {
      setStatus(UpdateStatus.CHECKING);
      await api.updater.checkForUpdates();
    } catch (err) {
      setStatus(UpdateStatus.ERROR);
      message.error('检查更新失败');
    }
  }, []);

  // 下载更新
  const downloadUpdate = useCallback(async () => {
    try {
      setStatus(UpdateStatus.DOWNLOADING);
      await api.updater.downloadUpdate();
    } catch (err) {
      setStatus(UpdateStatus.ERROR);
      message.error('下载更新失败');
    }
  }, []);

  // 安装更新
  const installUpdate = useCallback(() => {
    api.updater.quitAndInstall();
  }, []);

  // 设置事件监听器
  useEffect(() => {
    // 检查更新中
    api.updater.onCheckingForUpdate(() => {
      setStatus(UpdateStatus.CHECKING);
    });

    // 发现可用更新
    api.updater.onUpdateAvailable((info: UpdateInfo) => {
      setStatus(UpdateStatus.UPDATE_AVAILABLE);
      setUpdateInfo(info);
      message.info(`发现新版本 ${info.version}`);
    });

    // 没有可用更新
    api.updater.onUpdateNotAvailable((info: UpdateInfo) => {
      setStatus(UpdateStatus.NO_UPDATE);
      setUpdateInfo(info);
      message.success('已是最新版本');
    });

    // 下载进度
    api.updater.onDownloadProgress((progress: ProgressInfo) => {
      setDownloadProgress(progress);
    });

    // 下载完成
    api.updater.onUpdateDownloaded((info: UpdateInfo) => {
      setStatus(UpdateStatus.DOWNLOADED);
      setUpdateInfo(info);
      setDownloadProgress(null);
      message.success('更新已下载完成，点击安装');
    });

    // 更新错误
    api.updater.onError((errorMsg: string) => {
      setStatus(UpdateStatus.ERROR);
      message.error(`更新错误: ${errorMsg}`);
    });

    // 手动检查无更新
    api.updater.onManualCheckNoUpdate(() => {
      setStatus(UpdateStatus.NO_UPDATE);
      message.info('已是最新版本');
    });

    // 手动检查错误
    api.updater.onManualCheckError((errorMsg: string) => {
      setStatus(UpdateStatus.ERROR);
      message.error(`检查更新失败: ${errorMsg}`);
    });

    // 自动检查更新
    if (autoCheck) {
      const timer = setTimeout(() => {
        checkForUpdates();
      }, 5000); // 5秒后自动检查

      return () => {
        clearTimeout(timer);
        api.updater.removeAllListeners();
      };
    }

    // 组件卸载时移除监听器
    return () => {
      api.updater.removeAllListeners();
    };
  }, [autoCheck, checkForUpdates]);

  // 获取按钮图标
  const getIcon = () => {
    switch (status) {
      case UpdateStatus.CHECKING:
        return <ReloadOutlined spin />;
      case UpdateStatus.UPDATE_AVAILABLE:
        return <DownloadOutlined />;
      case UpdateStatus.DOWNLOADING:
        return <CloudDownloadOutlined />;
      case UpdateStatus.DOWNLOADED:
        return <CheckCircleOutlined />;
      case UpdateStatus.ERROR:
        return <ExclamationCircleOutlined />;
      default:
        return <ReloadOutlined />;
    }
  };

  // 获取按钮文本
  const getText = () => {
    if (!showText) return '';

    switch (status) {
      case UpdateStatus.CHECKING:
        return '检查中...';
      case UpdateStatus.UPDATE_AVAILABLE:
        return '有更新';
      case UpdateStatus.DOWNLOADING:
        return `下载中 ${downloadProgress ? Math.round(downloadProgress.percent) : 0}%`;
      case UpdateStatus.DOWNLOADED:
        return '立即安装';
      case UpdateStatus.ERROR:
        return '更新出错';
      case UpdateStatus.NO_UPDATE:
        return '已最新';
      default:
        return '检查更新';
    }
  };

  // 获取按钮点击事件
  const getClickHandler = () => {
    switch (status) {
      case UpdateStatus.UPDATE_AVAILABLE:
        return downloadUpdate;
      case UpdateStatus.DOWNLOADED:
        return installUpdate;
      case UpdateStatus.ERROR:
        return checkForUpdates;
      default:
        return checkForUpdates;
    }
  };

  // 是否显示红点提示
  const showBadge = status === UpdateStatus.UPDATE_AVAILABLE || status === UpdateStatus.DOWNLOADED;

  // 生成下拉菜单
  const menu = (
    <Menu>
      <Menu.Item key="check" onClick={checkForUpdates} disabled={status === UpdateStatus.CHECKING}>
        <ReloadOutlined /> 检查更新
      </Menu.Item>
      {status === UpdateStatus.UPDATE_AVAILABLE && (
        <Menu.Item key="download" onClick={downloadUpdate}>
          <DownloadOutlined /> 下载更新 v{updateInfo?.version}
        </Menu.Item>
      )}
      {status === UpdateStatus.DOWNLOADED && (
        <Menu.Item key="install" onClick={installUpdate}>
          <CheckCircleOutlined /> 立即安装
        </Menu.Item>
      )}
      {updateInfo && (
        <Menu.Divider />
      )}
      {updateInfo && (
        <Menu.Item key="info" disabled>
          版本: {updateInfo.version}
          {updateInfo.releaseDate && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              {new Date(updateInfo.releaseDate).toLocaleDateString()}
            </div>
          )}
        </Menu.Item>
      )}
    </Menu>
  );

  const button = (
    <Button
      size={size}
      type={type}
      icon={getIcon()}
      loading={status === UpdateStatus.CHECKING || status === UpdateStatus.DOWNLOADING}
      onClick={getClickHandler()}
      disabled={status === UpdateStatus.CHECKING}
    >
      {getText()}
    </Button>
  );

  return showBadge ? (
    <Dropdown overlay={menu} trigger={['contextMenu']}>
      <Badge dot status="processing">
        {button}
      </Badge>
    </Dropdown>
  ) : (
    <Dropdown overlay={menu} trigger={['contextMenu']}>
      {button}
    </Dropdown>
  );
};

export default SimpleUpdater;
