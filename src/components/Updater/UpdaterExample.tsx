import React from 'react';
import { Card, Space, Divider } from 'antd';
import Updater from './Updater';

const UpdaterExample: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <h2>更新器组件示例</h2>

        {/* 手动检查更新 */}
        <Card title="手动检查更新" size="small">
          <Updater />
        </Card>

        <Divider />

        {/* 自动检查更新 */}
        <Card title="自动检查更新（启动后3秒自动检查）" size="small">
          <Updater autoCheck={true} />
        </Card>

        <Divider />

        {/* 在设置页面中使用 */}
        <Card title="设置页面中的更新检查" size="small">
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <span>应用更新</span>
            <div style={{ flex: 1 }}>
              <Updater />
            </div>
          </div>
        </Card>
      </Space>
    </div>
  );
};

export default UpdaterExample;
