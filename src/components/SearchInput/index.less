.search-input {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  /* 中性色/内容区底色 */
  background: #F6F7FA;
  box-sizing: border-box;
  /* 中性色/边框线 */
  border: 1px solid #EBEEF9;
  height: 28px;
  transition: all 0.3s ease;

  &:hover {
    border: 1px solid #3355FF;
  }

  .ant-input-affix-wrapper {
    padding: 0px;
  }

  &:has(.ant-input:focus) {
    border: 1px solid #3355FF;
    background-color: #fff;
  }

  .bdp-input.ant-input-affix-wrapper {
    background: transparent;
  }
}