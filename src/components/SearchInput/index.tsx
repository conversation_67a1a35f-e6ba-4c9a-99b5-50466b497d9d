import React from 'react';
import { Input } from '@bdpfe/components';
// import SearchGreyIcon from '@/assets/image/search-grey.svg';
import Icon from '../MCPIcon';
import './index.less';

interface SearchInputProps {
  placeholder?: string;
  onChange?: (value: string) => void;
  value?: string;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = '搜索话题',
  onChange,
  value,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div className='search-input'>
      <Icon type="mcp-web-search-grey" color='#B8C1D5' />
      <Input
        bordered={false}
        value={value}
        allowClear
        placeholder={placeholder}
        onChange={handleChange}
      />
    </div>
  );
};

export default SearchInput;
