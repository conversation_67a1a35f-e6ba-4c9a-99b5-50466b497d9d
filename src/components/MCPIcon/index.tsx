import React, { CSSProperties } from 'react';
import './index.less';

interface IconProps {
  type: string;
  color?: string;
  className?: string;
  style?: CSSProperties;
  size?: string;
  [key: string]: any;
}
const Icon: React.FC<IconProps> = function ({
  type,
  color,
  style,
  className = '',
  size = 'default',
  ...restProps
}) {
  return (
    <svg
      className={`bdpicon bdpicon-${size} ${className}`}
      aria-hidden="true"
      {...restProps}
      style={{ color, ...style }}
    >
      <use xlinkHref={`#${type}`} />
    </svg>
  );
};

export default Icon;
