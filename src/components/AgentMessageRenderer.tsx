import React from 'react';
import { Tag, Card } from '@bdpfe/components';
import { Progress, Timeline } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, SyncOutlined, CloseCircleOutlined } from '@ant-design/icons';

interface TodoItem {
  id: string;
  content: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  agent_type: 'chat' | 'tool';
  tool_name?: string;
}

interface TaskAnalysis {
  userIntent: string;
  complexity: number;
  taskType: string;
}

interface ProgressInfo {
  percentage: number;
  currentStep: number;
  totalSteps: number;
  status: string;
}

interface ParsedAgentMessage {
  isAgentMode: boolean;
  taskAnalysis?: TaskAnalysis;
  todoList?: TodoItem[];
  progress?: ProgressInfo;
  currentStep?: string;
  content: string;
}

interface AgentMessageRendererProps {
  content: string;
  isStreaming?: boolean;
}

/**
 * Parse Agent markdown content into structured data
 */
function parseAgentMessage(markdownContent: string): ParsedAgentMessage {
  const result: ParsedAgentMessage = {
    isAgentMode: false,
    content: markdownContent
  };

  // Check if this is Agent mode content
  if (!markdownContent.includes('## 📋 任务规划')) {
    return result;
  }

  result.isAgentMode = true;

  // Extract task analysis
  const analysisMatch = markdownContent.match(/### 🎯 任务分析\s*\n([\s\S]*?)(?=###|$)/);
  if (analysisMatch) {
    const analysisText = analysisMatch[1];
    const userIntentMatch = analysisText.match(/用户需求：(.+)/);
    const complexityMatch = analysisText.match(/复杂度评估：(⭐+)\s*\((\d+)\/5\)/);
    const taskTypeMatch = analysisText.match(/任务类型：(.+)/);

    if (userIntentMatch || complexityMatch || taskTypeMatch) {
      result.taskAnalysis = {
        userIntent: userIntentMatch?.[1] || '',
        complexity: complexityMatch ? parseInt(complexityMatch[2]) : 0,
        taskType: taskTypeMatch?.[1] || ''
      };
    }
  }

  // Extract todo list
  const planMatch = markdownContent.match(/### 📝 执行计划\s*\n([\s\S]*?)(?=###|$)/);
  if (planMatch) {
    const planText = planMatch[1];
    const todoItems: TodoItem[] = [];

    const stepRegex = /- \[([ x])\] \*\*步骤(\d+)\*\*:\s*(.+?)\s*`\[([^\]]+)\]`/g;
    let match;

    while ((match = stepRegex.exec(planText)) !== null) {
      const [, checked, stepNum, content, agentInfo] = match;
      const [agentType, toolName] = agentInfo.split(':');

      todoItems.push({
        id: `step_${stepNum}`,
        content: content.trim(),
        status: checked === 'x' ? 'completed' : 'pending',
        agent_type: agentType as 'chat' | 'tool',
        tool_name: toolName
      });
    }

    result.todoList = todoItems;
  }

  // Extract progress
  const progressMatch = markdownContent.match(/### 📊 当前进度\s*\n进度：(\d+)%\s*\((\d+)\/(\d+)\s*已完成\)/);
  if (progressMatch) {
    result.progress = {
      percentage: parseInt(progressMatch[1]),
      currentStep: parseInt(progressMatch[2]),
      totalSteps: parseInt(progressMatch[3]),
      status: 'executing'
    };
  }

  // Extract main content (everything after the plan)
  const contentMatch = markdownContent.match(/---\s*\n\n([\s\S]*)$/);
  if (contentMatch) {
    result.content = contentMatch[1];
  } else {
    result.content = markdownContent;
  }

  return result;
}

/**
 * Get icon for step status
 */
function getStepIcon(status: string) {
  switch (status) {
    case 'completed':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'in_progress':
      return <SyncOutlined spin style={{ color: '#1890ff' }} />;
    case 'failed':
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    default:
      return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
  }
}

/**
 * Get color for step status
 */
function getStepColor(status: string): string {
  switch (status) {
    case 'completed':
      return 'green';
    case 'in_progress':
      return 'blue';
    case 'failed':
      return 'red';
    default:
      return 'gray';
  }
}

/**
 * Agent Message Renderer Component
 */
export const AgentMessageRenderer: React.FC<AgentMessageRendererProps> = ({
  content,
  isStreaming = false
}) => {
  const parsed = parseAgentMessage(content);

  if (!parsed.isAgentMode) {
    // Not an agent message, render as regular markdown
    return (
      <div
        className="message-content"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  return (
    <div className="agent-message-renderer">
      {/* Task Analysis Section */}
      {parsed.taskAnalysis && (
        <Card
          title="🎯 任务分析"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <div style={{ marginBottom: 8 }}>
            <strong>用户需求：</strong>{parsed.taskAnalysis.userIntent}
          </div>
          <div style={{ marginBottom: 8 }}>
            <strong>复杂度：</strong>
            {'⭐'.repeat(parsed.taskAnalysis.complexity)} ({parsed.taskAnalysis.complexity}/5)
          </div>
          <div>
            <strong>任务类型：</strong>{parsed.taskAnalysis.taskType}
          </div>
        </Card>
      )}

      {/* Progress Section */}
      {parsed.progress && (
        <Card
          title="📊 执行进度"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Progress
            percent={parsed.progress.percentage}
            status={isStreaming ? 'active' : 'normal'}
            showInfo={true}
            format={(percent) => `${percent}% (${parsed.progress?.currentStep}/${parsed.progress?.totalSteps})`}
          />
        </Card>
      )}

      {/* Steps Timeline */}
      {parsed.todoList && parsed.todoList.length > 0 && (
        <Card
          title="📝 执行计划"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Timeline>
            {parsed.todoList.map((step, index) => (
              <Timeline.Item
                key={step.id}
                color={getStepColor(step.status)}
                dot={getStepIcon(step.status)}
              >
                <div className="step-content">
                  <div style={{ marginBottom: 4 }}>
                    <span className="step-title">
                      <strong>步骤 {index + 1}:</strong> {step.content}
                    </span>
                  </div>
                  <div>
                    <Tag color={step.agent_type === 'tool' ? 'orange' : 'blue'}>
                      {step.agent_type === 'tool' ? `工具: ${step.tool_name}` : '对话'}
                    </Tag>
                    <Tag color={getStepColor(step.status)}>
                      {step.status === 'completed' ? '已完成' :
                       step.status === 'in_progress' ? '进行中' :
                       step.status === 'failed' ? '失败' : '待执行'}
                    </Tag>
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>
      )}

      {/* Main Content */}
      {parsed.content && (
        <div
          className="agent-main-content"
          dangerouslySetInnerHTML={{ __html: parsed.content }}
        />
      )}
    </div>
  );
};
