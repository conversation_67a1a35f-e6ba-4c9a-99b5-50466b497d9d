import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const EditIcon: React.FC<IconProps> = (props) => {
  const { color } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <defs>
        <clipPath id="master_svg0_19_825">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_19_825)">
        <g>
          <path
            d="M5,3.75L8,3.75Q8.07387,3.75,8.14632,3.735589Q8.21877,3.721178,8.28701,3.69291Q8.355260000000001,3.664641,8.41668,3.623602Q8.478100000000001,3.582563,8.53033,3.53033Q8.58256,3.478097,8.6236,3.416678Q8.66464,3.355258,8.692910000000001,3.287013Q8.72118,3.218767,8.73559,3.146318Q8.75,3.0738685,8.75,3Q8.75,2.9261315,8.73559,2.853682Q8.72118,2.781233,8.692910000000001,2.712987Q8.66464,2.644742,8.6236,2.583322Q8.58256,2.521903,8.53033,2.46967Q8.478100000000001,2.417437,8.41668,2.376398Q8.355260000000001,2.335359,8.28701,2.30709Q8.21877,2.278822,8.14632,2.264411Q8.07387,2.25,8,2.25L5,2.25Q3.860913,2.25,3.0554565,3.0554565Q2.25,3.860913,2.25,5L2.25,11Q2.25,12.13909,3.0554565,12.94454Q3.860913,13.75,5,13.75L11,13.75Q12.13909,13.75,12.94454,12.94454Q13.75,12.13909,13.75,11L13.75,8Q13.75,7.92613,13.7356,7.85368Q13.7212,7.78123,13.6929,7.71299Q13.6646,7.64474,13.6236,7.58332Q13.5826,7.5219,13.5303,7.46967Q13.4781,7.41744,13.4167,7.3764Q13.3553,7.33536,13.287,7.30709Q13.2188,7.27882,13.1463,7.26441Q13.0739,7.25,13,7.25Q12.92613,7.25,12.85368,7.26441Q12.78123,7.27882,12.71299,7.30709Q12.64474,7.33536,12.58332,7.3764Q12.5219,7.41744,12.46967,7.46967Q12.41744,7.5219,12.3764,7.58332Q12.33536,7.64474,12.30709,7.71299Q12.27882,7.78123,12.26441,7.85368Q12.25,7.92613,12.25,8L12.25,11Q12.25,11.51777,11.88388,11.88388Q11.51777,12.25,11,12.25L5,12.25Q4.4822299999999995,12.25,4.1161200000000004,11.88388Q3.75,11.51777,3.75,11L3.75,5Q3.75,4.4822299999999995,4.1161200000000004,4.1161200000000004Q4.4822299999999995,3.75,5,3.75Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M13.53033,3.53033Q13.635819999999999,3.424841,13.692910000000001,3.287013Q13.75,3.149184,13.75,3Q13.75,2.9261315,13.73559,2.853682Q13.72118,2.781233,13.692910000000001,2.712987Q13.66464,2.644742,13.6236,2.583322Q13.58256,2.521903,13.53033,2.46967Q13.478100000000001,2.417437,13.41668,2.376398Q13.355260000000001,2.335359,13.28701,2.30709Q13.21877,2.278822,13.14632,2.264411Q13.07387,2.25,13,2.25Q12.850819999999999,2.25,12.71299,2.30709Q12.57516,2.364181,12.46967,2.46967L12.46924,2.470097L6.970097,7.96924L6.96967,7.96967Q6.864181,8.07516,6.80709,8.21299Q6.75,8.350819999999999,6.75,8.5Q6.75,8.57387,6.764411,8.64632Q6.778822,8.71877,6.80709,8.78701Q6.835359,8.855260000000001,6.876398,8.91668Q6.917437,8.978100000000001,6.96967,9.03033Q7.021903,9.08256,7.083322,9.1236Q7.144742,9.16464,7.212987,9.192910000000001Q7.281233,9.22118,7.353682,9.23559Q7.4261315,9.25,7.5,9.25Q7.649184,9.25,7.787013,9.192910000000001Q7.924841,9.135819999999999,8.03033,9.03033L8.030756,9.0299L13.5299,3.5307560000000002L13.53033,3.53033L13.53033,3.53033Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default EditIcon;
