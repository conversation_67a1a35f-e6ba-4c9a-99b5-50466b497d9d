import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const SearchIcon: React.FC<IconProps> = (props) => {
  const { style, color } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1024 1024"
      version="1.1"
      width="36"
      height="36"
      fill={color || 'currentColor'}
      style={style}
      {...props}
    >
      <path d="M486.4 153.6a332.8 332.8 0 0 1 270.592 526.54l102.81 102.862a51.2 51.2 0 0 1-72.397 72.396L685.26 753.306A332.8 332.8 0 1 1 486.4 153.6z m0 102.4a230.4 230.4 0 1 0 0 460.8 230.4 230.4 0 0 0 0-460.8z"></path>
    </svg>
  );
};

export default SearchIcon;
