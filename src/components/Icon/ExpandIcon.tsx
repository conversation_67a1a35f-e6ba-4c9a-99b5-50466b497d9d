import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const ExpandIcon: React.FC<IconProps> = (props) => {
  const { style, color } = props;
  return (
    <svg
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={color || 'currentColor'}
      style={style}
    >
      <g>
        <g>
          <path
            d="M4.646446704864502,13.146446704864502L9.792896704864502,7.999996704864502L4.646446704864502,2.853553704864502L5.353553704864502,2.146446704864502L10.499996704864502,7.292896704864502Q10.792896704864502,7.585786704864502,10.792896704864502,7.999996704864502Q10.792896704864502,8.4142167048645,10.499996704864502,8.707106704864502L5.353553704864502,13.853546704864502L4.646446704864502,13.146446704864502Z"
            fill="currentColor"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default ExpandIcon;
