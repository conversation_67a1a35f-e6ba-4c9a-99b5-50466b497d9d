import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const CategoryIcon: React.FC<IconProps> = (props) => {
  const { style, color } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1024 1024"
      version="1.1"
      width="36"
      height="36"
      fill={color || 'currentColor'}
      style={style}
    >
      <path d="M240 672a48 48 0 1 1 0 96 48 48 0 0 1 0-96z m0-224a48 48 0 1 1 0 96 48 48 0 0 1 0-96z m0-224a48 48 0 1 1 0 96 48 48 0 0 1 0-96z m544 448a48 48 0 1 1 0 96H400a48 48 0 1 1 0-96h384z m0-224a48 48 0 1 1 0 96H400a48 48 0 0 1 0-96h384z m0-224a48 48 0 1 1 0 96H400a48 48 0 0 1 0-96h384z"></path>
    </svg>
  );
};

export default CategoryIcon;
