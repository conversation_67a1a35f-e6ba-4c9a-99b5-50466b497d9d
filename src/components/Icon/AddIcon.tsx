import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const AddIcon: React.FC<IconProps> = (props) => {
  const { color } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <defs>
        <clipPath id="master_svg0_19_827">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_19_827)">
        <g>
          <path
            d="M3,7.25L13,7.25Q13.0739,7.25,13.1463,7.264411Q13.2188,7.278822,13.287,7.30709Q13.3553,7.335359,13.4167,7.376398Q13.4781,7.417437,13.5303,7.46967Q13.5826,7.521903,13.6236,7.583322Q13.6646,7.644742,13.6929,7.712987Q13.7212,7.781233,13.7356,7.853682Q13.75,7.9261315,13.75,8Q13.75,8.0738685,13.7356,8.146318Q13.7212,8.218767,13.6929,8.287013Q13.6646,8.355258,13.6236,8.416678Q13.5826,8.478097,13.5303,8.53033Q13.4781,8.582563,13.4167,8.623602Q13.3553,8.664641,13.287,8.69291Q13.2188,8.721178,13.1463,8.735589000000001Q13.0739,8.75,13,8.75L3,8.75Q2.9261315,8.75,2.853682,8.735589000000001Q2.781233,8.721178,2.712987,8.69291Q2.644742,8.664641,2.583322,8.623602Q2.521903,8.582563,2.46967,8.53033Q2.417437,8.478097,2.376398,8.416678Q2.335359,8.355258,2.30709,8.287013Q2.278822,8.218767,2.264411,8.146318Q2.25,8.0738685,2.25,8Q2.25,7.9261315,2.264411,7.853682Q2.278822,7.781233,2.30709,7.712987Q2.335359,7.644742,2.376398,7.583322Q2.417437,7.521903,2.46967,7.46967Q2.521903,7.417437,2.583322,7.376398Q2.644742,7.335359,2.712987,7.30709Q2.781233,7.278822,2.853682,7.264411Q2.9261315,7.25,3,7.25Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
        <g transform="matrix(0,1,-1,0,11,-5)">
          <path
            d="M8,2.25L18,2.25Q18.073900000000002,2.25,18.1463,2.264411Q18.2188,2.278822,18.287,2.30709Q18.3553,2.335359,18.4167,2.376398Q18.478099999999998,2.417437,18.5303,2.46967Q18.5826,2.521903,18.6236,2.583322Q18.6646,2.644742,18.6929,2.712987Q18.7212,2.781233,18.735599999999998,2.853682Q18.75,2.9261315,18.75,3Q18.75,3.0738685,18.735599999999998,3.146318Q18.7212,3.218767,18.6929,3.287013Q18.6646,3.355258,18.6236,3.416678Q18.5826,3.478097,18.5303,3.53033Q18.478099999999998,3.582563,18.4167,3.623602Q18.3553,3.664641,18.287,3.69291Q18.2188,3.721178,18.1463,3.735589Q18.073900000000002,3.75,18,3.75L8,3.75Q7.9261315,3.75,7.853682,3.735589Q7.781233,3.721178,7.712987,3.69291Q7.644742,3.664641,7.583322,3.623602Q7.521903,3.582563,7.46967,3.53033Q7.417437,3.478097,7.376398,3.416678Q7.335359,3.355258,7.30709,3.287013Q7.278822,3.218767,7.264411,3.146318Q7.25,3.0738685,7.25,3Q7.25,2.9261315,7.264411,2.853682Q7.278822,2.781233,7.30709,2.712987Q7.335359,2.644742,7.376398,2.583322Q7.417437,2.521903,7.46967,2.46967Q7.521903,2.417437,7.583322,2.376398Q7.644742,2.335359,7.712987,2.30709Q7.781233,2.278822,7.853682,2.264411Q7.9261315,2.25,8,2.25Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default AddIcon;
