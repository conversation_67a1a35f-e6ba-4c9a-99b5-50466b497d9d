import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const ArrowLeftIcon: React.FC<IconProps> = (props) => {
  const { color } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      {...props}
    >
      <defs>
        <clipPath id="master_svg0_14_0775">
          <rect x="16" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g transform="matrix(0,1,-1,0,16,-16)" clipPath="url(#master_svg0_14_0775)">
        <g>
          <path
            d="M20.26965779296875,6.930331525878906Q20.16416879296875,6.824842525878906,20.10707779296875,6.687014525878906Q20.04998779296875,6.549185525878906,20.04998779296875,6.400001525878906Q20.04998779296875,6.326133025878907,20.06439879296875,6.253683525878906Q20.07880979296875,6.1812345258789065,20.10707779296875,6.112988525878906Q20.13534679296875,6.044743525878906,20.176385792968752,5.983323525878906Q20.21742479296875,5.921904525878906,20.26965779296875,5.869671525878906Q20.321890792968752,5.817438525878906,20.38330979296875,5.776399525878906Q20.44472979296875,5.735360525878907,20.51297479296875,5.707091525878906Q20.58122079296875,5.678823525878906,20.65366979296875,5.664412525878906Q20.72611929296875,5.650001525878906,20.79998779296875,5.650001525878906Q20.94917179296875,5.650001525878906,21.08700079296875,5.707091525878906Q21.22482879296875,5.764182525878907,21.33031779296875,5.869671525878906L23.99998779296875,8.539341525878907L26.669227792968748,5.870097525878906L26.66965779296875,5.869671525878906Q26.77514779296875,5.764182525878907,26.91297779296875,5.707091525878906Q27.05080779296875,5.650001525878906,27.19998779296875,5.650001525878906Q27.273857792968748,5.650001525878906,27.34630779296875,5.664412525878906Q27.418757792968748,5.678823525878906,27.48699779296875,5.707091525878906Q27.55524779296875,5.735360525878907,27.616667792968748,5.776399525878906Q27.67808779296875,5.817438525878906,27.730317792968748,5.869671525878906Q27.78254779296875,5.921904525878906,27.823587792968752,5.983323525878906Q27.86462779296875,6.044743525878906,27.89289779296875,6.112988525878906Q27.92116779296875,6.1812345258789065,27.93557779296875,6.253683525878906Q27.94998779296875,6.326133025878907,27.94998779296875,6.400001525878906Q27.94998779296875,6.549185525878906,27.89289779296875,6.687014525878906Q27.83580779296875,6.824842525878906,27.730317792968748,6.930331525878906L27.72988779296875,6.9307575258789065L24.53031779296875,10.130331525878907Q24.42482779296875,10.235821525878906,24.28699779296875,10.292911525878907Q24.14916779296875,10.350001525878906,23.99998779296875,10.350001525878906Q23.85080779296875,10.350001525878906,23.71297779296875,10.292911525878907Q23.57514779296875,10.235821525878906,23.46965779296875,10.130331525878907L20.26965779296875,6.930331525878906L20.26965779296875,6.930331525878906Z"
            fillRule="evenodd"
            fill={color || 'currentColor'}
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default ArrowLeftIcon;
