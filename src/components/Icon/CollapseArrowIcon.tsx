import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

const CollapseArrowIcon: React.FC<IconProps> = (props) => {
  const { color } = props;
  return (
    <svg
      version="1.1"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={color || 'currentColor'}
      {...props}
    >
      <defs>
        <clipPath id="master_svg0_128_15611">
          <rect x="0" y="0" width="16" height="16" rx="0" />
        </clipPath>
      </defs>
      <g clip-path="url(#master_svg0_128_15611)">
        <g>
          <path
            d="M4.26965779296875,5.03033Q4.16416879296875,4.924841,4.10707779296875,4.787013Q4.04998779296875,4.649184,4.04998779296875,4.5Q4.04998779296875,4.4261315,4.06439879296875,4.353682Q4.07880979296875,4.281233,4.10707779296875,4.212987Q4.13534679296875,4.144742,4.17638579296875,4.083322Q4.21742479296875,4.021903,4.26965779296875,3.96967Q4.32189079296875,3.917437,4.38330979296875,3.876398Q4.44472979296875,3.835359,4.51297479296875,3.80709Q4.58122079296875,3.778822,4.65366979296875,3.764411Q4.72611929296875,3.75,4.79998779296875,3.75Q4.94917179296875,3.75,5.08700079296875,3.80709Q5.22482879296875,3.864181,5.33031779296875,3.96967L7.99998779296875,6.63934L10.66922779296875,3.970096L10.66965779296875,3.96967Q10.775147792968749,3.864181,10.91297779296875,3.80709Q11.050807792968751,3.75,11.19998779296875,3.75Q11.27385779296875,3.75,11.34630779296875,3.764411Q11.41875779296875,3.778822,11.48699779296875,3.80709Q11.55524779296875,3.835359,11.61666779296875,3.876398Q11.67808779296875,3.917437,11.73031779296875,3.96967Q11.78254779296875,4.021903,11.82358779296875,4.083322Q11.86462779296875,4.144742,11.89289779296875,4.212987Q11.921167792968749,4.281233,11.93557779296875,4.353682Q11.94998779296875,4.4261315,11.94998779296875,4.5Q11.94998779296875,4.649184,11.89289779296875,4.787013Q11.835807792968751,4.924841,11.73031779296875,5.03033L11.72988779296875,5.030756L8.53031779296875,8.23033Q8.424827792968749,8.33582,8.28699779296875,8.39291Q8.14916779296875,8.45,7.99998779296875,8.45Q7.85080779296875,8.45,7.712977792968751,8.39291Q7.57514779296875,8.33582,7.46965779296875,8.23033L4.26965779296875,5.03033L4.26965779296875,5.03033Z"
            fill-rule="evenodd"
            fill="#797C8E"
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M4.26965779296875,9.53033Q4.16416879296875,9.424841,4.10707779296875,9.287013Q4.04998779296875,9.149184,4.04998779296875,9Q4.04998779296875,8.9261315,4.06439879296875,8.853682Q4.07880979296875,8.781233,4.10707779296875,8.712987Q4.13534679296875,8.644742,4.17638579296875,8.583322Q4.21742479296875,8.521903,4.26965779296875,8.46967Q4.32189079296875,8.417437,4.38330979296875,8.376398Q4.44472979296875,8.335359,4.51297479296875,8.30709Q4.58122079296875,8.278822,4.65366979296875,8.264410999999999Q4.72611929296875,8.25,4.79998779296875,8.25Q4.94917179296875,8.25,5.08700079296875,8.30709Q5.22482879296875,8.364181,5.33031779296875,8.46967L7.99998779296875,11.13934L10.66922779296875,8.470096L10.66965779296875,8.46967Q10.775147792968749,8.364181,10.91297779296875,8.30709Q11.050807792968751,8.25,11.19998779296875,8.25Q11.27385779296875,8.25,11.34630779296875,8.264410999999999Q11.41875779296875,8.278822,11.48699779296875,8.30709Q11.55524779296875,8.335359,11.61666779296875,8.376398Q11.67808779296875,8.417437,11.73031779296875,8.46967Q11.78254779296875,8.521903,11.82358779296875,8.583322Q11.86462779296875,8.644742,11.89289779296875,8.712987Q11.921167792968749,8.781233,11.93557779296875,8.853682Q11.94998779296875,8.9261315,11.94998779296875,9Q11.94998779296875,9.149184,11.89289779296875,9.287013Q11.835807792968751,9.424841,11.73031779296875,9.53033L11.72988779296875,9.530756L8.53031779296875,12.73033Q8.424827792968749,12.83582,8.28699779296875,12.89291Q8.14916779296875,12.95,7.99998779296875,12.95Q7.85080779296875,12.95,7.712977792968751,12.89291Q7.57514779296875,12.83582,7.46965779296875,12.73033L4.26965779296875,9.53033L4.26965779296875,9.53033Z"
            fill-rule="evenodd"
            fill="#797C8E"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
};

export default CollapseArrowIcon;
