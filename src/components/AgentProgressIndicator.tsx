import React from 'react';
import { Card } from '@bdpfe/components';
import { Progress, Typography } from 'antd';
import { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface AgentProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  percentage: number;
  status: 'planning' | 'executing' | 'completed' | 'failed';
  message?: string;
  isVisible?: boolean;
}

/**
 * Agent Progress Indicator Component
 * Shows the current progress of Agent execution
 */
export const AgentProgressIndicator: React.FC<AgentProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  percentage,
  status,
  message,
  isVisible = true
}) => {
  if (!isVisible) {
    return null;
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'planning':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'executing':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'planning':
        return '正在规划任务...';
      case 'executing':
        return `正在执行步骤 ${currentStep}/${totalSteps}`;
      case 'completed':
        return '任务执行完成';
      case 'failed':
        return '任务执行失败';
      default:
        return '';
    }
  };

  const getProgressStatus = (): "success" | "exception" | "active" | "normal" => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'exception';
      case 'executing':
        return 'active';
      default:
        return 'normal';
    }
  };

  return (
    <Card
      size="small"
      style={{
        marginBottom: 16,
        border: '1px solid #d9d9d9',
        borderRadius: 6
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
        {getStatusIcon()}
        <Text strong style={{ marginLeft: 8 }}>
          {getStatusText()}
        </Text>
      </div>

      <Progress
        percent={percentage}
        status={getProgressStatus()}
        showInfo={true}
        format={(percent) => `${percent}%`}
        strokeWidth={8}
      />

      {message && (
        <div style={{ marginTop: 8 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {message}
          </Text>
        </div>
      )}

      <div style={{ marginTop: 8, fontSize: 12, color: '#8c8c8c' }}>
        进度：{currentStep}/{totalSteps} 步骤已完成
      </div>
    </Card>
  );
};
