import React from 'react';
import { Provider, Model } from '@/common/types';

export const ModelServiceListContext = React.createContext<{
  providerList: Provider[];
  setProviderList: (providerList: Provider[]) => void;
  activeProviderId: number | null;
  setActiveProviderId: (activeProviderId: number) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}>({
  providerList: [],
  setProviderList: (providerList: any) => { },
  activeProviderId: null,
  setActiveProviderId: (activeProviderId: number) => { },
  loading: false,
  setLoading: (loading: boolean) => { },
});

