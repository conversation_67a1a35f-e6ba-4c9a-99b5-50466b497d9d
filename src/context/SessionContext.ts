import { createContext } from 'react';
import { Session } from '@/services/types';

export const SessionContext = createContext<{
  currentSession: Session | null;
  setCurrentSession: (session: Session) => void;
  sessionList: Session[];
  setSessionList: (sessionList: Session[]) => void;
  chatSettingVisible: boolean;
  setChatSettingVisible: (chatSettingVisible: boolean) => void;
  handleCreateSession: () => void;
}>({
  currentSession: null,
  setCurrentSession: () => { },
  sessionList: [],
  setSessionList: () => { },
  chatSettingVisible: false,
  setChatSettingVisible: () => { },
  handleCreateSession: () => { },
});
