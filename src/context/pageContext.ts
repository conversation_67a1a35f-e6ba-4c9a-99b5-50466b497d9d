import React from 'react';

export interface ProgressInfo {
  total: number;
  delta: number;
  transferred: number;
  percent: number;
  bytesPerSecond: number;
}

export type NewVersionInfo = {
  version: string;
  url: string;
  content: string;
  isForce: boolean;
} | false;

export const PageContext: React.Context<{
  userInfo: any;
  activePage: string;
  setActivePage: (page: string) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  sensorsFunc: (eventId: string, props?: any) => void;
  serverList: any[];
  setServerList: (servers: any) => void;
  downloadProgress: ProgressInfo;
  downloaded: boolean;
  downloading: boolean;
  hasNewVersion: boolean;
  newVersionInfo: NewVersionInfo;
}> = React.createContext({
  userInfo: null,
  activePage: 'HOME',
  setActivePage: (page: string) => { },
  open: false,
  setOpen: (open: boolean) => { },
  sensorsFunc: (eventId: string, props?: any) => { },
  serverList: [],
  setServerList: (servers: any) => { },
  downloadProgress: null,
  downloaded: false,
  downloading: false,
  hasNewVersion: false,
  newVersionInfo: false,
});

