import React from 'react';
import { Server } from '@/common/types';

export const MCPServersContext = React.createContext<{
  activeId: number | null;
  setActiveId: (activeId: number | null) => void;
  startingServersLength: number;
  setStartingServersLength: (startingServersLength: number | ((prev: number) => number)) => void;
  updateServerLoading: (serverId: number, loading: boolean) => void;
}>({
  activeId: null,
  setActiveId: (activeId: number | null) => { },
  startingServersLength: 0,
  setStartingServersLength: (startingServersLength: number | ((prev: number) => number)) => { },
  updateServerLoading: (serverId: number, loading: boolean) => { },
});
