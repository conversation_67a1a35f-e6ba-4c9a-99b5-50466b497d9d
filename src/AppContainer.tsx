import React, { useEffect } from 'react';
import { ConfigProvider } from '@bdpfe/components';
import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/es/locale/en_US';
import th from 'antd/es/locale/th_TH';
import { api } from '@/services/api';
import { useMcpProtocol } from '@/hooks/useMcpProtocol';

const getCurrentLanguage = () => {
  const currentLang = localStorage.getItem('language') || 'zh-CN';
  if (currentLang === 'zh-CN') {
    return zhCN;
  }
  if (currentLang === 'en-US') {
    return enUS;
  }
  if (currentLang === 'th') {
    return th;
  }
};

const AppConainer: React.FC<{ children: any }> = (props) => {
  const { children } = props;

  // 启用MCP协议监听

  useEffect(() => {
    api.getModelProviderList().then(res => {
      // console.log('res', res);
      if(res.length >0 ) {
        api.getModelListByProvider(res[0].provider).then(res => {
          // console.log('res', res);
        });
      }
    });
  }, []);
  return (
    <ConfigProvider locale={getCurrentLanguage()}>
      {children}
    </ConfigProvider>
  );
};

export default AppConainer;
